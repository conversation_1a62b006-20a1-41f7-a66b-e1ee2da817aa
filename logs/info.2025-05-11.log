[2025-07-12 19:32:32.032] [cn.taken.ad.RtbApplication.logStarting,50] INFO  - Starting RtbApplication on chang with PID 40320 (D:\ideaProjects\ssc\ssp-rtb-service\target\classes started by chang in D:\ideaProjects\ssc)
[2025-07-12 19:32:32.046] [cn.taken.ad.RtbApplication.logStartupProfileInfo,652] INFO  - The following profiles are active: dev
[2025-07-12 19:32:33.640] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize,90] INFO  - Tomcat initialized with port(s): 9090 (http)
[2025-07-12 19:32:33.662] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Initializing ProtocolHandler ["http-nio-9090"]
[2025-07-12 19:32:33.674] [org.apache.catalina.core.StandardService.log,173] INFO  - Starting service [Tomcat]
[2025-07-12 19:32:33.675] [org.apache.catalina.core.StandardEngine.log,173] INFO  - Starting Servlet engine: [Apache Tomcat/9.0.36]
[2025-07-12 19:32:33.777] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log,173] INFO  - Initializing Spring embedded WebApplicationContext
[2025-07-12 19:32:33.778] [org.springframework.web.context.ContextLoader.prepareWebApplicationContext,284] INFO  - Root WebApplicationContext: initialization completed in 1692 ms
[2025-07-12 19:32:34.699] [cn.taken.ad.configuration.server.ServerInfoManager.getNew,56] INFO  - new service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:32:35.871] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Starting ProtocolHandler ["http-nio-9090"]
[2025-07-12 19:32:35.972] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start,202] INFO  - Tomcat started on port(s): 9090 (http) with context path ''
[2025-07-12 19:32:35.976] [cn.taken.ad.RtbApplication.logStarted,59] INFO  - Started RtbApplication in 4.408 seconds (JVM running for 5.398)
[2025-07-12 19:32:35.980] [org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.initialize,171] INFO  - Initializing ExecutorService
[2025-07-12 19:32:35.982] [cn.taken.ad.component.superscheduler.SuperScheduler.start,102] INFO  - super-scheduler starting
[2025-07-12 19:32:35.996] [cn.taken.ad.component.superscheduler.SuperScheduler.start,126] INFO  - super-scheduler started
[2025-07-12 19:32:35.997] [cn.taken.ad.configuration.lb.HSLoadBalanceListener.startLoadBalance,60] INFO  - not need change load balance
[2025-07-12 19:32:43.699] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:32:53.274] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 0bf3acce5838436494ef7cf8bf22445c
[2025-07-12 19:32:53.392] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:32:53.407] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
