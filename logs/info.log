[2025-07-12 19:32:55.164] [cn.taken.ad.BusinessApplication.logStarting,50] INFO  - Starting BusinessApplication on chang with PID 50152 (D:\ideaProjects\ssc\ssp-business-service\target\classes started by chang in D:\ideaProjects\ssc)
[2025-07-12 19:32:55.178] [cn.taken.ad.BusinessApplication.logStartupProfileInfo,652] INFO  - The following profiles are active: dev
[2025-07-12 19:32:56.574] [org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn,127] INFO  - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2025-07-12 19:32:57.172] [org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn,185] INFO  - Finished Spring Data repository scanning in 585ms. Found 0 JPA repository interfaces.
[2025-07-12 19:32:58.077] [cn.taken.ad.configuration.server.ServerInfoManager.getNew,56] INFO  - new service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:32:58.265] [org.hibernate.Version.logVersion,46] INFO  - HHH000412: Hibernate Core {5.3.17.Final}
[2025-07-12 19:32:58.267] [org.hibernate.cfg.Environment.<clinit>,213] INFO  - HHH000206: hibernate.properties not found
[2025-07-12 19:32:58.868] [org.hibernate.annotations.common.Version.<clinit>,49] INFO  - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
[2025-07-12 19:32:59.958] [org.hibernate.dialect.Dialect.<init>,157] INFO  - HHH000400: Using dialect: org.hibernate.dialect.MySQLInnoDBDialect
[2025-07-12 19:33:01.037] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:33:01.037] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:33:01.037] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:33:01.037] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:33:01.037] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:33:03.286] [cn.taken.ad.BusinessApplication.logStarted,59] INFO  - Started BusinessApplication in 8.531 seconds (JVM running for 9.543)
[2025-07-12 19:33:03.292] [org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.initialize,171] INFO  - Initializing ExecutorService
[2025-07-12 19:33:03.295] [cn.taken.ad.component.superscheduler.SuperScheduler.start,102] INFO  - super-scheduler starting
[2025-07-12 19:33:03.308] [org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.initialize,171] INFO  - Initializing ExecutorService
[2025-07-12 19:33:03.317] [cn.taken.ad.component.superscheduler.SuperScheduler.start,126] INFO  - super-scheduler started
[2025-07-12 19:33:04.312] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:33:04.468] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:33:04.557] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:33:04.644] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:33:04.732] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:33:04.822] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:33:04.913] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:33:05.006] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:33:05.100] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:33:05.195] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:33:05.294] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:33:05.386] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:33:05.476] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:33:05.572] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:33:05.666] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:33:05.757] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:33:05.854] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:33:05.940] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:33:05.948] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:33:06.030] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:33:06.118] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:33:06.160] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:33:13.787] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:33:33.347] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:33:36.019] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:33:36.166] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:33:36.280] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:33:36.361] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:33:36.442] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:33:36.524] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:33:36.608] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:33:36.692] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:33:36.775] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:33:36.856] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:33:36.935] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:33:37.018] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:33:37.103] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:33:37.187] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:33:37.271] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:33:37.352] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:33:37.433] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:33:37.513] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:33:37.593] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:33:37.672] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:33:37.752] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:33:37.783] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:33:43.883] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:34:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:34:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:34:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:34:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:34:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:34:03.420] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:34:06.090] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:34:07.789] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:34:07.901] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:34:07.981] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:34:08.063] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:34:08.143] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:34:08.226] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:34:08.307] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:34:08.388] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:34:08.467] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:34:08.550] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:34:08.636] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:34:08.719] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:34:08.803] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:34:08.887] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:34:08.968] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:34:09.049] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:34:09.129] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:34:09.213] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:34:09.295] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:34:09.378] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:34:09.409] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:34:13.982] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:34:20.803] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log,173] INFO  - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-07-12 19:34:20.803] [org.springframework.web.servlet.DispatcherServlet.initServletBean,525] INFO  - Initializing Servlet 'dispatcherServlet'
[2025-07-12 19:34:20.811] [org.springframework.web.servlet.DispatcherServlet.initServletBean,547] INFO  - Completed initialization in 8 ms
[2025-07-12 19:34:33.490] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:34:36.171] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:34:39.424] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:34:39.539] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:34:39.621] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:34:39.703] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:34:39.788] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:34:39.871] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:34:39.953] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:34:40.038] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:34:40.119] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:34:40.198] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:34:40.279] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:34:40.362] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:34:40.446] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:34:40.529] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:34:40.612] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:34:40.694] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:34:40.778] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:34:40.861] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:34:40.943] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:34:41.028] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:34:41.060] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:34:44.076] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:35:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:35:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:35:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:35:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:35:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:35:03.564] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:35:06.263] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:35:10.040] [cn.taken.ad.task.clear.ClearServiceCodeTask.lambda$0,29] INFO  - server code timeout , delete it : BUSINESS__0bf3acce5838436494ef7cf8bf22445c__1752319943786__100.100.30.49__true__0
[2025-07-12 19:35:10.072] [cn.taken.ad.task.clear.ClearServiceCodeTask.lambda$0,29] INFO  - server code timeout , delete it : RTB__3424d20c066340d0970fafea1b9548c3__1752319940003__100.100.30.49__false__0
[2025-07-12 19:35:10.075] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1934:0
[2025-07-12 19:35:10.159] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1934 : 0
[2025-07-12 19:35:11.070] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:35:11.181] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:35:11.262] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:35:11.341] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:35:11.422] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:35:11.503] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:35:11.582] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:35:11.663] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:35:11.745] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:35:11.824] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:35:11.907] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:35:11.990] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:35:12.069] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:35:12.149] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:35:12.232] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:35:12.313] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:35:12.395] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:35:12.476] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:35:12.555] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:35:12.634] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:35:12.666] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:35:14.167] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:35:15.920] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:35:20.036] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1934:0
[2025-07-12 19:35:20.047] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1934:size 0
[2025-07-12 19:35:20.124] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1934:0
[2025-07-12 19:35:20.138] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1934:0
[2025-07-12 19:35:20.139] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1934 : 0
[2025-07-12 19:35:20.140] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1934:0
[2025-07-12 19:35:20.140] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1934:0
[2025-07-12 19:35:20.158] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1934 : size 0
[2025-07-12 19:35:27.992] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:35:36.361] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:35:44.255] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:35:44.941] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:35:44.940] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:35:44.942] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:35:50.611] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.statisticsHour,143] INFO  - statistics dsp hour success ********19
[2025-07-12 19:35:51.386] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:35:51.389] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsHour,101] INFO  - statistics hour success ********19
[2025-07-12 19:35:51.389] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsHour,148] INFO  - statistics dsp hour success ********19
[2025-07-12 19:35:51.390] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:35:51.675] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 19:35:51.675] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsHour,153] INFO  - statistics hour success ********19
[2025-07-12 19:35:51.676] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.statisticsHour,126] INFO  - statistics hour success ********19
[2025-07-12 19:35:51.676] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:35:51.676] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:35:51.677] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsHour,118] INFO  - statistics hour success ********19
[2025-07-12 19:35:51.682] [cn.taken.ad.task.media.MediaPackageTagToDBTask.lambda$exec$1,80] INFO  - save media tag:{"id":21,"name":"句读","code":"c31f9b24332c42b1_tech.caicheng.judourili","mediaId":3,"mediaAppId":8,"type":1,"timeout":700,"width":1080,"height":1920,"bidType":2,"pnyParam":"[]","operatorId":4,"mediaOperatorId":null,"createTime":"2025-03-29 12:00:00","needHttps":0,"materialHttps":false,"flowType":1,"settlementRatio":82.0,"filterOnOff":false,"filterUrlDomain":null,"filterMaterialRatio":0,"filterMaterialType":0,"filterActionType":0,"mediaBaseTagId":21}
[2025-07-12 19:35:52.255] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:35:52.258] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 19:35:52.258] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsHour,148] INFO  - statistics hour success ********19
[2025-07-12 19:35:52.330] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:35:52.330] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:35:52.346] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:35:52.406] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:35:53.802] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:35:54.173] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:35:54.246] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:35:54.327] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:35:54.357] [org.hibernate.hql.internal.QueryTranslatorFactoryInitiator.initiateService,47] INFO  - HHH000397: Using ASTQueryTranslatorFactory
[2025-07-12 19:35:54.399] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:35:54.472] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:35:54.546] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:35:54.616] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:35:54.692] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:35:54.765] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:35:54.837] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:35:55.516] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:35:57.153] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:35:57.183] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:35:57.226] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:35:57.298] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:35:57.333] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:36:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:36:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:36:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:36:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:36:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:36:06.458] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:36:09.264] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:36:10.033] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1935:0
[2025-07-12 19:36:10.035] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1935 : 0
[2025-07-12 19:36:11.348] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:36:14.355] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:36:15.022] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:36:20.041] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1935:0
[2025-07-12 19:36:20.042] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1935:0
[2025-07-12 19:36:20.043] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1935 : 0
[2025-07-12 19:36:20.043] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1935:0
[2025-07-12 19:36:20.044] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1935:0
[2025-07-12 19:36:20.044] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1935 : size 0
[2025-07-12 19:36:20.052] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1935:0
[2025-07-12 19:36:20.159] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1935:size 0
[2025-07-12 19:36:21.344] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:36:27.335] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:36:27.459] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:36:27.541] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:36:27.622] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:36:27.705] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:36:27.788] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:36:27.870] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:36:27.957] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:36:28.038] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:36:28.121] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:36:28.202] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:36:28.282] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:36:28.364] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:36:28.448] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:36:28.530] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:36:28.609] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:36:28.691] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:36:28.774] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:36:28.856] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:36:28.937] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:36:28.979] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:36:33.432] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:36:36.538] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:36:40.157] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:36:40.173] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:36:44.449] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:36:45.113] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:36:45.523] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:36:57.619] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:36:58.986] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:36:59.107] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:36:59.184] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:36:59.261] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:36:59.336] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:36:59.409] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:36:59.483] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:36:59.556] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:36:59.635] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:36:59.710] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:36:59.788] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:36:59.862] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:36:59.939] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:37:00.014] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:37:00.089] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:37:00.165] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:37:00.242] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:37:00.318] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:37:00.393] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:37:00.468] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:37:00.509] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:37:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:37:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:37:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:37:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:37:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:37:06.607] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:37:07.544] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:37:09.710] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:37:10.035] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1936:0
[2025-07-12 19:37:10.035] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1936 : 0
[2025-07-12 19:37:14.546] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:37:15.212] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:37:20.041] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1936 : 0
[2025-07-12 19:37:20.043] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1936 : size 0
[2025-07-12 19:37:20.043] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1936:0
[2025-07-12 19:37:20.044] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1936:size 0
[2025-07-12 19:37:20.052] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1936:0
[2025-07-12 19:37:20.052] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1936:0
[2025-07-12 19:37:20.052] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1936:0
[2025-07-12 19:37:20.145] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1936:0
[2025-07-12 19:37:21.798] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:37:30.519] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:37:30.632] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:37:30.707] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:37:30.780] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:37:30.852] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:37:30.926] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:37:31.002] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:37:31.079] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:37:31.154] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:37:31.230] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:37:31.307] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:37:31.382] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:37:31.456] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:37:31.532] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:37:31.608] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:37:31.683] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:37:31.760] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:37:31.837] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:37:31.914] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:37:31.990] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:37:32.031] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:37:33.908] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:37:36.703] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:37:40.158] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:37:40.171] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:37:44.634] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:37:45.288] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:37:45.997] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:37:58.065] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:38:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:38:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:38:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:38:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:38:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:38:02.680] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:38:03.526] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:38:06.222] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:38:06.296] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:38:06.370] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:38:06.443] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:38:06.515] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:38:06.589] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:38:06.662] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:38:06.734] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:38:06.789] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:38:08.338] [cn.taken.ad.task.media.MediaPackageTagToDBTask.lambda$exec$1,80] INFO  - save media tag:{"id":21,"name":"句读","code":"c31f9b24332c42b1_tech.caicheng.judourili","mediaId":3,"mediaAppId":8,"type":1,"timeout":700,"width":1080,"height":1920,"bidType":2,"pnyParam":"[]","operatorId":4,"mediaOperatorId":null,"createTime":"2025-03-29 12:00:00","needHttps":0,"materialHttps":false,"flowType":1,"settlementRatio":82.0,"filterOnOff":false,"filterUrlDomain":null,"filterMaterialRatio":0,"filterMaterialType":0,"filterActionType":0,"mediaBaseTagId":21}
[2025-07-12 19:38:08.843] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:38:08.917] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:38:08.988] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:38:09.059] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:38:09.131] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:38:09.202] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:38:09.277] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:38:09.350] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:38:11.219] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ************ : 0
[2025-07-12 19:38:11.219] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ************:0
[2025-07-12 19:38:11.219] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:38:11.223] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:38:11.620] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:38:12.035] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:38:14.735] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:38:16.774] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:38:20.039] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ************:size 0
[2025-07-12 19:38:20.040] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ************:0
[2025-07-12 19:38:20.040] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ************:0
[2025-07-12 19:38:20.046] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ************ : size 0
[2025-07-12 19:38:20.046] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ************:0
[2025-07-12 19:38:20.046] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ************ : 0
[2025-07-12 19:38:20.046] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ************:0
[2025-07-12 19:38:20.144] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ************:0
[2025-07-12 19:38:22.360] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:38:23.591] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:38:34.689] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/strategy/main/page] stack[cn.taken.ad.api.oper.strategy.StrategyApi.pageList] time[2025-07-12 19:38:34 to 2025-07-12 19:38:34] params[req={"mediaId":3,"mediaAppId":8,"mediaTagId":null,"advertiserId":null,"advertiserAppId":null,"advertiserTagId":null,"state":null,"start":0,"limit":20} ; ] response[{"success":true,"code":"0","message":"success","result":{"list":[{"mediaName":"Taken","mediaCode":"f3f0876a","appName":"测试安卓","appCode":"9e2db554d9d54715","appType":1,"mediaTagName":"句读","mediaTagCode":"c31f9b24332c42b1_tech.caicheng.judourili","mediaTagBidType":2,"reqTotal":0,"reqInvalidTotal":0,"participatingTotal":0,"winTotal":0,"amount":0.00,"maxTime":null,"minTime":null,"avgTime":null,"eventExposureTotal":0,"eventClickTotal":0,"id":41,"mediaId":3,"mediaAppId":8,"mediaTagId":65,"state":1,"quantityLimitType":1,"parallelMaxAdv":1,"operatorId":4,"createTime":"2025-03-29 12:31:16","isDelete":0},{"mediaName":"Taken","mediaCode":"f3f0876a","appName":"测试安卓","appCode":"9e2db554d9d54715","appType":1,"mediaTagName":"测试-插屏视频","mediaTagCode":"785e1f22eded4be5","mediaTagBidType":2,"reqTotal":0,"reqInvalidTotal":0,"participatingTotal":0,"winTotal":0,"amount":0.00,"maxTime":null,"minTime":null,"avgTime":null,"eventExposureTotal":0,"eventClickTotal":0,"id":26,"mediaId":3,"mediaAppId":8,"mediaTagId":18,"state":0,"quantityLimitType":1,"parallelMaxAdv":1,"operatorId":16,"createTime":"2025-05-22 14:42:14","isDelete":0},{"mediaName":"Taken","mediaCode":"f3f0876a","appName":"测试安卓","appCode":"9e2db554d9d54715","appType":1,"mediaTagName":"测试安卓-安卓-信息流-芒果-竞价","mediaTagCode":"43beea5b","mediaTagBidType":1,"reqTotal":0,"reqInvalidTotal":0,"participatingTotal":0,"winTotal":0,"amount":0.00,"maxTime":null,"minTime":null,"avgTime":null,"eventExposureTotal":0,"eventClickTotal":0,"id":11,"mediaId":3,"mediaAppId":8,"mediaTagId":27,"state":1,"quantityLimitType":1,"parallelMaxAdv":1,"operatorId":1,"createTime":"2025-04-14 16:03:07","isDelete":0},{"mediaName":"Taken","mediaCode":"f3f0876a","appName":"测试安卓","appCode":"9e2db554d9d54715","appType":1,"mediaTagName":"测试安卓-安卓-信息流-测试-分成","mediaTagCode":"c31f9b24332c42b1","mediaTagBidType":2,"reqTotal":0,"reqInvalidTotal":0,"participatingTotal":0,"winTotal":0,"amount":0.00,"maxTime":null,"minTime":null,"avgTime":null,"eventExposureTotal":0,"eventClickTotal":0,"id":4,"mediaId":3,"mediaAppId":8,"mediaTagId":21,"state":1,"quantityLimitType":1,"parallelMaxAdv":1,"operatorId":4,"createTime":"2025-03-29 12:31:16","isDelete":0},{"mediaName":"Taken","mediaCode":"f3f0876a","appName":"测试安卓","appCode":"9e2db554d9d54715","appType":1,"mediaTagName":"测试-开屏-RTB","mediaTagCode":"c0de47a4ed044ac0","mediaTagBidType":1,"reqTotal":3,"reqInvalidTotal":0,"participatingTotal":0,"winTotal":0,"amount":0.00,"maxTime":81463,"minTime":0,"avgTime":37512,"eventExposureTotal":0,"eventClickTotal":0,"id":3,"mediaId":3,"mediaAppId":8,"mediaTagId":20,"state":1,"quantityLimitType":1,"parallelMaxAdv":1,"operatorId":4,"createTime":"2025-03-29 12:30:40","isDelete":0}],"totalCount":5,"start":0,"limit":20,"currentPageNum":1,"totalPage":1}}]  SUCCESS
[2025-07-12 19:38:35.675] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:38:36.875] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:38:40.140] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:38:40.165] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:38:42.036] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:38:42.162] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:38:42.246] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:38:42.326] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:38:42.406] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:38:42.487] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:38:42.566] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:38:42.894] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:1
[2025-07-12 19:38:42.977] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:38:43.059] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:38:43.141] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:38:43.219] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:38:43.525] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:1
[2025-07-12 19:38:43.709] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:1
[2025-07-12 19:38:43.775] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:38:43.846] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:38:43.913] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:38:43.984] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:38:44.050] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:38:44.120] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:38:44.161] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:38:44.826] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:38:46.862] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:38:47.759] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:38:54.780] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/media/tag/list] stack[cn.taken.ad.api.oper.media.MediaTagApi.findList] time[2025-07-12 19:38:54 to 2025-07-12 19:38:54] params[req={"mediaAppId":null,"name":"c31f9b24332c42b1_tech.caicheng.judourili","mediaAppIds":null,"mediaIds":null} ; ] response[{"success":true,"code":"0","message":"success","result":[{"id":65,"name":"句读","code":"c31f9b24332c42b1_tech.caicheng.judourili","mediaId":3,"mediaAppId":8,"type":1,"timeout":700,"width":1080,"height":1920,"bidType":2,"pnyParam":"[]","operatorId":4,"mediaOperatorId":null,"createTime":"2025-03-29 12:00:00","needHttps":0,"materialHttps":false,"flowType":1,"settlementRatio":82.0,"filterOnOff":false,"filterUrlDomain":null,"filterMaterialRatio":0,"filterMaterialType":0,"filterActionType":0,"mediaBaseTagId":21}]}]  SUCCESS
[2025-07-12 19:38:55.916] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/strategy/main/findByTagId] stack[cn.taken.ad.api.oper.strategy.StrategyApi.findByTagId] time[2025-07-12 19:38:55 to 2025-07-12 19:38:55] params[req={"id":65} ; ] response[{"success":true,"code":"0","message":"success","result":{"mediaName":"Taken","mediaCode":"f3f0876a","appName":"测试安卓","appCode":"9e2db554d9d54715","appType":1,"tagName":"句读","tagCode":"c31f9b24332c42b1_tech.caicheng.judourili","tagType":1,"id":41,"mediaId":3,"mediaAppId":8,"mediaTagId":65,"state":1,"quantityLimitType":1,"parallelMaxAdv":1,"operatorId":4,"createTime":"2025-03-29 12:31:16","isDelete":0}}]  SUCCESS
[2025-07-12 19:38:56.395] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/media/tag/info] stack[cn.taken.ad.api.oper.media.MediaTagApi.load] time[2025-07-12 19:38:56 to 2025-07-12 19:38:56] params[req={"id":65} ; ] response[{"success":true,"code":"0","message":"success","result":{"mediaName":"Taken","mediaCode":"f3f0876a","mediaAppName":"测试安卓","mediaAppCode":"9e2db554d9d54715","mediaAppType":1,"protocolName":"TAKEN","protocolCode":"TAKEN","protocolTagParam":"[]","protocolType":1,"reqTotal":null,"reqInvalidTotal":null,"participatingTotal":null,"winTotal":null,"amount":null,"maxTime":null,"minTime":null,"avgTime":null,"eventExposureTotal":null,"eventClickTotal":null,"mediaSettlementRatio":null,"id":65,"name":"句读","code":"c31f9b24332c42b1_tech.caicheng.judourili","mediaId":3,"mediaAppId":8,"type":1,"timeout":700,"width":1080,"height":1920,"bidType":2,"pnyParam":"[]","operatorId":4,"mediaOperatorId":null,"createTime":"2025-03-29 12:00:00","needHttps":0,"materialHttps":false,"flowType":1,"settlementRatio":82.0,"filterOnOff":false,"filterUrlDomain":null,"filterMaterialRatio":0,"filterMaterialType":0,"filterActionType":0,"mediaBaseTagId":21}}]  SUCCESS
[2025-07-12 19:38:56.733] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/advertiser/tag/pageRule] stack[cn.taken.ad.api.oper.advertiser.AdvertiserTagApi.pageRule] time[2025-07-12 19:38:56 to 2025-07-12 19:38:56] params[pageReq={"name":null,"advertiserId":null,"advertiserAppId":null,"settlementType":2,"mediaTagId":65,"tagType":1,"start":0,"limit":10} ; ] response[{"success":true,"code":"0","message":"success","result":{"list":[{"advertiserName":null,"advertiserAppName":null,"advertiserAppCode":null,"advertiserAppType":null,"advertiserProtocolTagParam":null,"directCitySelection":null,"industrySelection":null,"targetDeviceBrandCheck":null,"targetNetworkCheck":null,"targetOperatorCheck":null,"filterCitySelection":null,"filterIndustrySelection":null,"filterDeviceBrandCheck":null,"filterNetworkCheck":null,"filterOperatorCheck":null,"reqTotal":null,"reqSuccessTotal":null,"reqFailTotal":null,"respFailTotal":null,"reqTimeoutTotal":null,"participatingTotal":null,"winTotal":null,"maxTime":null,"minTime":null,"avgTime":null,"amount":null,"eventExposureTotal":null,"eventClickTotal":null,"appendEventTypes":null,"accountName":null,"accountCode":null,"id":77,"advertiserId":48,"advertiserAppId":48,"name":"爱念-爱念App-安卓-信息流-1111-分成","code":"8210","type":1,"timeout":600,"pnyParam":"[]","settlementType":2,"bidPriceType":1,"bidRisesRatio":null,"sharingPriceType":1,"fixedPrice":0.0,"limitRuleOpen":false,"limitType":4,"quota":null,"startTime":null,"endTime":null,"filterExposureNum":null,"filterClickNum":null,"filterDeviceReqNum":null,"filterDeviceExposureNum":null,"directOnOff":false,"targetArea":"[]","targetAppIndustry":"[]","targetAppPackage":null,"targetDeviceModel":null,"targetDeviceBrand":"[]","targetNetwork":"[]","targetOperator":"[]","targetOsVersionType":null,"targetOsVersion":null,"filterOnOff":false,"filterArea":"[]","filterAppIndustry":"[]","filterAppPackage":null,"filterDeviceModel":null,"filterUrlDomain":null,"filterDeviceBrand":"[]","filterNetwork":"[]","filterOperator":"[]","filterEmptyDevice":0,"filterInvalidDevice":0,"filterForeignIp":0,"createTime":"2025-07-08 14:59:16","operatorId":16,"addEventTypes":"[1,2]","dspType":2,"accountId":null,"filterInstalledAppPackage":null,"targetInstalledAppPackage":null,"targetPackId":null,"filterPackId":null,"filterRepeatEvent":0},{"advertiserName":null,"advertiserAppName":null,"advertiserAppCode":null,"advertiserAppType":null,"advertiserProtocolTagParam":null,"directCitySelection":null,"industrySelection":null,"targetDeviceBrandCheck":null,"targetNetworkCheck":null,"targetOperatorCheck":null,"filterCitySelection":null,"filterIndustrySelection":null,"filterDeviceBrandCheck":null,"filterNetworkCheck":null,"filterOperatorCheck":null,"reqTotal":null,"reqSuccessTotal":null,"reqFailTotal":null,"respFailTotal":null,"reqTimeoutTotal":null,"participatingTotal":null,"winTotal":null,"maxTime":null,"minTime":null,"avgTime":null,"amount":null,"eventExposureTotal":null,"eventClickTotal":null,"appendEventTypes":null,"accountName":null,"accountCode":null,"id":75,"advertiserId":48,"advertiserAppId":48,"name":"爱念-爱念App-安卓-信息流-ces-分成","code":"b7afaecb","type":1,"timeout":600,"pnyParam":"[]","settlementType":2,"bidPriceType":1,"bidRisesRatio":null,"sharingPriceType":1,"fixedPrice":0.0,"limitRuleOpen":false,"limitType":4,"quota":null,"startTime":null,"endTime":null,"filterExposureNum":null,"filterClickNum":null,"filterDeviceReqNum":null,"filterDeviceExposureNum":null,"directOnOff":false,"targetArea":"[]","targetAppIndustry":"[]","targetAppPackage":null,"targetDeviceModel":null,"targetDeviceBrand":"[]","targetNetwork":"[]","targetOperator":"[]","targetOsVersionType":null,"targetOsVersion":null,"filterOnOff":false,"filterArea":"[]","filterAppIndustry":"[]","filterAppPackage":null,"filterDeviceModel":null,"filterUrlDomain":null,"filterDeviceBrand":"[]","filterNetwork":"[]","filterOperator":"[]","filterEmptyDevice":0,"filterInvalidDevice":0,"filterForeignIp":0,"createTime":"2025-06-30 09:44:25","operatorId":13,"addEventTypes":"[1,2]","dspType":2,"accountId":null,"filterInstalledAppPackage":null,"targetInstalledAppPackage":null,"targetPackId":null,"filterPackId":null,"filterRepeatEvent":0},{"advertiserName":null,"advertiserAppName":null,"advertiserAppCode":null,"advertiserAppType":null,"advertiserProtocolTagParam":null,"directCitySelection":null,"industrySelection":null,"targetDeviceBrandCheck":null,"targetNetworkCheck":null,"targetOperatorCheck":null,"filterCitySelection":null,"filterIndustrySelection":null,"filterDeviceBrandCheck":null,"filterNetworkCheck":null,"filterOperatorCheck":null,"reqTotal":null,"reqSuccessTotal":null,"reqFailTotal":null,"respFailTotal":null,"reqTimeoutTotal":null,"participatingTotal":null,"winTotal":null,"maxTime":null,"minTime":null,"avgTime":null,"amount":null,"eventExposureTotal":null,"eventClickTotal":null,"appendEventTypes":null,"accountName":null,"accountCode":null,"id":70,"advertiserId":46,"advertiserAppId":46,"name":"甜枣测试-cc-安卓-信息流-ccs-分成","code":"a5b9967500554127","type":1,"timeout":1000,"pnyParam":"[]","settlementType":2,"bidPriceType":1,"bidRisesRatio":null,"sharingPriceType":1,"fixedPrice":0.0,"limitRuleOpen":false,"limitType":4,"quota":null,"startTime":null,"endTime":null,"filterExposureNum":null,"filterClickNum":null,"filterDeviceReqNum":null,"filterDeviceExposureNum":null,"directOnOff":false,"targetArea":"[]","targetAppIndustry":"[]","targetAppPackage":null,"targetDeviceModel":null,"targetDeviceBrand":"[]","targetNetwork":"[]","targetOperator":"[]","targetOsVersionType":null,"targetOsVersion":null,"filterOnOff":false,"filterArea":"[]","filterAppIndustry":"[]","filterAppPackage":null,"filterDeviceModel":null,"filterUrlDomain":null,"filterDeviceBrand":"[]","filterNetwork":"[]","filterOperator":"[]","filterEmptyDevice":0,"filterInvalidDevice":0,"filterForeignIp":0,"createTime":"2025-06-27 11:04:14","operatorId":16,"addEventTypes":"[1,2]","dspType":2,"accountId":null,"filterInstalledAppPackage":null,"targetInstalledAppPackage":null,"targetPackId":null,"filterPackId":null,"filterRepeatEvent":0},{"advertiserName":null,"advertiserAppName":null,"advertiserAppCode":null,"advertiserAppType":null,"advertiserProtocolTagParam":null,"directCitySelection":null,"industrySelection":null,"targetDeviceBrandCheck":null,"targetNetworkCheck":null,"targetOperatorCheck":null,"filterCitySelection":null,"filterIndustrySelection":null,"filterDeviceBrandCheck":null,"filterNetworkCheck":null,"filterOperatorCheck":null,"reqTotal":null,"reqSuccessTotal":null,"reqFailTotal":null,"respFailTotal":null,"reqTimeoutTotal":null,"participatingTotal":null,"winTotal":null,"maxTime":null,"minTime":null,"avgTime":null,"amount":null,"eventExposureTotal":null,"eventClickTotal":null,"appendEventTypes":null,"accountName":null,"accountCode":null,"id":59,"advertiserId":38,"advertiserAppId":38,"name":"嗨吖测试-ios001-iOS-信息流-xinxi-分成","code":"470cc7ac28ed52da","type":1,"timeout":3600,"pnyParam":"[]","settlementType":2,"bidPriceType":1,"bidRisesRatio":null,"sharingPriceType":1,"fixedPrice":0.0,"limitRuleOpen":false,"limitType":4,"quota":null,"startTime":null,"endTime":null,"filterExposureNum":null,"filterClickNum":null,"filterDeviceReqNum":null,"filterDeviceExposureNum":null,"directOnOff":false,"targetArea":"[]","targetAppIndustry":"[]","targetAppPackage":null,"targetDeviceModel":null,"targetDeviceBrand":"[]","targetNetwork":"[]","targetOperator":"[]","targetOsVersionType":null,"targetOsVersion":null,"filterOnOff":false,"filterArea":"[]","filterAppIndustry":"[]","filterAppPackage":null,"filterDeviceModel":null,"filterUrlDomain":null,"filterDeviceBrand":"[]","filterNetwork":"[]","filterOperator":"[]","filterEmptyDevice":0,"filterInvalidDevice":0,"filterForeignIp":0,"createTime":"2025-05-22 21:43:26","operatorId":16,"addEventTypes":"[1,2]","dspType":2,"accountId":null,"filterInstalledAppPackage":null,"targetInstalledAppPackage":null,"targetPackId":null,"filterPackId":null,"filterRepeatEvent":0},{"advertiserName":null,"advertiserAppName":null,"advertiserAppCode":null,"advertiserAppType":null,"advertiserProtocolTagParam":null,"directCitySelection":null,"industrySelection":null,"targetDeviceBrandCheck":null,"targetNetworkCheck":null,"targetOperatorCheck":null,"filterCitySelection":null,"filterIndustrySelection":null,"filterDeviceBrandCheck":null,"filterNetworkCheck":null,"filterOperatorCheck":null,"reqTotal":null,"reqSuccessTotal":null,"reqFailTotal":null,"respFailTotal":null,"reqTimeoutTotal":null,"participatingTotal":null,"winTotal":null,"maxTime":null,"minTime":null,"avgTime":null,"amount":null,"eventExposureTotal":null,"eventClickTotal":null,"appendEventTypes":null,"accountName":null,"accountCode":null,"id":57,"advertiserId":38,"advertiserAppId":37,"name":"haiyanew-ces-安卓-信息流-111-分成","code":"470cc7ac28ed52da","type":1,"timeout":3000,"pnyParam":"[]","settlementType":2,"bidPriceType":1,"bidRisesRatio":null,"sharingPriceType":1,"fixedPrice":0.0,"limitRuleOpen":false,"limitType":4,"quota":null,"startTime":null,"endTime":null,"filterExposureNum":null,"filterClickNum":null,"filterDeviceReqNum":null,"filterDeviceExposureNum":null,"directOnOff":false,"targetArea":"[]","targetAppIndustry":"[]","targetAppPackage":null,"targetDeviceModel":null,"targetDeviceBrand":"[]","targetNetwork":"[]","targetOperator":"[]","targetOsVersionType":null,"targetOsVersion":null,"filterOnOff":false,"filterArea":"[]","filterAppIndustry":"[]","filterAppPackage":null,"filterDeviceModel":null,"filterUrlDomain":null,"filterDeviceBrand":"[]","filterNetwork":"[]","filterOperator":"[]","filterEmptyDevice":0,"filterInvalidDevice":0,"filterForeignIp":0,"createTime":"2025-05-22 21:24:13","operatorId":16,"addEventTypes":"[1,2]","dspType":2,"accountId":null,"filterInstalledAppPackage":null,"targetInstalledAppPackage":null,"targetPackId":null,"filterPackId":null,"filterRepeatEvent":0},{"advertiserName":null,"advertiserAppName":null,"advertiserAppCode":null,"advertiserAppType":null,"advertiserProtocolTagParam":null,"directCitySelection":null,"industrySelection":null,"targetDeviceBrandCheck":null,"targetNetworkCheck":null,"targetOperatorCheck":null,"filterCitySelection":null,"filterIndustrySelection":null,"filterDeviceBrandCheck":null,"filterNetworkCheck":null,"filterOperatorCheck":null,"reqTotal":null,"reqSuccessTotal":null,"reqFailTotal":null,"respFailTotal":null,"reqTimeoutTotal":null,"participatingTotal":null,"winTotal":null,"maxTime":null,"minTime":null,"avgTime":null,"amount":null,"eventExposureTotal":null,"eventClickTotal":null,"appendEventTypes":null,"accountName":null,"accountCode":null,"id":56,"advertiserId":37,"advertiserAppId":36,"name":"嗨吖CES-HAIYA-安卓-信息流-ces-分成","code":"470cc7ac28ed52da","type":1,"timeout":2000,"pnyParam":"[]","settlementType":2,"bidPriceType":1,"bidRisesRatio":null,"sharingPriceType":1,"fixedPrice":0.0,"limitRuleOpen":false,"limitType":4,"quota":null,"startTime":null,"endTime":null,"filterExposureNum":null,"filterClickNum":null,"filterDeviceReqNum":null,"filterDeviceExposureNum":null,"directOnOff":false,"targetArea":"[]","targetAppIndustry":"[]","targetAppPackage":null,"targetDeviceModel":null,"targetDeviceBrand":"[]","targetNetwork":"[]","targetOperator":"[]","targetOsVersionType":null,"targetOsVersion":null,"filterOnOff":false,"filterArea":"[]","filterAppIndustry":"[]","filterAppPackage":null,"filterDeviceModel":null,"filterUrlDomain":null,"filterDeviceBrand":"[]","filterNetwork":"[]","filterOperator":"[]","filterEmptyDevice":0,"filterInvalidDevice":0,"filterForeignIp":0,"createTime":"2025-05-22 21:11:25","operatorId":16,"addEventTypes":"[1,2]","dspType":2,"accountId":null,"filterInstalledAppPackage":null,"targetInstalledAppPackage":null,"targetPackId":null,"filterPackId":null,"filterRepeatEvent":0},{"advertiserName":null,"advertiserAppName":null,"advertiserAppCode":null,"advertiserAppType":null,"advertiserProtocolTagParam":null,"directCitySelection":null,"industrySelection":null,"targetDeviceBrandCheck":null,"targetNetworkCheck":null,"targetOperatorCheck":null,"filterCitySelection":null,"filterIndustrySelection":null,"filterDeviceBrandCheck":null,"filterNetworkCheck":null,"filterOperatorCheck":null,"reqTotal":null,"reqSuccessTotal":null,"reqFailTotal":null,"respFailTotal":null,"reqTimeoutTotal":null,"participatingTotal":null,"winTotal":null,"maxTime":null,"minTime":null,"avgTime":null,"amount":null,"eventExposureTotal":null,"eventClickTotal":null,"appendEventTypes":null,"accountName":null,"accountCode":null,"id":55,"advertiserId":36,"advertiserAppId":35,"name":"降龙十八掌测试-测试ios-iOS-信息流-测完002-分成","code":"9386","type":1,"timeout":600,"pnyParam":"[]","settlementType":2,"bidPriceType":1,"bidRisesRatio":null,"sharingPriceType":1,"fixedPrice":0.0,"limitRuleOpen":false,"limitType":4,"quota":null,"startTime":null,"endTime":null,"filterExposureNum":null,"filterClickNum":null,"filterDeviceReqNum":null,"filterDeviceExposureNum":null,"directOnOff":false,"targetArea":"[]","targetAppIndustry":"[]","targetAppPackage":null,"targetDeviceModel":null,"targetDeviceBrand":"[]","targetNetwork":"[]","targetOperator":"[]","targetOsVersionType":null,"targetOsVersion":null,"filterOnOff":false,"filterArea":"[]","filterAppIndustry":"[]","filterAppPackage":null,"filterDeviceModel":null,"filterUrlDomain":null,"filterDeviceBrand":"[]","filterNetwork":"[]","filterOperator":"[]","filterEmptyDevice":0,"filterInvalidDevice":0,"filterForeignIp":0,"createTime":"2025-05-21 18:34:20","operatorId":16,"addEventTypes":"[1,2]","dspType":2,"accountId":null,"filterInstalledAppPackage":null,"targetInstalledAppPackage":null,"targetPackId":null,"filterPackId":null,"filterRepeatEvent":0},{"advertiserName":null,"advertiserAppName":null,"advertiserAppCode":null,"advertiserAppType":null,"advertiserProtocolTagParam":null,"directCitySelection":null,"industrySelection":null,"targetDeviceBrandCheck":null,"targetNetworkCheck":null,"targetOperatorCheck":null,"filterCitySelection":null,"filterIndustrySelection":null,"filterDeviceBrandCheck":null,"filterNetworkCheck":null,"filterOperatorCheck":null,"reqTotal":null,"reqSuccessTotal":null,"reqFailTotal":null,"respFailTotal":null,"reqTimeoutTotal":null,"participatingTotal":null,"winTotal":null,"maxTime":null,"minTime":null,"avgTime":null,"amount":null,"eventExposureTotal":null,"eventClickTotal":null,"appendEventTypes":null,"accountName":null,"accountCode":null,"id":50,"advertiserId":35,"advertiserAppId":33,"name":"chetiansi-android","code":"fd0a4866-5390-4cf4-b4f1-50c057f7c790","type":1,"timeout":2000,"pnyParam":"[]","settlementType":2,"bidPriceType":1,"bidRisesRatio":10,"sharingPriceType":1,"fixedPrice":0.0,"limitRuleOpen":false,"limitType":4,"quota":null,"startTime":null,"endTime":null,"filterExposureNum":null,"filterClickNum":null,"filterDeviceReqNum":null,"filterDeviceExposureNum":null,"directOnOff":false,"targetArea":"[]","targetAppIndustry":"[]","targetAppPackage":null,"targetDeviceModel":null,"targetDeviceBrand":"[]","targetNetwork":"[]","targetOperator":"[]","targetOsVersionType":null,"targetOsVersion":null,"filterOnOff":false,"filterArea":"[]","filterAppIndustry":"[]","filterAppPackage":null,"filterDeviceModel":null,"filterUrlDomain":null,"filterDeviceBrand":"[]","filterNetwork":"[]","filterOperator":"[]","filterEmptyDevice":0,"filterInvalidDevice":0,"filterForeignIp":0,"createTime":"2025-05-16 16:13:42","operatorId":16,"addEventTypes":"[1,2]","dspType":2,"accountId":null,"filterInstalledAppPackage":null,"targetInstalledAppPackage":null,"targetPackId":null,"filterPackId":null,"filterRepeatEvent":0},{"advertiserName":null,"advertiserAppName":null,"advertiserAppCode":null,"advertiserAppType":null,"advertiserProtocolTagParam":null,"directCitySelection":null,"industrySelection":null,"targetDeviceBrandCheck":null,"targetNetworkCheck":null,"targetOperatorCheck":null,"filterCitySelection":null,"filterIndustrySelection":null,"filterDeviceBrandCheck":null,"filterNetworkCheck":null,"filterOperatorCheck":null,"reqTotal":null,"reqSuccessTotal":null,"reqFailTotal":null,"respFailTotal":null,"reqTimeoutTotal":null,"participatingTotal":null,"winTotal":null,"maxTime":null,"minTime":null,"avgTime":null,"amount":null,"eventExposureTotal":null,"eventClickTotal":null,"appendEventTypes":null,"accountName":null,"accountCode":null,"id":49,"advertiserId":34,"advertiserAppId":32,"name":"MOSTMOB测试-mm-android-安卓-信息流-句读Android-分成","code":"*********","type":1,"timeout":3000,"pnyParam":"[]","settlementType":2,"bidPriceType":1,"bidRisesRatio":null,"sharingPriceType":1,"fixedPrice":0.0,"limitRuleOpen":false,"limitType":4,"quota":null,"startTime":null,"endTime":null,"filterExposureNum":null,"filterClickNum":null,"filterDeviceReqNum":null,"filterDeviceExposureNum":null,"directOnOff":false,"targetArea":"[]","targetAppIndustry":"[]","targetAppPackage":null,"targetDeviceModel":null,"targetDeviceBrand":"[]","targetNetwork":"[]","targetOperator":"[]","targetOsVersionType":null,"targetOsVersion":null,"filterOnOff":false,"filterArea":"[]","filterAppIndustry":"[]","filterAppPackage":null,"filterDeviceModel":null,"filterUrlDomain":null,"filterDeviceBrand":"[]","filterNetwork":"[]","filterOperator":"[]","filterEmptyDevice":0,"filterInvalidDevice":0,"filterForeignIp":0,"createTime":"2025-05-16 14:32:58","operatorId":16,"addEventTypes":"[1,2]","dspType":2,"accountId":null,"filterInstalledAppPackage":null,"targetInstalledAppPackage":null,"targetPackId":null,"filterPackId":null,"filterRepeatEvent":0},{"advertiserName":null,"advertiserAppName":null,"advertiserAppCode":null,"advertiserAppType":null,"advertiserProtocolTagParam":null,"directCitySelection":null,"industrySelection":null,"targetDeviceBrandCheck":null,"targetNetworkCheck":null,"targetOperatorCheck":null,"filterCitySelection":null,"filterIndustrySelection":null,"filterDeviceBrandCheck":null,"filterNetworkCheck":null,"filterOperatorCheck":null,"reqTotal":null,"reqSuccessTotal":null,"reqFailTotal":null,"respFailTotal":null,"reqTimeoutTotal":null,"participatingTotal":null,"winTotal":null,"maxTime":null,"minTime":null,"avgTime":null,"amount":null,"eventExposureTotal":null,"eventClickTotal":null,"appendEventTypes":null,"accountName":null,"accountCode":null,"id":48,"advertiserId":34,"advertiserAppId":31,"name":"ios-taken-信息流","code":"*********","type":1,"timeout":3000,"pnyParam":"[]","settlementType":2,"bidPriceType":1,"bidRisesRatio":null,"sharingPriceType":1,"fixedPrice":0.0,"limitRuleOpen":false,"limitType":4,"quota":null,"startTime":null,"endTime":null,"filterExposureNum":null,"filterClickNum":null,"filterDeviceReqNum":null,"filterDeviceExposureNum":null,"directOnOff":false,"targetArea":"[]","targetAppIndustry":"[]","targetAppPackage":null,"targetDeviceModel":null,"targetDeviceBrand":"[]","targetNetwork":"[]","targetOperator":"[]","targetOsVersionType":null,"targetOsVersion":null,"filterOnOff":false,"filterArea":"[]","filterAppIndustry":"[]","filterAppPackage":null,"filterDeviceModel":null,"filterUrlDomain":null,"filterDeviceBrand":"[]","filterNetwork":"[]","filterOperator":"[]","filterEmptyDevice":0,"filterInvalidDevice":0,"filterForeignIp":0,"createTime":"2025-05-15 21:22:47","operatorId":16,"addEventTypes":"[1,2]","dspType":2,"accountId":null,"filterInstalledAppPackage":null,"targetInstalledAppPackage":null,"targetPackId":null,"filterPackId":null,"filterRepeatEvent":0}],"totalCount":15,"start":0,"limit":10,"currentPageNum":1,"totalPage":2}}]  SUCCESS
[2025-07-12 19:38:57.317] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/strategy/rule/page] stack[cn.taken.ad.api.oper.strategy.StrategyRuleApi.page] time[2025-07-12 19:38:57 to 2025-07-12 19:38:57] params[req={"strategyId":41,"start":0,"limit":20} ; ] response[{"success":true,"code":"0","message":"success","result":[{"advertName":"测试","appName":"测试","appCode":"09uy1v","appType":1,"tagName":"测试-测试-安卓-开屏-OPEN-分成","tagCode":"y872mu","settlementType":2,"type":4,"mediaReqTotal":0,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"advertiserReqTotal":0,"advertiserReqSuccessTotal":0,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"mediaWinTotal":0,"advertiserWinTotal":0,"mediaAmount":0.00,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0,"id":134,"strategyId":41,"advertiserId":1,"advertiserAppId":1,"advertiserTagId":1,"parallelPriority":1,"randomFlowRatio":1,"randomQps":null,"state":1,"handleType":0,"isDelete":0,"advTagFixedPrice":0.0,"rtbToSharingBasePriceType":1,"rtbToSharingBidPriceType":1,"rtbToSharingMaxPrice":null,"rtbToSharingBidRisesRatio":null,"rtbToSharingFixedPrice":null}]}]  SUCCESS
[2025-07-12 19:38:59.843] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:39:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:39:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:39:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:39:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:39:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:39:06.971] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:39:08.055] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:39:10.042] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1938 : 0
[2025-07-12 19:39:10.050] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1938:0
[2025-07-12 19:39:11.933] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:39:14.169] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:39:14.282] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:39:14.356] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:39:14.425] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:39:14.491] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:39:14.558] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:39:14.625] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:39:14.699] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:39:14.766] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:39:14.834] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:39:14.901] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:39:14.920] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:39:14.967] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:39:15.035] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:39:15.110] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:39:15.175] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:39:15.240] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:39:15.307] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:39:15.377] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:39:15.445] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:39:15.510] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:39:15.550] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:39:16.946] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:39:20.036] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1938:0
[2025-07-12 19:39:20.037] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1938:0
[2025-07-12 19:39:20.037] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1938:size 0
[2025-07-12 19:39:20.038] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1938 : size 0
[2025-07-12 19:39:20.039] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1938 : 0
[2025-07-12 19:39:20.040] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1938:0
[2025-07-12 19:39:20.049] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1938:0
[2025-07-12 19:39:20.175] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1938:0
[2025-07-12 19:39:24.021] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:39:36.116] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:39:37.068] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:39:40.133] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:39:40.166] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:39:45.021] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:39:45.557] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:39:45.665] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:39:45.733] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:39:45.798] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:39:45.862] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:39:45.928] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:39:45.997] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:39:46.065] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:39:46.130] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:39:46.196] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:39:46.264] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:39:46.330] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:39:46.398] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:39:46.466] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:39:46.535] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:39:46.602] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:39:46.669] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:39:46.737] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:39:46.807] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:39:46.874] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:39:46.916] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:39:47.034] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:39:48.203] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:40:00.292] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:40:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:40:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:40:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:40:01.150] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"41-134-3-8-65-1-1-1":{"id":null,"statisticsType":"MINUTE","statisticsTime":"********1939","strategyId":41,"strategyTagAdvId":134,"mediaId":3,"mediaAppId":8,"mediaTagId":65,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":10813,"mediaAvgTime":10813,"mediaMinTime":0,"mediaUseTimeTotal":10813,"advertiserId":1,"advertiserAppId":1,"advertiserTagId":1,"advertiserReqTotal":1,"advertiserReqSuccessTotal":0,"advertiserReqFailTotal":1,"advertiserRespFailTotal":1,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":2291,"advertiserAvgTime":2291,"advertiserMinTime":0,"advertiserUseTimeTotal":2291}},minuteMediaReq:{"41-3-8-65":{"id":null,"statisticsType":"MINUTE","statisticsTime":"********1939","mediaId":3,"mediaAppId":8,"mediaTagId":65,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":41,"maxTime":10813,"avgTime":10813,"minTime":0,"useTimeTotal":10813}},minuteAdvReq:{"1-1-1":{"id":null,"statisticsType":"MINUTE","statisticsTime":"********1939","advertiserId":1,"advertiserAppId":1,"advertiserTagId":1,"reqTotal":1,"reqSuccessTotal":0,"reqFailTotal":1,"respFailTotal":1,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":2291,"avgTime":2291,"minTime":0,"useTimeTotal":2291}}
[2025-07-12 19:40:01.240] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"41-3-8-65-FAIL_ADV":{"id":null,"statisticsType":"MINUTE","statisticsTime":"********1939","mediaId":3,"mediaAppId":8,"mediaTagId":65,"code":"FAIL_ADV","total":1,"strategyId":41}},advertiserErrorCode:{"1-1-1-HTTP/1.1 600 http请求异常":{"id":null,"statisticsType":"MINUTE","statisticsTime":"********1939","advertiserId":1,"advertiserAppId":1,"advertiserTagId":1,"code":"HTTP/1.1 600 http请求异常","total":1}}
[2025-07-12 19:40:07.167] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:40:07.834] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:40:10.034] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1939 : 0
[2025-07-12 19:40:10.034] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1939:0
[2025-07-12 19:40:12.383] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:40:15.110] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:40:16.921] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:40:17.039] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:40:17.116] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:40:17.127] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:40:17.198] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:40:17.270] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:40:17.344] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:40:17.417] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:40:17.490] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:40:17.562] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:40:17.637] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:40:17.711] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:40:17.786] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:40:17.860] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:40:17.931] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:40:18.004] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:40:18.078] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:40:18.153] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:40:18.228] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:40:18.307] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:40:18.380] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:40:18.421] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:40:20.046] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1939:0
[2025-07-12 19:40:20.047] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1939:0
[2025-07-12 19:40:20.047] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1939:0
[2025-07-12 19:40:20.284] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1939 : 1
[2025-07-12 19:40:20.296] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1939:1
[2025-07-12 19:40:20.312] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1939 : size 1
[2025-07-12 19:40:20.322] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1939:size 1
[2025-07-12 19:40:20.703] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1939:1
[2025-07-12 19:40:24.482] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:40:36.572] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:40:37.256] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:40:40.079] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsHour,148] INFO  - statistics dsp hour success ********19
[2025-07-12 19:40:40.088] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.statisticsHour,143] INFO  - statistics dsp hour success ********19
[2025-07-12 19:40:40.092] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 19:40:40.415] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsHour,153] INFO  - statistics hour success ********19
[2025-07-12 19:40:40.418] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 19:40:40.435] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsHour,148] INFO  - statistics hour success ********19
[2025-07-12 19:40:40.482] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:40:40.488] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsHour,118] INFO  - statistics hour success ********19
[2025-07-12 19:40:40.522] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:40:40.539] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.statisticsHour,126] INFO  - statistics hour success ********19
[2025-07-12 19:40:40.597] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:40:40.794] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:40:40.845] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsHour,101] INFO  - statistics hour success ********19
[2025-07-12 19:40:40.961] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:40:45.202] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:40:47.220] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:40:48.424] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:40:48.551] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:40:48.637] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:40:48.663] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:40:48.721] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:40:48.808] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:40:48.893] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:40:48.982] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:40:49.077] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:40:49.162] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:40:49.248] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:40:49.337] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:40:49.422] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:40:49.506] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:40:49.592] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:40:49.677] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:40:49.760] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:40:49.846] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:40:49.932] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:40:50.019] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:40:50.080] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsDay,131] INFO  - statistics dsp day success ********
[2025-07-12 19:40:50.081] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsDay,101] INFO  - statistics day success ********
[2025-07-12 19:40:50.083] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsDay,103] INFO  - statistics day success ********
[2025-07-12 19:40:50.094] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsDay,103] INFO  - statistics day success ********
[2025-07-12 19:40:50.094] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsDay,82] INFO  - statistics day success ********
[2025-07-12 19:40:50.096] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.statisticsDay,109] INFO  - statistics day success ********
[2025-07-12 19:40:50.097] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.statisticsDay,126] INFO  - statistics dsp day success ********
[2025-07-12 19:40:50.102] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:40:50.143] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:40:50.392] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsDay,82] INFO  - statistics day success ********
[2025-07-12 19:40:50.504] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsDay,136] INFO  - statistics day success ********
[2025-07-12 19:40:50.540] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsDay,84] INFO  - statistics day success ********
[2025-07-12 19:40:50.556] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsDay,131] INFO  - statistics day success ********
[2025-07-12 19:40:50.556] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsDay,82] INFO  - statistics day success ********
[2025-07-12 19:41:00.750] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:41:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:41:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:41:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:41:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:41:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:41:07.345] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:41:08.056] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:41:10.041] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1940 : 0
[2025-07-12 19:41:10.041] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1940:0
[2025-07-12 19:41:12.841] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:41:15.298] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:41:17.320] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:41:20.043] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1940:0
[2025-07-12 19:41:20.044] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1940:0
[2025-07-12 19:41:20.047] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1940:0
[2025-07-12 19:41:20.047] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1940 : 0
[2025-07-12 19:41:20.047] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1940:0
[2025-07-12 19:41:20.048] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1940:size 0
[2025-07-12 19:41:20.056] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1940 : size 0
[2025-07-12 19:41:20.151] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:41:20.159] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1940:0
[2025-07-12 19:41:20.279] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:41:20.361] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:41:20.444] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:41:20.530] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:41:20.615] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:41:20.702] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:41:20.787] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:41:20.883] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:41:20.969] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:41:21.066] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:41:21.150] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:41:21.237] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:41:21.322] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:41:21.404] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:41:21.487] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:41:21.569] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:41:21.655] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:41:21.740] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:41:21.825] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:41:21.859] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:41:24.926] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:41:37.010] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:41:37.441] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:41:40.169] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:41:40.180] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:41:45.396] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:41:47.397] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:41:49.092] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:41:51.863] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:41:51.990] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:41:52.074] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:41:52.158] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:41:52.242] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:41:52.326] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:41:52.409] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:41:52.492] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:41:52.576] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:41:52.660] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:41:52.742] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:41:52.826] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:41:52.908] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:41:52.992] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:41:53.078] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:41:53.162] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:41:53.246] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:41:53.328] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:41:53.412] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:41:53.493] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:41:53.527] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:42:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:42:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:42:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:42:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:42:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:42:01.161] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:42:07.535] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:42:07.749] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:42:10.045] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1941 : 0
[2025-07-12 19:42:10.046] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1941:0
[2025-07-12 19:42:13.239] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:42:15.495] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:42:17.472] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:42:20.043] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1941:0
[2025-07-12 19:42:20.045] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1941:0
[2025-07-12 19:42:20.046] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1941:0
[2025-07-12 19:42:20.046] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1941 : 0
[2025-07-12 19:42:20.047] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1941:size 0
[2025-07-12 19:42:20.047] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1941 : size 0
[2025-07-12 19:42:20.047] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1941:0
[2025-07-12 19:42:20.156] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1941:0
[2025-07-12 19:42:23.530] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:42:23.647] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:42:23.731] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:42:23.815] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:42:23.899] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:42:23.985] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:42:24.079] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:42:24.160] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:42:24.242] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:42:24.323] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:42:24.406] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:42:24.487] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:42:24.568] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:42:24.651] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:42:24.737] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:42:24.819] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:42:24.901] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:42:24.983] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:42:25.064] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:42:25.148] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:42:25.183] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:42:25.316] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:42:37.398] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:42:37.629] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:42:40.164] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:42:40.176] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:42:45.596] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:42:47.552] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:42:49.479] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:42:55.190] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:42:55.312] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:42:55.397] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:42:55.492] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:42:55.576] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:42:55.660] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:42:55.742] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:42:55.825] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:42:55.910] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:42:55.996] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:42:56.080] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:42:56.167] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:42:56.251] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:42:56.333] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:42:56.419] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:42:56.503] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:42:56.586] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:42:56.672] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:42:56.757] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:42:56.840] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:42:56.874] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:43:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:43:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:43:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:43:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:43:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:43:01.563] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:43:06.609] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:43:07.721] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:43:10.041] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1942 : 0
[2025-07-12 19:43:10.042] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1942:0
[2025-07-12 19:43:13.641] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:43:15.686] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:43:17.624] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:43:20.040] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1942:0
[2025-07-12 19:43:20.040] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1942 : size 0
[2025-07-12 19:43:20.040] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1942 : 0
[2025-07-12 19:43:20.040] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1942:0
[2025-07-12 19:43:20.040] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1942:size 0
[2025-07-12 19:43:20.040] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1942:0
[2025-07-12 19:43:20.040] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1942:0
[2025-07-12 19:43:20.147] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1942:0
[2025-07-12 19:43:25.723] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:43:26.886] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:43:27.007] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:43:27.092] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:43:27.176] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:43:27.262] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:43:27.348] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:43:27.430] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:43:27.515] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:43:27.602] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:43:27.692] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:43:27.778] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:43:27.865] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:43:27.950] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:43:28.034] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:43:28.121] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:43:28.205] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:43:28.288] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:43:28.375] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:43:28.462] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:43:28.546] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:43:28.578] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:43:37.795] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:43:37.815] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:43:40.137] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:43:40.171] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:43:45.784] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:43:47.695] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:43:49.874] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:43:58.587] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:43:58.707] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:43:58.791] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:43:58.875] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:43:58.958] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:43:59.044] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:43:59.129] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:43:59.210] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:43:59.295] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:43:59.379] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:43:59.463] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:43:59.550] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:43:59.634] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:43:59.719] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:43:59.805] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:43:59.890] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:43:59.981] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:44:00.067] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:44:00.150] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:44:00.237] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:44:00.271] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:44:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:44:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:44:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:44:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:44:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:44:01.946] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:44:07.734] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:44:07.902] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:44:10.033] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1943 : 0
[2025-07-12 19:44:10.036] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1943:0
[2025-07-12 19:44:14.020] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:44:15.880] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:44:17.776] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:44:20.043] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1943:0
[2025-07-12 19:44:20.044] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1943 : 0
[2025-07-12 19:44:20.044] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1943:0
[2025-07-12 19:44:20.045] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1943:size 0
[2025-07-12 19:44:20.046] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1943 : size 0
[2025-07-12 19:44:20.046] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1943:0
[2025-07-12 19:44:20.046] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1943:0
[2025-07-12 19:44:20.190] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1943:0
[2025-07-12 19:44:26.095] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:44:30.276] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:44:30.396] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:44:30.482] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:44:30.567] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:44:30.652] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:44:30.735] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:44:30.822] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:44:30.904] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:44:30.987] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:44:31.070] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:44:31.153] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:44:31.236] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:44:31.318] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:44:31.400] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:44:31.484] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:44:31.571] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:44:31.654] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:44:31.736] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:44:31.817] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:44:31.902] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:44:31.937] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:44:37.999] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:44:38.175] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:44:40.140] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:44:40.180] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:44:44.637] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/media/list] stack[cn.taken.ad.api.oper.media.MediaApi.list] time[2025-07-12 19:44:44 to 2025-07-12 19:44:44] params[req={"name":null} ; ] response[{"success":true,"code":"0","message":"success","result":[{"id":16,"name":"萤火虫CES","code":"6f0a4c40","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"YEjp1EQlGI4m8zG8","protocolId":15,"pnyParam":"[]","createTime":"2025-06-28 14:46:33","operatorId":16,"settlementRatio":null,"filterOnOff":false,"filterUrlDomain":null},{"id":15,"name":"安卓","code":"edc216f2","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"mc1uqn1k96hndgkm","protocolId":14,"pnyParam":"[]","createTime":"2025-06-18 19:11:56","operatorId":16,"settlementRatio":null,"filterOnOff":false,"filterUrlDomain":null},{"id":14,"name":"ZIXUAN测试","code":"9ac2a762","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"mc1pdjv8551q1xgw","protocolId":13,"pnyParam":"[]","createTime":"2025-06-18 16:41:52","operatorId":16,"settlementRatio":null,"filterOnOff":false,"filterUrlDomain":null},{"id":13,"name":"Test过滤","code":"20b6b693","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"mbunr7ps4f5l1k5o","protocolId":1,"pnyParam":"[]","createTime":"2025-06-13 18:22:17","operatorId":4,"settlementRatio":80.0,"filterOnOff":false,"filterUrlDomain":"ssp.fmmob.com,s.adintl.cn"},{"id":12,"name":"广推测试","code":"guangtui001","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"maqmxfvkr4i0p7e1","protocolId":12,"pnyParam":"[]","createTime":"2025-05-16 18:08:09","operatorId":16,"settlementRatio":null,"filterOnOff":false,"filterUrlDomain":null},{"id":11,"name":"研义","code":"cd9c38a5","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"makq2eua42r52gs2","protocolId":11,"pnyParam":"[]","createTime":"2025-05-12 14:49:55","operatorId":1,"settlementRatio":1.0,"filterOnOff":false,"filterUrlDomain":null},{"id":10,"name":"亿帆","code":"5c5d540f","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"mag50v37dieobmp4","protocolId":10,"pnyParam":"[]","createTime":"2025-05-09 09:49:02","operatorId":13,"settlementRatio":null,"filterOnOff":false,"filterUrlDomain":null},{"id":9,"name":"美数","code":"f8cc67db","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"maf3toywv7f6hn8d","protocolId":9,"pnyParam":"[]","createTime":"2025-05-08 16:27:56","operatorId":1,"settlementRatio":10.0,"filterOnOff":false,"filterUrlDomain":null},{"id":8,"name":"UBiX","code":"ba9582d7","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"maerc18s3cntpp70","protocolId":8,"pnyParam":"[]","createTime":"2025-05-08 10:38:05","operatorId":13,"settlementRatio":null,"filterOnOff":false,"filterUrlDomain":null},{"id":7,"name":"努比亚","code":"e090418c","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"madu64vyj7i6ihyi","protocolId":7,"pnyParam":"[]","createTime":"2025-05-07 19:09:46","operatorId":13,"settlementRatio":null,"filterOnOff":false,"filterUrlDomain":null},{"id":6,"name":"鱼泡网","code":"996ecb17","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"m9w772ront31fiap","protocolId":4,"pnyParam":"[]","createTime":"2025-04-25 10:54:34","operatorId":13,"settlementRatio":null,"filterOnOff":false,"filterUrlDomain":null},{"id":5,"name":"章鱼","code":"00464382","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"m9touck4no3ilcut","protocolId":3,"pnyParam":"[]","createTime":"2025-04-23 16:45:25","operatorId":1,"settlementRatio":null,"filterOnOff":false,"filterUrlDomain":null},{"id":4,"name":"多赢","code":"92dc7970","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"m99c9fksf8fbtrgz","protocolId":2,"pnyParam":"[{\"paramsName\":\"加密KEY\",\"paramsKey\":\"aesKey\",\"paramsValue\":\"de63cbfa1ec7b1db\"},{\"paramsName\":\"加密IV\",\"paramsKey\":\"aesIvKey\",\"paramsValue\":\"2c3528325d999e18\"}]","createTime":"2025-04-09 10:58:12","operatorId":4,"settlementRatio":80.0,"filterOnOff":false,"filterUrlDomain":null},{"id":3,"name":"Taken","code":"f3f0876a","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"10630feac87f4bb4","protocolId":1,"pnyParam":"[]","createTime":"2025-03-29 12:00:00","operatorId":4,"settlementRatio":80.0,"filterOnOff":false,"filterUrlDomain":null},{"id":2,"name":"抖音","code":"fc00fead","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"m8pbn3z4waihmpnz","protocolId":1,"pnyParam":"[]","createTime":"2025-03-26 10:44:53","operatorId":4,"settlementRatio":80.0,"filterOnOff":false,"filterUrlDomain":null},{"id":1,"name":"测试","code":"7b6fb536","linkman":null,"address":null,"linkmanMobile":null,"priceKey":"m8o80v10eeu9sryn","protocolId":1,"pnyParam":"[]","createTime":"2025-03-25 16:15:43","operatorId":3,"settlementRatio":30.0,"filterOnOff":false,"filterUrlDomain":"ssp.fmmob.com,**************,s.adintl.cn,tracking.adoptimizeplatform.com,openapi.aoongmob.com,open.fmmobi.com.cn,lemon.gameley.com"}]}]  SUCCESS
[2025-07-12 19:44:45.370] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/statistics/media/media] stack[cn.taken.ad.api.oper.statistics.StatisticsMediaApi.mediaStatistics] time[2025-07-12 19:44:44 to 2025-07-12 19:44:45] params[req={"statisticsType":"DAY","beginTime":"20250705","endTime":"********","mediaTagId":null,"mediaId":null,"mediaAppId":null,"bidType":null,"start":0,"limit":20} ; ] response[{"success":true,"code":"0","message":"success","result":{"list":[{"id":8485,"mediaId":3,"mediaAppId":8,"mediaTagId":20,"strategyId":3,"useTimeTotal":112536,"bidType":1,"statisticsType":"天","statisticsTime":"2025-07-12","mediaName":"Taken","mediaCode":"f3f0876a","appName":"测试安卓","appCode":"9e2db554d9d54715","appType":1,"appTypeName":"安卓","tagName":"测试-开屏-RTB","tagCode":"c0de47a4ed044ac0","tagType":4,"tagTypeName":"信息流","bidTypeName":"RTB","reqTotal":3,"reqInvalidTotal":0,"reqInvalidRate":0.00,"respFailTotal":0,"respFailRate":0.00,"participatingTotal":0,"participatingRate":0.00,"winTotal":0,"winRate":0,"amount":0.0000,"maxTime":81463,"avgTime":37512,"minTime":0,"event_1":null,"event_1Rate":0,"event_2":null,"event_2Rate":0,"event_3":null,"event_4":null,"event_5":null,"event_6":null,"event_7":null,"event_8":null,"event_9":null,"event_10":null,"event_11":null,"event_12":null,"event_13":null,"event_14":null,"event_15":null,"event_16":null,"event_17":null,"event_18":null,"event_19":null,"event_20":null,"event_21":null,"event_22":null,"event_23":null,"event_24":null,"event_25":null,"event_26":null,"event_27":null,"event_28":null,"event_29":null,"event_30":null,"event_31":null,"event_32":null,"event_33":null,"event_34":null,"event_35":null,"event_36":null,"event_37":null,"ecPm":0,"cpc":0},{"id":8486,"mediaId":3,"mediaAppId":8,"mediaTagId":65,"strategyId":41,"useTimeTotal":10813,"bidType":2,"statisticsType":"天","statisticsTime":"2025-07-12","mediaName":"Taken","mediaCode":"f3f0876a","appName":"测试安卓","appCode":"9e2db554d9d54715","appType":1,"appTypeName":"安卓","tagName":"句读","tagCode":"c31f9b24332c42b1_tech.caicheng.judourili","tagType":1,"tagTypeName":"信息流","bidTypeName":"分成","reqTotal":1,"reqInvalidTotal":0,"reqInvalidRate":0.00,"respFailTotal":0,"respFailRate":0.00,"participatingTotal":0,"participatingRate":0.00,"winTotal":0,"winRate":0,"amount":0.0000,"maxTime":10813,"avgTime":10813,"minTime":0,"event_1":null,"event_1Rate":0,"event_2":null,"event_2Rate":0,"event_3":null,"event_4":null,"event_5":null,"event_6":null,"event_7":null,"event_8":null,"event_9":null,"event_10":null,"event_11":null,"event_12":null,"event_13":null,"event_14":null,"event_15":null,"event_16":null,"event_17":null,"event_18":null,"event_19":null,"event_20":null,"event_21":null,"event_22":null,"event_23":null,"event_24":null,"event_25":null,"event_26":null,"event_27":null,"event_28":null,"event_29":null,"event_30":null,"event_31":null,"event_32":null,"event_33":null,"event_34":null,"event_35":null,"event_36":null,"event_37":null,"ecPm":0,"cpc":0},{"id":8476,"mediaId":3,"mediaAppId":3,"mediaTagId":5,"strategyId":31,"useTimeTotal":59382,"bidType":2,"statisticsType":"天","statisticsTime":"2025-07-07","mediaName":"Taken","mediaCode":"f3f0876a","appName":"头条","appCode":"tak1888","appType":1,"appTypeName":"安卓","tagName":"JYM-开屏","tagCode":"56bcf0db81264af2","tagType":4,"tagTypeName":"信息流","bidTypeName":"分成","reqTotal":1,"reqInvalidTotal":0,"reqInvalidRate":0.00,"respFailTotal":0,"respFailRate":0.00,"participatingTotal":1,"participatingRate":100.00,"winTotal":0,"winRate":0.00,"amount":0.0000,"maxTime":59382,"avgTime":59382,"minTime":0,"event_1":null,"event_1Rate":0,"event_2":null,"event_2Rate":0,"event_3":null,"event_4":null,"event_5":null,"event_6":null,"event_7":null,"event_8":null,"event_9":null,"event_10":null,"event_11":null,"event_12":null,"event_13":null,"event_14":null,"event_15":null,"event_16":null,"event_17":null,"event_18":null,"event_19":null,"event_20":null,"event_21":null,"event_22":null,"event_23":null,"event_24":null,"event_25":null,"event_26":null,"event_27":null,"event_28":null,"event_29":null,"event_30":null,"event_31":null,"event_32":null,"event_33":null,"event_34":null,"event_35":null,"event_36":null,"event_37":null,"ecPm":0,"cpc":0},{"id":8446,"mediaId":3,"mediaAppId":3,"mediaTagId":5,"strategyId":31,"useTimeTotal":884,"bidType":2,"statisticsType":"天","statisticsTime":"2025-07-05","mediaName":"Taken","mediaCode":"f3f0876a","appName":"头条","appCode":"tak1888","appType":1,"appTypeName":"安卓","tagName":"JYM-开屏","tagCode":"56bcf0db81264af2","tagType":4,"tagTypeName":"信息流","bidTypeName":"分成","reqTotal":1,"reqInvalidTotal":0,"reqInvalidRate":0.00,"respFailTotal":0,"respFailRate":0.00,"participatingTotal":1,"participatingRate":100.00,"winTotal":0,"winRate":0.00,"amount":0.0000,"maxTime":884,"avgTime":884,"minTime":0,"event_1":null,"event_1Rate":0,"event_2":null,"event_2Rate":0,"event_3":null,"event_4":null,"event_5":null,"event_6":null,"event_7":null,"event_8":null,"event_9":null,"event_10":null,"event_11":null,"event_12":null,"event_13":null,"event_14":null,"event_15":null,"event_16":null,"event_17":null,"event_18":null,"event_19":null,"event_20":null,"event_21":null,"event_22":null,"event_23":null,"event_24":null,"event_25":null,"event_26":null,"event_27":null,"event_28":null,"event_29":null,"event_30":null,"event_31":null,"event_32":null,"event_33":null,"event_34":null,"event_35":null,"event_36":null,"event_37":null,"ecPm":0,"cpc":0}],"totalCount":4,"start":0,"limit":20,"currentPageNum":1,"totalPage":1}}]  SUCCESS
[2025-07-12 19:44:45.956] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:44:46.815] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/media/tag/list] stack[cn.taken.ad.api.oper.media.MediaTagApi.findList] time[2025-07-12 19:44:46 to 2025-07-12 19:44:46] params[req={"mediaAppId":null,"name":"c31f9b24332c42b1_tech.caicheng.judourili","mediaAppIds":null,"mediaIds":null} ; ] response[{"success":true,"code":"0","message":"success","result":[{"id":65,"name":"句读","code":"c31f9b24332c42b1_tech.caicheng.judourili","mediaId":3,"mediaAppId":8,"type":1,"timeout":700,"width":1080,"height":1920,"bidType":2,"pnyParam":"[]","operatorId":4,"mediaOperatorId":null,"createTime":"2025-03-29 12:00:00","needHttps":0,"materialHttps":false,"flowType":1,"settlementRatio":82.0,"filterOnOff":false,"filterUrlDomain":null,"filterMaterialRatio":0,"filterMaterialType":0,"filterActionType":0,"mediaBaseTagId":21}]}]  SUCCESS
[2025-07-12 19:44:47.848] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:44:49.234] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/statistics/media/media] stack[cn.taken.ad.api.oper.statistics.StatisticsMediaApi.mediaStatistics] time[2025-07-12 19:44:48 to 2025-07-12 19:44:49] params[req={"statisticsType":"DAY","beginTime":"20250705","endTime":"********","mediaTagId":65,"mediaId":null,"mediaAppId":null,"bidType":null,"start":0,"limit":20} ; ] response[{"success":true,"code":"0","message":"success","result":{"list":[{"id":8486,"mediaId":3,"mediaAppId":8,"mediaTagId":65,"strategyId":41,"useTimeTotal":10813,"bidType":2,"statisticsType":"天","statisticsTime":"2025-07-12","mediaName":"Taken","mediaCode":"f3f0876a","appName":"测试安卓","appCode":"9e2db554d9d54715","appType":1,"appTypeName":"安卓","tagName":"句读","tagCode":"c31f9b24332c42b1_tech.caicheng.judourili","tagType":1,"tagTypeName":"信息流","bidTypeName":"分成","reqTotal":1,"reqInvalidTotal":0,"reqInvalidRate":0.00,"respFailTotal":0,"respFailRate":0.00,"participatingTotal":0,"participatingRate":0.00,"winTotal":0,"winRate":0,"amount":0.0000,"maxTime":10813,"avgTime":10813,"minTime":0,"event_1":null,"event_1Rate":0,"event_2":null,"event_2Rate":0,"event_3":null,"event_4":null,"event_5":null,"event_6":null,"event_7":null,"event_8":null,"event_9":null,"event_10":null,"event_11":null,"event_12":null,"event_13":null,"event_14":null,"event_15":null,"event_16":null,"event_17":null,"event_18":null,"event_19":null,"event_20":null,"event_21":null,"event_22":null,"event_23":null,"event_24":null,"event_25":null,"event_26":null,"event_27":null,"event_28":null,"event_29":null,"event_30":null,"event_31":null,"event_32":null,"event_33":null,"event_34":null,"event_35":null,"event_36":null,"event_37":null,"ecPm":0,"cpc":0}],"totalCount":1,"start":0,"limit":20,"currentPageNum":1,"totalPage":1}}]  SUCCESS
[2025-07-12 19:44:50.252] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:45:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:45:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:45:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:45:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:45:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:45:01.951] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:45:02.070] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:45:02.156] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:45:02.239] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:45:02.324] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:45:02.335] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:45:02.408] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:45:02.491] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:45:02.577] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:45:02.663] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:45:02.746] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:45:02.829] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:45:02.911] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:45:02.994] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:45:03.082] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:45:03.166] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:45:03.250] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:45:03.337] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:45:03.419] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:45:03.500] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:45:03.584] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:45:03.618] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:45:06.427] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/media/tag/list] stack[cn.taken.ad.api.oper.media.MediaTagApi.findList] time[2025-07-12 19:45:06 to 2025-07-12 19:45:06] params[req={"mediaAppId":null,"name":"c31f9b24332c42b1_tech.caicheng.judourili","mediaAppIds":null,"mediaIds":null} ; ] response[{"success":true,"code":"0","message":"success","result":[{"id":65,"name":"句读","code":"c31f9b24332c42b1_tech.caicheng.judourili","mediaId":3,"mediaAppId":8,"type":1,"timeout":700,"width":1080,"height":1920,"bidType":2,"pnyParam":"[]","operatorId":4,"mediaOperatorId":null,"createTime":"2025-03-29 12:00:00","needHttps":0,"materialHttps":false,"flowType":1,"settlementRatio":82.0,"filterOnOff":false,"filterUrlDomain":null,"filterMaterialRatio":0,"filterMaterialType":0,"filterActionType":0,"mediaBaseTagId":21}]}]  SUCCESS
[2025-07-12 19:45:06.625] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:45:08.095] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:45:10.037] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1944 : 0
[2025-07-12 19:45:10.038] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1944:0
[2025-07-12 19:45:14.405] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:45:16.040] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:45:17.922] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:45:20.044] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1944 : size 0
[2025-07-12 19:45:20.045] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1944:size 0
[2025-07-12 19:45:20.045] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1944:0
[2025-07-12 19:45:20.047] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1944 : 0
[2025-07-12 19:45:20.047] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1944:0
[2025-07-12 19:45:20.048] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1944:0
[2025-07-12 19:45:20.049] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1944:0
[2025-07-12 19:45:20.186] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1944:0
[2025-07-12 19:45:20.553] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/media/tag/list] stack[cn.taken.ad.api.oper.media.MediaTagApi.findList] time[2025-07-12 19:45:20 to 2025-07-12 19:45:20] params[req={"mediaAppId":null,"name":"c31f9b24332c42b1","mediaAppIds":null,"mediaIds":null} ; ] response[{"success":true,"code":"0","message":"success","result":[{"id":21,"name":"测试安卓-安卓-信息流-测试-分成","code":"c31f9b24332c42b1","mediaId":3,"mediaAppId":8,"type":1,"timeout":700,"width":1080,"height":1920,"bidType":2,"pnyParam":"[]","operatorId":4,"mediaOperatorId":null,"createTime":"2025-03-29 12:00:00","needHttps":0,"materialHttps":false,"flowType":1,"settlementRatio":82.0,"filterOnOff":false,"filterUrlDomain":null,"filterMaterialRatio":0,"filterMaterialType":0,"filterActionType":0,"mediaBaseTagId":null},{"id":16,"name":"测试IOS-iOS-信息流-测试-分成","code":"c31f9b24332c42b1","mediaId":3,"mediaAppId":7,"type":1,"timeout":700,"width":1920,"height":1080,"bidType":2,"pnyParam":"[]","operatorId":4,"mediaOperatorId":null,"createTime":"2025-03-29 12:00:00","needHttps":0,"materialHttps":false,"flowType":1,"settlementRatio":75.0,"filterOnOff":false,"filterUrlDomain":null,"filterMaterialRatio":0,"filterMaterialType":0,"filterActionType":0,"mediaBaseTagId":null}]}]  SUCCESS
[2025-07-12 19:45:26.486] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:45:28.309] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/statistics/media/media] stack[cn.taken.ad.api.oper.statistics.StatisticsMediaApi.mediaStatistics] time[2025-07-12 19:45:27 to 2025-07-12 19:45:28] params[req={"statisticsType":"DAY","beginTime":"20250705","endTime":"********","mediaTagId":21,"mediaId":null,"mediaAppId":null,"bidType":null,"start":0,"limit":20} ; ] response[{"success":true,"code":"0","message":"success","result":{"list":[],"totalCount":0,"start":0,"limit":20,"currentPageNum":1,"totalPage":0}}]  SUCCESS
[2025-07-12 19:45:33.625] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:45:33.753] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:45:33.838] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:45:33.925] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:45:34.010] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:45:34.096] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:45:34.179] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:45:34.266] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:45:34.350] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:45:34.436] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:45:34.523] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:45:34.612] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:45:34.696] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:45:34.783] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:45:34.870] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:45:34.955] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:45:35.043] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:45:35.126] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:45:35.209] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:45:35.296] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:45:35.339] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:45:38.196] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:45:38.563] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:45:40.081] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsHour,118] INFO  - statistics hour success ********19
[2025-07-12 19:45:40.100] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsHour,148] INFO  - statistics dsp hour success ********19
[2025-07-12 19:45:40.185] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:45:40.516] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 19:45:40.525] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.statisticsHour,143] INFO  - statistics dsp hour success ********19
[2025-07-12 19:45:40.536] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.statisticsHour,126] INFO  - statistics hour success ********19
[2025-07-12 19:45:40.544] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 19:45:40.579] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:45:40.594] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:45:40.827] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsHour,153] INFO  - statistics hour success ********19
[2025-07-12 19:45:40.840] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:45:40.928] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsHour,101] INFO  - statistics hour success ********19
[2025-07-12 19:45:40.928] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:45:41.065] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsHour,148] INFO  - statistics hour success ********19
[2025-07-12 19:45:46.120] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:45:47.991] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:45:50.641] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:46:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:46:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:46:01.002] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:46:01.002] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:46:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:46:02.726] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:46:05.353] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:46:05.475] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:46:05.554] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:46:05.632] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:46:05.711] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:46:05.792] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:46:05.871] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:46:05.951] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:46:06.032] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:46:06.111] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:46:06.190] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:46:06.271] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:46:06.348] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:46:06.425] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:46:06.503] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:46:06.588] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:46:06.664] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:46:06.744] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:46:06.823] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:46:06.899] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:46:06.932] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:46:08.288] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:46:08.858] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:46:10.037] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1945:0
[2025-07-12 19:46:10.037] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1945 : 0
[2025-07-12 19:46:14.813] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:46:16.198] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:46:18.090] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:46:20.038] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1945:0
[2025-07-12 19:46:20.039] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1945:0
[2025-07-12 19:46:20.039] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1945:0
[2025-07-12 19:46:20.040] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1945 : size 0
[2025-07-12 19:46:20.040] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1945:0
[2025-07-12 19:46:20.041] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1945:0
[2025-07-12 19:46:20.050] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1945 : 0
[2025-07-12 19:46:20.176] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1945:size 0
[2025-07-12 19:46:26.904] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:46:36.935] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:46:37.061] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:46:37.141] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:46:37.220] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:46:37.297] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:46:37.377] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:46:37.455] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:46:37.544] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:46:37.622] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:46:37.699] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:46:37.778] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:46:37.856] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:46:37.934] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:46:38.019] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:46:38.096] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:46:38.175] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:46:38.253] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:46:38.330] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:46:38.373] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:46:38.409] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:46:38.489] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:46:38.531] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:46:39.001] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:46:40.167] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:46:40.193] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:46:46.285] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:46:48.181] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:46:51.099] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:47:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:47:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:47:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:47:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:47:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:47:03.194] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:47:07.400] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:47:08.461] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:47:08.542] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:47:08.667] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:47:08.749] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:47:08.827] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:47:08.904] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:47:08.983] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:47:09.063] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:47:09.140] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:47:09.217] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:47:09.295] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:47:09.374] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:47:09.454] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:47:09.532] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:47:09.611] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:47:09.689] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:47:09.767] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:47:09.844] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:47:09.925] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:47:10.002] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:47:10.047] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1946:0
[2025-07-12 19:47:10.047] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1946 : 0
[2025-07-12 19:47:10.080] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:47:10.112] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:47:15.288] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:47:16.366] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:47:18.282] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:47:20.042] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1946:0
[2025-07-12 19:47:20.044] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1946:0
[2025-07-12 19:47:20.044] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1946:0
[2025-07-12 19:47:20.044] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1946:0
[2025-07-12 19:47:20.046] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1946 : 0
[2025-07-12 19:47:20.046] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1946:0
[2025-07-12 19:47:20.058] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1946:size 0
[2025-07-12 19:47:20.163] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1946 : size 0
[2025-07-12 19:47:27.388] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:47:38.558] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:47:39.466] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:47:40.116] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:47:40.158] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:47:40.170] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:47:40.241] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:47:40.335] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:47:40.427] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:47:40.518] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:47:40.609] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:47:40.701] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:47:40.794] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:47:40.889] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:47:40.980] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:47:41.076] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:47:41.169] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:47:41.260] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:47:41.354] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:47:41.448] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:47:41.539] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:47:41.636] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:47:41.729] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:47:41.819] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:47:41.914] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:47:41.948] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:47:46.447] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:47:48.356] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:47:51.542] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:48:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:48:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:48:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:48:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:48:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:48:03.639] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:48:08.646] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:48:09.172] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:48:10.036] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1947 : 0
[2025-07-12 19:48:10.046] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1947:0
[2025-07-12 19:48:11.961] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:48:12.097] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:48:12.192] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:48:12.283] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:48:12.376] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:48:12.470] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:48:12.562] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:48:12.655] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:48:12.746] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:48:12.835] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:48:12.928] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:48:13.021] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:48:13.113] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:48:13.206] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:48:13.297] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:48:13.387] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:48:13.478] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:48:13.571] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:48:13.662] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:48:13.755] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:48:13.797] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:48:15.756] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:48:16.531] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:48:18.455] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:48:20.043] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1947:0
[2025-07-12 19:48:20.043] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1947:0
[2025-07-12 19:48:20.043] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1947 : 0
[2025-07-12 19:48:20.044] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1947 : size 0
[2025-07-12 19:48:20.044] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1947:0
[2025-07-12 19:48:20.045] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1947:0
[2025-07-12 19:48:20.053] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1947:0
[2025-07-12 19:48:20.150] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1947:size 0
[2025-07-12 19:48:27.853] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:48:38.737] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:48:39.948] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:48:40.153] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:48:40.191] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:48:43.811] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:48:43.933] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:48:44.013] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:48:44.092] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:48:44.170] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:48:44.252] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:48:44.331] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:48:44.408] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:48:44.485] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:48:44.564] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:48:44.642] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:48:44.724] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:48:44.803] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:48:44.884] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:48:44.965] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:48:45.044] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:48:45.124] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:48:45.204] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:48:45.281] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:48:45.358] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:48:45.400] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:48:46.615] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:48:48.537] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:48:52.036] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:49:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:49:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:49:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:49:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:49:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:49:04.122] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:49:07.310] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:49:08.826] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:49:10.038] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1948 : 0
[2025-07-12 19:49:10.048] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1948:0
[2025-07-12 19:49:15.410] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:49:15.528] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:49:15.601] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:49:15.677] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:49:15.747] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:49:15.820] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:49:15.898] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:49:15.967] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:49:16.038] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:49:16.111] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:49:16.182] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:49:16.209] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:49:16.253] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:49:16.325] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:49:16.396] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:49:16.468] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:49:16.542] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:49:16.624] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:49:16.696] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:49:16.697] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:49:16.769] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:49:16.838] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:49:16.881] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:49:18.635] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:49:20.038] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1948:0
[2025-07-12 19:49:20.039] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1948 : 0
[2025-07-12 19:49:20.039] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1948:0
[2025-07-12 19:49:20.039] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1948:0
[2025-07-12 19:49:20.041] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1948:0
[2025-07-12 19:49:20.041] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1948 : size 0
[2025-07-12 19:49:20.050] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1948:size 0
[2025-07-12 19:49:20.134] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1948:0
[2025-07-12 19:49:28.303] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:49:38.914] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:49:40.174] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:49:40.207] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:49:40.402] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:49:46.776] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:49:46.883] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:49:47.005] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:49:47.086] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:49:47.166] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:49:47.245] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:49:47.325] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:49:47.403] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:49:47.482] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:49:47.561] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:49:47.646] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:49:47.723] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:49:47.800] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:49:47.878] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:49:47.956] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:49:48.043] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:49:48.120] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:49:48.198] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:49:48.281] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:49:48.361] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:49:48.440] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:49:48.482] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:49:48.736] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:49:52.494] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:50:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:50:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:50:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:50:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:50:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:50:04.592] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:50:08.597] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:50:09.002] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:50:10.051] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1949 : 0
[2025-07-12 19:50:10.057] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1949:0
[2025-07-12 19:50:16.673] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:50:16.883] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:50:18.489] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:50:18.813] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:50:18.914] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:50:18.960] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:50:19.053] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:50:19.142] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:50:19.232] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:50:19.324] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:50:19.418] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:50:19.509] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:50:19.601] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:50:19.695] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:50:19.787] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:50:19.880] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:50:19.975] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:50:20.039] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1949:0
[2025-07-12 19:50:20.040] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1949:0
[2025-07-12 19:50:20.040] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1949 : size 0
[2025-07-12 19:50:20.040] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1949:size 0
[2025-07-12 19:50:20.041] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1949:0
[2025-07-12 19:50:20.042] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1949:0
[2025-07-12 19:50:20.051] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1949 : 0
[2025-07-12 19:50:20.067] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:50:20.149] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1949:0
[2025-07-12 19:50:20.159] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:50:20.253] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:50:20.346] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:50:20.437] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:50:20.526] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:50:20.568] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:50:28.867] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:50:39.102] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:50:40.123] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsHour,118] INFO  - statistics hour success ********19
[2025-07-12 19:50:40.441] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:50:40.469] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsHour,148] INFO  - statistics dsp hour success ********19
[2025-07-12 19:50:40.483] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.statisticsHour,126] INFO  - statistics hour success ********19
[2025-07-12 19:50:40.488] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:50:40.502] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 19:50:40.527] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 19:50:40.577] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsHour,101] INFO  - statistics hour success ********19
[2025-07-12 19:50:40.578] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.statisticsHour,143] INFO  - statistics dsp hour success ********19
[2025-07-12 19:50:40.615] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:50:40.617] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:50:40.759] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsHour,148] INFO  - statistics hour success ********19
[2025-07-12 19:50:40.872] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:50:40.911] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsHour,153] INFO  - statistics hour success ********19
[2025-07-12 19:50:40.966] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:50:46.968] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:50:49.004] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:50:50.096] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsDay,101] INFO  - statistics day success ********
[2025-07-12 19:50:50.099] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsDay,82] INFO  - statistics day success ********
[2025-07-12 19:50:50.099] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.statisticsDay,109] INFO  - statistics day success ********
[2025-07-12 19:50:50.101] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsDay,131] INFO  - statistics dsp day success ********
[2025-07-12 19:50:50.102] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsDay,103] INFO  - statistics day success ********
[2025-07-12 19:50:50.110] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.statisticsDay,126] INFO  - statistics dsp day success ********
[2025-07-12 19:50:50.119] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsDay,103] INFO  - statistics day success ********
[2025-07-12 19:50:50.424] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsDay,82] INFO  - statistics day success ********
[2025-07-12 19:50:50.497] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsDay,131] INFO  - statistics day success ********
[2025-07-12 19:50:50.523] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsDay,82] INFO  - statistics day success ********
[2025-07-12 19:50:50.549] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsDay,136] INFO  - statistics day success ********
[2025-07-12 19:50:50.582] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:50:50.620] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsDay,84] INFO  - statistics day success ********
[2025-07-12 19:50:50.717] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:50:50.810] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:50:50.905] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:50:50.996] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:50:51.092] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:50:51.183] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:50:51.277] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:50:51.371] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:50:51.465] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:50:51.557] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:50:51.647] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:50:51.738] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:50:51.833] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:50:51.926] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:50:52.020] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:50:52.112] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:50:52.206] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:50:52.299] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:50:52.391] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:50:52.433] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:50:53.068] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:51:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:51:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:51:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:51:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:51:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:51:05.161] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:51:07.906] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:51:09.194] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:51:10.038] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1950 : 0
[2025-07-12 19:51:10.047] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1950:0
[2025-07-12 19:51:17.052] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:51:17.239] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:51:19.076] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:51:20.041] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1950:0
[2025-07-12 19:51:20.042] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1950 : 0
[2025-07-12 19:51:20.042] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1950:size 0
[2025-07-12 19:51:20.042] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1950:0
[2025-07-12 19:51:20.043] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1950:0
[2025-07-12 19:51:20.043] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1950 : size 0
[2025-07-12 19:51:20.045] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1950:0
[2025-07-12 19:51:20.054] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1950:0
[2025-07-12 19:51:22.442] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:51:22.568] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:51:22.649] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:51:22.728] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:51:22.809] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:51:22.889] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:51:22.968] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:51:23.047] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:51:23.127] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:51:23.204] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:51:23.286] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:51:23.367] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:51:23.447] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:51:23.527] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:51:23.607] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:51:23.686] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:51:23.766] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:51:23.849] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:51:23.931] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:51:24.009] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:51:24.051] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:51:29.327] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:51:39.296] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:51:40.168] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:51:40.191] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:51:41.427] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:51:47.135] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:51:49.155] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:51:53.506] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:51:54.065] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:51:54.177] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:51:54.255] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:51:54.339] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:51:54.417] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:51:54.496] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:51:54.574] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:51:54.650] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:51:54.730] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:51:54.807] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:51:54.885] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:51:54.965] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:51:55.043] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:51:55.119] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:51:55.197] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:51:55.280] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:51:55.358] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:51:55.437] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:51:55.515] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:51:55.591] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:51:55.624] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:52:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:52:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:52:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:52:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:52:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:52:05.596] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:52:08.155] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:52:09.396] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:52:10.041] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1951:0
[2025-07-12 19:52:10.051] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1951 : 0
[2025-07-12 19:52:17.215] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:52:17.686] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:52:19.249] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:52:20.045] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1951:0
[2025-07-12 19:52:20.046] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1951:0
[2025-07-12 19:52:20.046] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1951:0
[2025-07-12 19:52:20.046] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1951:0
[2025-07-12 19:52:20.047] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1951:0
[2025-07-12 19:52:20.047] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1951:size 0
[2025-07-12 19:52:20.048] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1951 : size 0
[2025-07-12 19:52:20.181] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1951 : 0
[2025-07-12 19:52:25.628] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:52:25.764] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:52:25.855] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:52:25.946] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:52:26.038] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:52:26.129] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:52:26.222] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:52:26.316] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:52:26.407] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:52:26.498] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:52:26.590] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:52:26.681] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:52:26.772] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:52:26.866] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:52:26.957] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:52:27.048] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:52:27.142] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:52:27.240] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:52:27.331] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:52:27.424] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:52:27.481] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:52:29.775] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:52:39.487] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:52:40.173] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:52:40.192] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:52:41.844] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:52:47.292] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:52:49.318] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:52:53.926] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:52:57.487] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:52:57.604] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:52:57.685] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:52:57.763] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:52:57.842] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:52:57.924] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:52:58.004] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:52:58.086] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:52:58.168] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:52:58.247] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:52:58.327] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:52:58.409] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:52:58.489] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:52:58.566] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:52:58.645] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:52:58.733] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:52:58.811] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:52:58.893] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:52:58.975] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:52:59.052] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:52:59.085] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:53:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:53:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:53:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:53:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:53:01.011] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:53:06.010] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:53:08.576] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:53:09.575] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:53:10.040] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1952:0
[2025-07-12 19:53:10.040] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1952 : 0
[2025-07-12 19:53:17.382] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:53:18.105] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:53:19.410] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:53:20.042] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1952:0
[2025-07-12 19:53:20.042] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1952:0
[2025-07-12 19:53:20.042] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1952:0
[2025-07-12 19:53:20.043] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1952 : size 0
[2025-07-12 19:53:20.044] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1952:size 0
[2025-07-12 19:53:20.044] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1952:0
[2025-07-12 19:53:20.053] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1952:0
[2025-07-12 19:53:20.169] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1952 : 0
[2025-07-12 19:53:29.095] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:53:29.217] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:53:29.295] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:53:29.374] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:53:29.452] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:53:29.531] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:53:29.610] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:53:29.688] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:53:29.765] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:53:29.855] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:53:29.930] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:53:30.008] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:53:30.086] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:53:30.162] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:53:30.200] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:53:30.241] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:53:30.318] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:53:30.396] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:53:30.476] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:53:30.557] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:53:30.636] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:53:30.671] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:53:39.670] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:53:40.173] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:53:40.175] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:53:42.288] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:53:47.459] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:53:49.478] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:53:54.357] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:54:00.677] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:54:00.801] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:54:00.880] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:54:00.967] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:54:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:54:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:54:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:54:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:54:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:54:01.044] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:54:01.124] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:54:01.203] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:54:01.280] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:54:01.359] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:54:01.436] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:54:01.511] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:54:01.590] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:54:01.668] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:54:01.746] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:54:01.826] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:54:01.904] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:54:01.981] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:54:02.060] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:54:02.139] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:54:02.217] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:54:02.260] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:54:06.424] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:54:07.860] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:54:09.757] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:54:10.039] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1953 : 0
[2025-07-12 19:54:10.048] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1953:0
[2025-07-12 19:54:17.545] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:54:18.501] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:54:19.575] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:54:20.039] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1953:0
[2025-07-12 19:54:20.040] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1953 : size 0
[2025-07-12 19:54:20.040] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1953:0
[2025-07-12 19:54:20.040] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1953:0
[2025-07-12 19:54:20.041] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1953:0
[2025-07-12 19:54:20.042] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1953 : 0
[2025-07-12 19:54:20.053] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1953:size 0
[2025-07-12 19:54:20.156] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1953:0
[2025-07-12 19:54:30.593] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:54:32.264] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:54:32.390] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:54:32.471] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:54:32.550] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:54:32.632] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:54:32.714] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:54:32.792] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:54:32.873] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:54:32.955] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:54:33.036] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:54:33.122] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:54:33.200] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:54:33.278] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:54:33.356] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:54:33.437] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:54:33.516] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:54:33.595] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:54:33.676] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:54:33.755] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:54:33.834] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:54:33.879] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:54:39.854] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:54:40.171] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:54:40.174] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:54:42.692] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:54:47.622] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:54:49.663] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:54:54.783] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:55:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:55:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:55:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:55:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:55:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:55:03.886] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:55:04.011] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:55:04.089] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:55:04.169] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:55:04.248] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:55:04.328] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:55:04.406] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:55:04.486] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:55:04.565] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:55:04.644] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:55:04.721] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:55:04.801] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:55:04.880] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:55:04.958] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:55:05.038] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:55:05.118] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:55:05.197] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:55:05.279] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:55:05.358] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:55:05.438] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:55:05.476] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:55:06.877] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:55:07.252] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:55:09.945] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:55:10.046] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1954:0
[2025-07-12 19:55:10.046] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1954 : 0
[2025-07-12 19:55:17.705] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:55:18.968] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:55:19.754] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:55:20.033] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1954 : 0
[2025-07-12 19:55:20.034] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1954:0
[2025-07-12 19:55:20.034] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1954:0
[2025-07-12 19:55:20.035] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1954:0
[2025-07-12 19:55:20.038] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1954:0
[2025-07-12 19:55:20.038] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1954 : size 0
[2025-07-12 19:55:20.038] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1954:0
[2025-07-12 19:55:20.156] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1954:size 0
[2025-07-12 19:55:31.061] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:55:35.477] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:55:35.603] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:55:35.681] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:55:35.759] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:55:35.834] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:55:35.915] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:55:35.992] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:55:36.070] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:55:36.149] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:55:36.228] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:55:36.307] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:55:36.386] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:55:36.465] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:55:36.541] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:55:36.621] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:55:36.699] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:55:36.776] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:55:36.856] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:55:36.937] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:55:37.016] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:55:37.058] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:55:40.045] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:55:40.088] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:55:40.101] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.statisticsHour,143] INFO  - statistics dsp hour success ********19
[2025-07-12 19:55:40.456] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:55:40.463] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 19:55:40.499] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsHour,118] INFO  - statistics hour success ********19
[2025-07-12 19:55:40.507] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:55:40.512] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 19:55:40.535] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsHour,148] INFO  - statistics dsp hour success ********19
[2025-07-12 19:55:40.538] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.statisticsHour,126] INFO  - statistics hour success ********19
[2025-07-12 19:55:40.593] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 19:55:40.607] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:55:40.893] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsHour,153] INFO  - statistics hour success ********19
[2025-07-12 19:55:40.912] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsHour,101] INFO  - statistics hour success ********19
[2025-07-12 19:55:41.017] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsHour,148] INFO  - statistics hour success ********19
[2025-07-12 19:55:43.149] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:55:47.784] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:55:49.824] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:55:55.218] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:56:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:56:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:56:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:56:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:56:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:56:07.067] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:56:07.191] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:56:07.280] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:56:07.296] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:56:07.367] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:56:07.454] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:56:07.545] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:56:07.632] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:56:07.721] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:56:07.810] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:56:07.898] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:56:07.988] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:56:08.084] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:56:08.172] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:56:08.264] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:56:08.353] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:56:08.393] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:56:08.440] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:56:08.530] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:56:08.619] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:56:08.709] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:56:08.800] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:56:08.842] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:56:10.045] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1955 : 0
[2025-07-12 19:56:10.052] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1955:0
[2025-07-12 19:56:10.140] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:56:17.866] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:56:19.383] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:56:19.911] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:56:20.039] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1955:0
[2025-07-12 19:56:20.040] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1955:0
[2025-07-12 19:56:20.041] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1955 : size 0
[2025-07-12 19:56:20.041] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1955:0
[2025-07-12 19:56:20.042] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1955:0
[2025-07-12 19:56:20.042] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1955:size 0
[2025-07-12 19:56:20.042] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1955:0
[2025-07-12 19:56:20.139] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1955 : 0
[2025-07-12 19:56:31.481] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:56:38.854] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:56:38.990] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:56:39.085] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:56:39.172] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:56:39.260] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:56:39.354] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:56:39.444] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:56:39.534] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:56:39.620] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:56:39.709] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:56:39.801] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:56:39.889] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:56:39.978] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:56:40.067] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:56:40.165] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:56:40.169] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:56:40.171] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:56:40.231] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:56:40.254] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:56:40.347] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:56:40.434] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:56:40.523] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:56:40.614] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:56:40.657] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:56:43.570] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:56:47.942] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:56:49.992] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:56:55.648] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:57:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:57:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:57:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:57:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:57:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:57:07.725] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:57:08.464] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:57:10.044] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1956 : 0
[2025-07-12 19:57:10.053] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1956:0
[2025-07-12 19:57:10.323] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:57:10.663] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:57:10.797] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:57:10.889] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:57:10.978] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:57:11.067] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:57:11.155] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:57:11.244] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:57:11.336] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:57:11.425] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:57:11.514] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:57:11.603] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:57:11.691] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:57:11.780] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:57:11.870] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:57:11.962] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:57:12.049] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:57:12.140] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:57:12.227] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:57:12.320] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:57:12.411] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:57:12.453] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:57:18.016] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:57:19.807] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:57:20.038] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1956:size 0
[2025-07-12 19:57:20.052] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1956:0
[2025-07-12 19:57:20.052] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1956:0
[2025-07-12 19:57:20.052] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1956 : size 0
[2025-07-12 19:57:20.052] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1956 : 0
[2025-07-12 19:57:20.052] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1956:0
[2025-07-12 19:57:20.092] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:57:20.160] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1956:0
[2025-07-12 19:57:20.174] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1956:0
[2025-07-12 19:57:31.888] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:57:40.172] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:57:40.188] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:57:40.410] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:57:42.469] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:57:42.592] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:57:42.681] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:57:42.769] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:57:42.859] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:57:42.950] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:57:43.038] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:57:43.128] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:57:43.215] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:57:43.304] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:57:43.394] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:57:43.483] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:57:43.573] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:57:43.668] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:57:43.759] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:57:43.846] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:57:43.937] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:57:43.957] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:57:44.025] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:57:44.115] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:57:44.205] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:57:44.239] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:57:48.095] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:57:50.162] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:57:56.030] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:58:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:58:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:58:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:58:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:58:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:58:07.941] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:58:08.108] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:58:10.047] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1957:0
[2025-07-12 19:58:10.049] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1957 : 0
[2025-07-12 19:58:10.506] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:58:14.253] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:58:14.387] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:58:14.479] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:58:14.566] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:58:14.656] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:58:14.744] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:58:14.834] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:58:14.923] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:58:15.011] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:58:15.099] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:58:15.188] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:58:15.281] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:58:15.369] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:58:15.459] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:58:15.549] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:58:15.639] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:58:15.730] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:58:15.821] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:58:15.910] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:58:16.000] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:58:16.043] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:58:18.183] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:58:20.042] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1957:0
[2025-07-12 19:58:20.043] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1957:0
[2025-07-12 19:58:20.043] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1957 : size 0
[2025-07-12 19:58:20.045] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1957:size 0
[2025-07-12 19:58:20.045] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1957:0
[2025-07-12 19:58:20.045] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1957:0
[2025-07-12 19:58:20.218] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:58:20.288] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1957 : 0
[2025-07-12 19:58:20.288] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:58:20.288] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1957:0
[2025-07-12 19:58:32.339] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:58:40.175] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:58:40.192] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:58:40.595] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:58:44.425] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:58:46.057] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:58:46.167] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:58:46.240] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:58:46.322] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:58:46.393] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:58:46.467] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:58:46.540] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:58:46.613] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:58:46.684] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:58:46.760] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:58:46.830] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:58:46.901] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:58:46.974] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:58:47.045] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:58:47.118] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:58:47.192] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:58:47.262] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:58:47.334] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:58:47.413] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:58:47.483] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:58:47.519] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:58:48.259] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:58:50.373] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:58:56.513] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:59:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 19:59:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 19:59:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 19:59:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 19:59:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 19:59:07.094] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 19:59:08.588] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:59:10.041] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1958:0
[2025-07-12 19:59:10.043] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1958 : 0
[2025-07-12 19:59:10.686] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:59:17.523] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:59:17.633] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:59:17.707] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:59:17.780] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:59:17.852] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:59:17.925] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:59:18.002] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:59:18.082] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:59:18.155] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:59:18.228] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:59:18.299] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:59:18.333] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:59:18.369] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:59:18.441] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:59:18.513] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:59:18.584] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:59:18.657] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:59:18.729] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:59:18.801] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:59:18.874] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:59:18.947] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:59:18.982] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:59:20.040] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1958:0
[2025-07-12 19:59:20.041] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1958:0
[2025-07-12 19:59:20.041] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1958 : 0
[2025-07-12 19:59:20.041] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1958 : size 0
[2025-07-12 19:59:20.043] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1958:0
[2025-07-12 19:59:20.043] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1958:0
[2025-07-12 19:59:20.053] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1958:size 0
[2025-07-12 19:59:20.178] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1958:0
[2025-07-12 19:59:20.473] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:59:20.673] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:59:32.761] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:59:40.179] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 19:59:40.205] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 19:59:40.781] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 19:59:44.858] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 19:59:48.420] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 19:59:48.986] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 19:59:49.118] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 19:59:49.205] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 19:59:49.292] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 19:59:49.381] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 19:59:49.470] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 19:59:49.565] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 19:59:49.657] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 19:59:49.745] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 19:59:49.834] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 19:59:49.925] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 19:59:50.012] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 19:59:50.102] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 19:59:50.192] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 19:59:50.281] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 19:59:50.371] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 19:59:50.464] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 19:59:50.557] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 19:59:50.559] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 19:59:50.647] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 19:59:50.738] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 19:59:50.785] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 19:59:56.951] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:00:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 20:00:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 20:00:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 20:00:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 20:00:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 20:00:07.426] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 20:00:09.040] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:00:10.047] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********1959 : 0
[2025-07-12 20:00:10.047] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********1959:0
[2025-07-12 20:00:10.871] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 20:00:18.496] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 20:00:20.032] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********1959 : 0
[2025-07-12 20:00:20.032] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********1959:size 0
[2025-07-12 20:00:20.033] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1959:0
[2025-07-12 20:00:20.033] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********1959:0
[2025-07-12 20:00:20.033] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1959:0
[2025-07-12 20:00:20.036] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********1959 : size 0
[2025-07-12 20:00:20.043] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********1959:0
[2025-07-12 20:00:20.174] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********1959:0
[2025-07-12 20:00:20.647] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 20:00:20.790] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 20:00:20.916] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 20:00:20.995] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 20:00:21.079] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 20:00:21.136] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:00:21.158] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 20:00:21.236] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 20:00:21.319] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 20:00:21.399] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 20:00:21.479] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 20:00:21.560] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 20:00:21.640] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 20:00:21.721] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 20:00:21.803] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 20:00:21.884] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 20:00:21.963] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 20:00:22.043] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 20:00:22.124] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 20:00:22.204] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 20:00:22.289] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 20:00:22.368] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 20:00:22.411] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 20:00:33.227] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:00:40.081] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 20:00:40.088] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.statisticsHour,143] INFO  - statistics dsp hour success ********19
[2025-07-12 20:00:40.099] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.statisticsHour,126] INFO  - statistics hour success ********19
[2025-07-12 20:00:40.150] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsHour,120] INFO  - statistics hour success ********20
[2025-07-12 20:00:40.167] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.statisticsHour,143] INFO  - statistics dsp hour success ********20
[2025-07-12 20:00:40.187] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.statisticsHour,126] INFO  - statistics hour success ********20
[2025-07-12 20:00:40.424] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 20:00:40.463] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsHour,118] INFO  - statistics hour success ********19
[2025-07-12 20:00:40.474] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsHour,120] INFO  - statistics hour success ********19
[2025-07-12 20:00:40.493] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsHour,99] INFO  - statistics hour success ********20
[2025-07-12 20:00:40.525] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsHour,148] INFO  - statistics dsp hour success ********19
[2025-07-12 20:00:40.535] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsHour,118] INFO  - statistics hour success ********20
[2025-07-12 20:00:40.546] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsHour,120] INFO  - statistics hour success ********20
[2025-07-12 20:00:40.603] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 20:00:40.603] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsHour,148] INFO  - statistics dsp hour success ********20
[2025-07-12 20:00:40.608] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 20:00:40.746] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 20:00:40.788] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsHour,148] INFO  - statistics hour success ********19
[2025-07-12 20:00:40.811] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********20
[2025-07-12 20:00:40.827] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********19
[2025-07-12 20:00:40.857] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsHour,148] INFO  - statistics hour success ********20
[2025-07-12 20:00:40.872] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsHour,101] INFO  - statistics hour success ********19
[2025-07-12 20:00:40.897] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsHour,99] INFO  - statistics hour success ********20
[2025-07-12 20:00:40.922] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsHour,153] INFO  - statistics hour success ********19
[2025-07-12 20:00:40.949] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsHour,101] INFO  - statistics hour success ********20
[2025-07-12 20:00:40.968] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 20:00:41.001] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsHour,153] INFO  - statistics hour success ********20
[2025-07-12 20:00:45.313] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:00:48.575] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 20:00:50.086] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.statisticsDay,126] INFO  - statistics dsp day success ********
[2025-07-12 20:00:50.087] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsDay,103] INFO  - statistics day success ********
[2025-07-12 20:00:50.087] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsDay,103] INFO  - statistics day success ********
[2025-07-12 20:00:50.088] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsDay,82] INFO  - statistics day success ********
[2025-07-12 20:00:50.091] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.statisticsDay,109] INFO  - statistics day success ********
[2025-07-12 20:00:50.094] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsDay,131] INFO  - statistics dsp day success ********
[2025-07-12 20:00:50.094] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsDay,101] INFO  - statistics day success ********
[2025-07-12 20:00:50.400] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsDay,82] INFO  - statistics day success ********
[2025-07-12 20:00:50.433] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsDay,82] INFO  - statistics day success ********
[2025-07-12 20:00:50.478] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsDay,136] INFO  - statistics day success ********
[2025-07-12 20:00:50.510] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsDay,131] INFO  - statistics day success ********
[2025-07-12 20:00:50.580] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsDay,84] INFO  - statistics day success ********
[2025-07-12 20:00:50.743] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 20:00:52.414] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 20:00:52.550] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 20:00:52.640] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 20:00:52.730] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 20:00:52.819] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 20:00:52.912] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 20:00:53.006] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 20:00:53.096] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 20:00:53.186] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 20:00:53.272] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 20:00:53.360] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 20:00:53.456] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 20:00:53.543] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 20:00:53.632] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 20:00:53.722] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 20:00:53.811] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 20:00:53.898] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 20:00:53.987] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 20:00:54.075] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 20:00:54.164] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 20:00:54.207] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 20:00:57.403] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:01:00.003] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,66] INFO  - start upload chunk file
[2025-07-12 20:01:00.012] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,103] INFO  - finished upload chunk file
[2025-07-12 20:01:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 20:01:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 20:01:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-12 20:01:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 20:01:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-12 20:01:08.443] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 20:01:09.496] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:01:10.049] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********2000:0
[2025-07-12 20:01:10.051] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********2000 : 0
[2025-07-12 20:01:11.057] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 20:01:18.658] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 20:01:20.041] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********2000:size 0
[2025-07-12 20:01:20.042] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********2000:0
[2025-07-12 20:01:20.043] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********2000:0
[2025-07-12 20:01:20.043] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********2000 : 0
[2025-07-12 20:01:20.044] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********2000:0
[2025-07-12 20:01:20.047] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********2000 : size 0
[2025-07-12 20:01:20.053] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********2000:0
[2025-07-12 20:01:20.180] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********2000:0
[2025-07-12 20:01:20.840] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 20:01:21.583] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:01:24.211] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 20:01:24.335] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 20:01:24.414] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 20:01:24.495] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 20:01:24.571] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 20:01:24.649] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 20:01:24.725] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 20:01:24.811] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 20:01:24.889] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 20:01:24.965] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 20:01:25.042] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 20:01:25.120] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 20:01:25.198] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 20:01:25.276] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 20:01:25.357] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 20:01:25.435] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 20:01:25.517] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 20:01:25.596] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 20:01:25.677] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 20:01:25.753] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 20:01:25.796] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 20:01:33.669] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:01:40.157] [cn.taken.ad.task.statistics.StatisticsDspAdEventTask.minute,64] INFO  - dsp ad event minute finished 0 
[2025-07-12 20:01:40.176] [cn.taken.ad.task.statistics.StatisticsDspAdRequestTask.minute,80] INFO  - dsp ad request minute finished 0
[2025-07-12 20:01:41.153] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 20:01:45.773] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:01:48.735] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 20:01:50.940] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 20:01:55.807] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,89] INFO  - cache base data to redis begin
[2025-07-12 20:01:55.943] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseIndustry total:0
[2025-07-12 20:01:56.034] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseProvince total:0
[2025-07-12 20:01:56.123] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute BaseCity total:0
[2025-07-12 20:01:56.217] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaProtocol total:0
[2025-07-12 20:01:56.306] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Media total:0
[2025-07-12 20:01:56.396] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaApp total:0
[2025-07-12 20:01:56.487] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute MediaTag total:0
[2025-07-12 20:01:56.575] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Advertiser total:0
[2025-07-12 20:01:56.662] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserApp total:0
[2025-07-12 20:01:56.753] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserTag total:0
[2025-07-12 20:01:56.844] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute AdvertiserProtocol total:0
[2025-07-12 20:01:56.932] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute Strategy total:0
[2025-07-12 20:01:57.021] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute StrategyTagAdvertiser total:0
[2025-07-12 20:01:57.110] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserApp total:0
[2025-07-12 20:01:57.195] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserResource total:0
[2025-07-12 20:01:57.283] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAd total:0
[2025-07-12 20:01:57.371] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DspAdvertiserAccount total:0
[2025-07-12 20:01:57.461] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute DmpPackage total:0
[2025-07-12 20:01:57.550] [cn.taken.ad.task.cache.CacheBaseDataToTask.execute,405] INFO  - cache base data execute SystemSettings total:0
[2025-07-12 20:01:57.593] [cn.taken.ad.task.cache.CacheBaseDataToTask.cache,121] INFO  - cache base data to redis end
[2025-07-12 20:01:57.869] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:02:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-12 20:02:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-12 20:02:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-12 20:02:01.134] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"41-134-3-8-65-1-1-1":{"id":null,"statisticsType":"MINUTE","statisticsTime":"********2001","strategyId":41,"strategyTagAdvId":134,"mediaId":3,"mediaAppId":8,"mediaTagId":65,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":6701,"mediaAvgTime":6701,"mediaMinTime":0,"mediaUseTimeTotal":6701,"advertiserId":1,"advertiserAppId":1,"advertiserTagId":1,"advertiserReqTotal":1,"advertiserReqSuccessTotal":0,"advertiserReqFailTotal":1,"advertiserRespFailTotal":1,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":2067,"advertiserAvgTime":2067,"advertiserMinTime":0,"advertiserUseTimeTotal":2067}},minuteMediaReq:{"41-3-8-65":{"id":null,"statisticsType":"MINUTE","statisticsTime":"********2001","mediaId":3,"mediaAppId":8,"mediaTagId":65,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":41,"maxTime":6701,"avgTime":6701,"minTime":0,"useTimeTotal":6701}},minuteAdvReq:{"1-1-1":{"id":null,"statisticsType":"MINUTE","statisticsTime":"********2001","advertiserId":1,"advertiserAppId":1,"advertiserTagId":1,"reqTotal":1,"reqSuccessTotal":0,"reqFailTotal":1,"respFailTotal":1,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":2067,"avgTime":2067,"minTime":0,"useTimeTotal":2067}}
[2025-07-12 20:02:01.219] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"41-3-8-65-FAIL_ADV":{"id":null,"statisticsType":"MINUTE","statisticsTime":"********2001","mediaId":3,"mediaAppId":8,"mediaTagId":65,"code":"FAIL_ADV","total":1,"strategyId":41}},advertiserErrorCode:{"1-1-1-HTTP/1.1 600 http请求异常":{"id":null,"statisticsType":"MINUTE","statisticsTime":"********2001","advertiserId":1,"advertiserAppId":1,"advertiserTagId":1,"code":"HTTP/1.1 600 http请求异常","total":1}}
[2025-07-12 20:02:07.731] [cn.taken.ad.task.dsp.DspCloseAdTask.checkClose,126] INFO  - Need openCodes Ids:[4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57]
[2025-07-12 20:02:09.956] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:02:10.038] [cn.taken.ad.task.statistics.StatisticsDspAdvAdEventTask.statisticsMinute,84] INFO  - statistics event minute success ********2001:0
[2025-07-12 20:02:10.040] [cn.taken.ad.task.statistics.StatisticsDspAdvAdRequestTask.statisticsMinute,85] INFO  -  dsp adv request statistics request minute success ********2001 : 0
[2025-07-12 20:02:11.246] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 20:02:18.821] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 20:02:20.036] [cn.taken.ad.task.statistics.StatisticsAdvertiserEventTask.statisticsMinute,86] INFO  - statistics event minute success ********2001:0
[2025-07-12 20:02:20.038] [cn.taken.ad.task.statistics.StatisticsMediaEventTask.statisticsMinute,86] INFO  - statistics event minute success ********2001:0
[2025-07-12 20:02:20.038] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserEventTask.statisticsMinute,123] INFO  - statistics event minute success ********2001:0
[2025-07-12 20:02:20.272] [cn.taken.ad.task.statistics.StatisticsAdvertiserRequestTask.statisticsMinute,90] INFO  -  adv request statistics request minute success ********2001 : 1
[2025-07-12 20:02:20.288] [cn.taken.ad.task.statistics.StatisticsMediaErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********2001:1
[2025-07-12 20:02:20.318] [cn.taken.ad.task.statistics.StatisticsMediaAdvertiserRequestTask.statisticsMinute,181] INFO  - media adv request statistics minute success ********2001 : size 1
[2025-07-12 20:02:20.690] [cn.taken.ad.task.statistics.StatisticsMediaRequestTask.statisticsMinute,86] INFO  - media request statistics minute success ********2001:size 1
[2025-07-12 20:02:20.943] [cn.taken.ad.task.statistics.StatisticsAdvertiserErrorCodeTask.statisticsMinute,119] INFO  - statistics event minute success ********2001:1
[2025-07-12 20:02:21.032] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
[2025-07-12 20:02:22.043] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.lock,31] INFO  - expire node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:02:23.423] [cn.taken.ad.configuration.lb.HSLoadBalanceListener.stop,94] INFO  - not need change load balance
[2025-07-12 20:02:23.423] [cn.taken.ad.component.superscheduler.SuperScheduler.stop,130] INFO  - super-scheduler stopping
[2025-07-12 20:02:23.423] [org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.shutdown,208] INFO  - Shutting down ExecutorService
[2025-07-12 20:02:23.431] [cn.taken.ad.component.superscheduler.SuperScheduler.stop,140] INFO  - super-scheduler stopped
[2025-07-12 20:02:23.440] [cn.taken.ad.component.superscheduler.SuperScheduler.stop,130] INFO  - super-scheduler stopping
[2025-07-12 20:02:23.441] [org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.shutdown,208] INFO  - Shutting down ExecutorService
[2025-07-12 20:02:23.441] [org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.shutdown,208] INFO  - Shutting down ExecutorService
[2025-07-12 20:02:23.491] [cn.taken.ad.configuration.server.ServerInfoManager.unsubscribe,87] INFO  - unsubscribe service server WEB , 2f5586410d4f476985391564fcaf8f0f , 63
[2025-07-12 20:02:23.501] [cn.taken.ad.configuration.server.ServerInfoManager.unsubscribe,87] INFO  - unsubscribe service server RTB , 067c360df37b4b9293c2ef05f5b259e6 , 69
[2025-07-12 20:02:23.584] [cn.taken.ad.component.superscheduler.SuperScheduler.stop,137] INFO  - super-scheduler unlock by 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:02:23.626] [cn.taken.ad.component.superscheduler.lock.impl.RedisOnlyLockHandler.unLock,47] INFO  - unlock node 6f2d4522ea5f4560a975c01fe42c8ea5
[2025-07-12 20:02:23.668] [cn.taken.ad.component.superscheduler.SuperScheduler.stop,140] INFO  - super-scheduler stopped
[2025-07-12 20:02:23.723] [cn.taken.ad.configuration.server.ServerInfoManager.unsubscribe,87] INFO  - unsubscribe service server BUSINESS , 6f2d4522ea5f4560a975c01fe42c8ea5 , 70
