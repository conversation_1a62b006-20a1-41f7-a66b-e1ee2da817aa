[2025-07-12 19:35:57.326] [org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions,142] ERROR - Data truncation: Data too long for column 'code' at row 1
[2025-07-12 19:36:04.821] [cn.taken.ad.task.media.MediaPackageTagToDBTask.lambda$exec$1,91] ERROR - could not execute statement; SQL [n/a]; nested exception is org.hibernate.exception.DataException: could not execute statement
org.springframework.dao.DataIntegrityViolationException: could not execute statement; SQL [n/a]; nested exception is org.hibernate.exception.DataException: could not execute statement
	at org.springframework.orm.hibernate5.SessionFactoryUtils.convertHibernateAccessException(SessionFactoryUtils.java:249) ~[spring-orm-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.orm.hibernate5.HibernateTemplate.doExecute(HibernateTemplate.java:388) ~[spring-orm-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.orm.hibernate5.HibernateTemplate.executeWithNativeSession(HibernateTemplate.java:351) ~[spring-orm-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.orm.hibernate5.HibernateTemplate.save(HibernateTemplate.java:638) ~[spring-orm-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at cn.taken.ad.component.orm.AbstractDaoSupport.save(AbstractDaoSupport.java:86) ~[classes/:?]
	at cn.taken.ad.component.orm.AbstractPojoDaoSupport.save(AbstractPojoDaoSupport.java:41) ~[classes/:?]
	at cn.taken.ad.component.orm.AbstractPojoDaoSupport$$FastClassBySpringCGLIB$$1682fef9.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:752) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:139) ~[spring-tx-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at cn.taken.ad.core.dao.media.impl.MediaTagDaoImpl$$EnhancerBySpringCGLIB$$4a8a5353.save(<generated>) ~[classes/:?]
	at cn.taken.ad.core.service.media.impl.MediaTagServiceImpl.saveMediaTagAndStrategy(MediaTagServiceImpl.java:406) ~[classes/:?]
	at cn.taken.ad.core.service.media.impl.MediaTagServiceImpl$$FastClassBySpringCGLIB$$a0235142.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:752) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:295) ~[spring-tx-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98) ~[spring-tx-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at cn.taken.ad.core.service.media.impl.MediaTagServiceImpl$$EnhancerBySpringCGLIB$$7b8f41e.saveMediaTagAndStrategy(<generated>) ~[classes/:?]
	at cn.taken.ad.task.media.MediaPackageTagToDBTask.lambda$exec$1(MediaPackageTagToDBTask.java:85) ~[classes/:?]
	at java.lang.Iterable.forEach(Iterable.java:75) ~[?:1.8.0_152]
	at cn.taken.ad.task.media.MediaPackageTagToDBTask.exec(MediaPackageTagToDBTask.java:78) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_152]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_152]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_152]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_152]
	at cn.taken.ad.component.superscheduler.SuperExecutor.invokeMethod(SuperExecutor.java:340) ~[classes/:?]
	at cn.taken.ad.component.superscheduler.task.generate.FixedDelayTaskGenerate.lambda$genRunnable$0(FixedDelayTaskGenerate.java:77) ~[classes/:?]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93) [spring-context-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_152]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) [?:1.8.0_152]
	at java.util.concurrent.FutureTask.run(FutureTask.java) [?:1.8.0_152]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_152]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_152]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_152]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_152]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_152]
Caused by: org.hibernate.exception.DataException: could not execute statement
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:52) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:42) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:113) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:99) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:178) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.dialect.identity.GetGeneratedKeysDelegate.executeAndExtract(GetGeneratedKeysDelegate.java:57) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.id.insert.AbstractReturningDelegate.performInsert(AbstractReturningDelegate.java:42) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:3090) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:3683) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:645) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:282) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:263) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:317) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:332) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:289) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:196) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:127) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.DefaultSaveOrUpdateEventListener.saveWithGeneratedOrRequestedId(DefaultSaveOrUpdateEventListener.java:192) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.DefaultSaveEventListener.saveWithGeneratedOrRequestedId(DefaultSaveEventListener.java:38) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.DefaultSaveOrUpdateEventListener.entityIsTransient(DefaultSaveOrUpdateEventListener.java:177) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.DefaultSaveEventListener.performSaveOrUpdate(DefaultSaveEventListener.java:32) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.DefaultSaveOrUpdateEventListener.onSaveOrUpdate(DefaultSaveOrUpdateEventListener.java:73) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.internal.SessionImpl.fireSave(SessionImpl.java:713) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.internal.SessionImpl.save(SessionImpl.java:705) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.internal.SessionImpl.save(SessionImpl.java:700) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.springframework.orm.hibernate5.HibernateTemplate.lambda$save$11(HibernateTemplate.java:640) ~[spring-orm-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.orm.hibernate5.HibernateTemplate.doExecute(HibernateTemplate.java:385) ~[spring-orm-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	... 41 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'code' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1092) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1040) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1347) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:1025) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_152]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_152]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_152]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_152]
	at org.apache.tomcat.jdbc.pool.StatementFacade$StatementProxy.invoke(StatementFacade.java:114) ~[tomcat-jdbc-9.0.36.jar:?]
	at com.sun.proxy.$Proxy89.executeUpdate(Unknown Source) ~[?:?]
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:175) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.dialect.identity.GetGeneratedKeysDelegate.executeAndExtract(GetGeneratedKeysDelegate.java:57) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.id.insert.AbstractReturningDelegate.performInsert(AbstractReturningDelegate.java:42) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:3090) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:3683) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:645) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:282) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:263) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:317) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:332) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:289) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:196) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:127) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.DefaultSaveOrUpdateEventListener.saveWithGeneratedOrRequestedId(DefaultSaveOrUpdateEventListener.java:192) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.DefaultSaveEventListener.saveWithGeneratedOrRequestedId(DefaultSaveEventListener.java:38) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.DefaultSaveOrUpdateEventListener.entityIsTransient(DefaultSaveOrUpdateEventListener.java:177) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.DefaultSaveEventListener.performSaveOrUpdate(DefaultSaveEventListener.java:32) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.event.internal.DefaultSaveOrUpdateEventListener.onSaveOrUpdate(DefaultSaveOrUpdateEventListener.java:73) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.internal.SessionImpl.fireSave(SessionImpl.java:713) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.internal.SessionImpl.save(SessionImpl.java:705) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.hibernate.internal.SessionImpl.save(SessionImpl.java:700) ~[hibernate-core-5.3.17.Final.jar:5.3.17.Final]
	at org.springframework.orm.hibernate5.HibernateTemplate.lambda$save$11(HibernateTemplate.java:640) ~[spring-orm-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.orm.hibernate5.HibernateTemplate.doExecute(HibernateTemplate.java:385) ~[spring-orm-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	... 41 more
