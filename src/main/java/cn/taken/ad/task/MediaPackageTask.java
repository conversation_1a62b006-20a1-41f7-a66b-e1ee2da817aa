package cn.taken.ad.task;

import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.configuration.monitor.MediaPackageMonitor;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.core.pojo.media.MediaPackage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Component
public class MediaPackageTask {

    @Autowired
    private BaseRedisL2Cache l2Cache;

    @Resource
    private RedisClient redisClient;

    @Resource
    private MediaPackageMonitor mediaPackageMonitor;

    @SuperScheduled(cron = "0 0/1 * * * ?")
    public void execute() {
        Map<Long, Map<String, String>> map = mediaPackageMonitor.getTempQueue();
        Iterator<Map.Entry<Long, Map<String, String>>> it = map.entrySet().iterator();
        List<String> values = new LinkedList<>();
        while (it.hasNext()) {
            Map.Entry<Long, Map<String, String>> entry = it.next();
            it.remove();
            Long tagId = entry.getKey();
            entry.getValue().forEach((k, v) -> {
                MediaPackage old = l2Cache.get(BaseRedisKeys.KV_MEDIA_PACKAGE_NAME_ + tagId + "_" + k, MediaPackage.class);
                if (old == null) {
                    values.add(v + "_" + tagId + "_" + k);
                }
            });
            if (values.size() == 200) {
                redisClient.lpush(BaseRedisKeys.QUEUE_MEDIA_PACK, -1, values);
                values.clear();
            }
        }
        if (!values.isEmpty()) {
            redisClient.lpush(BaseRedisKeys.QUEUE_MEDIA_PACK, -1, values);
        }
    }
}
