package cn.taken.ad.task.statistics;

import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserEvent;
import cn.taken.ad.core.service.statistics.StatisticsMediaPackageAdvertiserEventService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public class StatisticsMediaPackageAdvertiserEventTask {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Resource(name = "BaseRedis")
    private RedisClient redis;

    @Resource
    private StatisticsMediaPackageAdvertiserEventService statisticsMediaAdvertiserEventService;

    @SuperScheduled(cron = "20 */1 * * * ?", only = true)
    public void statistics() {
        Date statisticsDate = new Date(System.currentTimeMillis() - (60L * 1000L));
        this.statisticsMinute(statisticsDate);
    }

    @SuperScheduled(cron = "40 0/5 * * * ?", only = true)
    public void hour() {
        Date statisticsDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(statisticsDate);
        if (c.get(Calendar.MINUTE) < 9) {
            // 统计上一个小时的数据
            c.add(Calendar.HOUR_OF_DAY, -1);
            this.statisticsHour(c.getTime());
        }
        //当前小时
        this.statisticsHour(statisticsDate);
    }

    @SuperScheduled(cron = "50 0/10 * * * ?", only = true)
    public void day() {
        Date statisticsDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(statisticsDate);
        if (c.get(Calendar.HOUR_OF_DAY) == 0 && c.get(Calendar.MINUTE) < 20) {
            // 统计昨天的数据
            c.add(Calendar.DAY_OF_MONTH, -1);
            this.statisticsDay(c.getTime());
        }
        //当前天的
        this.statisticsDay(statisticsDate);
    }

    private void statisticsDay(Date statisticsDate) {
        String day = DateUtils.toString(statisticsDate, StatisticsType.DAY.getFormat());
        String beginTime = day + "00";
        String endTime = day + "23";

        List<StatisticsMediaPackageAdvertiserEvent> eventList = statisticsMediaAdvertiserEventService.findStatistics(beginTime, endTime, StatisticsType.HOUR);
        if (!eventList.isEmpty()) {
            eventList.forEach(event -> {
                event.setStatisticsType(StatisticsType.DAY.getCode());
                event.setStatisticsTime(day);
            });
            statisticsMediaAdvertiserEventService.deleteByTime(StatisticsType.DAY, day);
            statisticsMediaAdvertiserEventService.saveList(eventList);
        }
        log.info("statistics day success {}", day);
    }

    private void statisticsHour(Date statisticsDate) {
        String hour = DateUtils.toString(statisticsDate, StatisticsType.HOUR.getFormat());
        String beginTime = hour + "00";
        String endTime = hour + "59";

        List<StatisticsMediaPackageAdvertiserEvent> eventList = statisticsMediaAdvertiserEventService.findStatistics(beginTime, endTime, StatisticsType.MINUTE);
        if (!eventList.isEmpty()) {
            eventList.forEach(event -> {
                event.setStatisticsType(StatisticsType.HOUR.getCode());
                event.setStatisticsTime(hour);
            });
            statisticsMediaAdvertiserEventService.deleteByTime(StatisticsType.HOUR, hour);
            statisticsMediaAdvertiserEventService.saveList(eventList);
        }
        log.info("statistics hour success {}", hour);
    }

    private void statisticsMinute(Date statisticsDate) {
        String minute = DateUtils.toString(statisticsDate, StatisticsType.MINUTE.getFormat());
        Map<String, StatisticsMediaPackageAdvertiserEvent> eventMap = new HashMap<>();
        for (; ; ) {
            List<StatisticsMediaPackageAdvertiserEvent> eventList = redis.rpop(BaseRedisKeys.QUEUE_STATISTICS_MEDIA_PACK_ADV_EVENT, 100, StatisticsMediaPackageAdvertiserEvent.class);
            if (CollectionUtils.isEmpty(eventList)) {
                break;
            }
            for (StatisticsMediaPackageAdvertiserEvent eventDto : eventList) {
                String mediaAdvKey = eventDto.getStrategyId() + "&&" + eventDto.getStrategyTagAdvId() + "&&" + eventDto.getMediaId() + "&&" + eventDto.getMediaAppId() + "&&" + eventDto.getMediaTagId() + "&&"
                        + eventDto.getAdvertiserId() + "&&" + eventDto.getAdvertiserAppId() + "&&" + eventDto.getAdvertiserTagId() + "&&" + eventDto.getStrategyTagAdvId() + "&&" + eventDto.getMediaReqPackageName() + "&&" + eventDto.getEventType();
                StatisticsMediaPackageAdvertiserEvent tmp = eventMap.computeIfAbsent(mediaAdvKey, k -> new StatisticsMediaPackageAdvertiserEvent(
                        StatisticsType.MINUTE.getCode(),
                        minute,
                        eventDto.getStrategyId(),
                        eventDto.getStrategyTagAdvId(),
                        eventDto.getMediaId(),
                        eventDto.getMediaAppId(),
                        eventDto.getMediaTagId(),
                        eventDto.getAdvertiserId(),
                        eventDto.getAdvertiserAppId(),
                        eventDto.getAdvertiserTagId(),
                        eventDto.getEventType(),
                        eventDto.getStrategyPackageId(),
                        eventDto.getMediaReqPackageName())

                );
                Long total = eventDto.getTotal() == null ? 0L : eventDto.getTotal();
                tmp.setTotal(total + tmp.getTotal());
                tmp.setRepeatTotal(tmp.getRepeatTotal() + (eventDto.getRepeatTotal() == null ? 0 : eventDto.getRepeatTotal()));
            }
        }
        if (!eventMap.isEmpty()) {
            statisticsMediaAdvertiserEventService.saveList(new ArrayList<>(eventMap.values()));
        }

        log.info("statistics package event minute success {}:{}", minute, eventMap.size());
    }
}
