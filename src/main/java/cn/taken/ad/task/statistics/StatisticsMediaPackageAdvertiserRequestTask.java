package cn.taken.ad.task.statistics;

import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.component.utils.number.BigDecimalUtils;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserRequest;
import cn.taken.ad.core.service.statistics.StatisticsMediaPackageAdvertiserRequestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Component
public class StatisticsMediaPackageAdvertiserRequestTask {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Resource(name = "BaseRedis")
    private RedisClient redis;

    @Resource
    private StatisticsMediaPackageAdvertiserRequestService statisticsMediaAdvertiserRequestService;

    @SuperScheduled(cron = "20 */1 * * * ?", only = true)
    public void statistics() {
        Date statisticsDate = new Date(System.currentTimeMillis() - (60L * 1000L));
        this.statisticsMinute(statisticsDate);
    }

    @SuperScheduled(cron = "40 0/5 * * * ?", only = true)
    public void hour() {
        Date statisticsDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(statisticsDate);
        if (c.get(Calendar.MINUTE) < 9) {
            // 统计上一个小时的数据
            c.add(Calendar.HOUR_OF_DAY, -1);
            this.statisticsHour(c.getTime());
        }
        //当前小时
        this.statisticsHour(statisticsDate);
    }

    @SuperScheduled(cron = "50 0/10 * * * ?", only = true)
    public void day() {
        Date statisticsDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(statisticsDate);
        if (c.get(Calendar.HOUR_OF_DAY) == 0 && c.get(Calendar.MINUTE) < 20) {
            // 统计昨天的数据
            c.add(Calendar.DAY_OF_MONTH, -1);
            this.statisticsDay(c.getTime());
        }
        //当前天的
        this.statisticsDay(statisticsDate);
    }

    private void statisticsDay(Date statisticsDate) {
        String day = DateUtils.toString(statisticsDate, StatisticsType.DAY.getFormat());
        String beginTime = day + "00";
        String endTime = day + "23";

        List<StatisticsMediaPackageAdvertiserRequest> eventList = statisticsMediaAdvertiserRequestService.findStatistics(beginTime, endTime, StatisticsType.HOUR);
        if (!eventList.isEmpty()) {
            eventList.forEach(event -> {
                event.setStatisticsType(StatisticsType.DAY.getCode());
                event.setStatisticsTime(day);
            });
            statisticsMediaAdvertiserRequestService.deleteByTime(StatisticsType.DAY, day);
            statisticsMediaAdvertiserRequestService.saveList(eventList);
        }
        log.info("statistics day success {}", day);
    }

    private void statisticsHour(Date statisticsDate) {
        String hour = DateUtils.toString(statisticsDate, StatisticsType.HOUR.getFormat());
        String beginTime = hour + "00";
        String endTime = hour + "59";

        List<StatisticsMediaPackageAdvertiserRequest> eventList = statisticsMediaAdvertiserRequestService.findStatistics(beginTime, endTime, StatisticsType.MINUTE);
        if (!eventList.isEmpty()) {
            eventList.forEach(event -> {
                event.setStatisticsType(StatisticsType.HOUR.getCode());
                event.setStatisticsTime(hour);
            });
            statisticsMediaAdvertiserRequestService.deleteByTime(StatisticsType.HOUR, hour);
            statisticsMediaAdvertiserRequestService.saveList(eventList);
        }
        log.info("statistics hour success {}", hour);
    }

    private void statisticsMinute(Date statisticsDate) {
        String minute = DateUtils.toString(statisticsDate, StatisticsType.MINUTE.getFormat());
        Map<String, StatisticsMediaPackageAdvertiserRequest> requestMap = new HashMap<>();
        for (; ; ) {
            List<StatisticsMediaPackageAdvertiserRequest> requests = redis.rpop(BaseRedisKeys.QUEUE_STATISTICS_MEDIA_PACK_ADV_REQ, 100, StatisticsMediaPackageAdvertiserRequest.class);
            if (CollectionUtils.isEmpty(requests)) {
                break;
            }
            for (StatisticsMediaPackageAdvertiserRequest request : requests) {
                String key = request.getStrategyId() + "&&" + request.getStrategyTagAdvId() + "&&" + request.getMediaId() + "&&" + request.getMediaAppId() + "&&" + request.getMediaTagId() + "&&"
                        + request.getAdvertiserId() + "&&" + request.getAdvertiserAppId() + "&&" + request.getAdvertiserTagId() + "&&" + request.getStrategyPackageId() + "&&" + request.getMediaReqPackageName();
                StatisticsMediaPackageAdvertiserRequest tmp = requestMap.computeIfAbsent(key, k -> new StatisticsMediaPackageAdvertiserRequest(StatisticsType.MINUTE.getCode(), minute, request.getStrategyId(), request.getStrategyTagAdvId(), request.getMediaId(), request.getMediaAppId(), request.getMediaTagId(), request.getAdvertiserId(), request.getAdvertiserAppId(), request.getAdvertiserTagId(), request.getStrategyPackageId(), request.getMediaReqPackageName()));
                tmp.setMediaReqTotal(tmp.getMediaReqTotal() + (null == request.getMediaReqTotal() ? 0 : request.getMediaReqTotal()));
                tmp.setMediaReqInvalidTotal(tmp.getMediaReqInvalidTotal() + (null == request.getMediaReqInvalidTotal() ? 0 : request.getMediaReqInvalidTotal()));
                tmp.setMediaRespFailTotal(tmp.getMediaRespFailTotal() + (null == request.getMediaRespFailTotal() ? 0 : request.getMediaRespFailTotal()));
                tmp.setMediaParticipatingTotal(tmp.getMediaParticipatingTotal() + (null == request.getMediaParticipatingTotal() ? 0 : request.getMediaParticipatingTotal()));
                tmp.setMediaWinTotal(tmp.getMediaWinTotal() + (null == request.getMediaWinTotal() ? 0 : request.getMediaWinTotal()));
                BigDecimal mediaAmount = BigDecimal.ZERO;
                if (null != request.getMediaAmount()) {
                    mediaAmount = BigDecimalUtils.div(request.getMediaAmount(), new BigDecimal(1000), 4);
                }
                tmp.setMediaAmount(BigDecimalUtils.add(tmp.getMediaAmount(), mediaAmount));
                if (null != request.getMediaMaxTime()) {
                    tmp.setMediaMaxTime(Math.max(tmp.getMediaMaxTime(), request.getMediaMaxTime()));
                }
                if (null != request.getMediaMinTime()) {
                    tmp.setMediaMinTime(Math.min(tmp.getMediaMinTime(), request.getMediaMinTime()));
                }
                tmp.setMediaUseTimeTotal(tmp.getMediaUseTimeTotal() + (null == request.getMediaUseTimeTotal() ? 0 : request.getMediaUseTimeTotal()));
                tmp.setAdvertiserReqTotal(tmp.getAdvertiserReqTotal() + (null == request.getAdvertiserReqTotal() ? 0 : request.getAdvertiserReqTotal()));
                tmp.setAdvertiserReqSuccessTotal(tmp.getAdvertiserReqSuccessTotal() + (null == request.getAdvertiserReqSuccessTotal() ? 0 : request.getAdvertiserReqSuccessTotal()));
                tmp.setAdvertiserReqFailTotal(tmp.getAdvertiserReqFailTotal() + (null == request.getAdvertiserReqFailTotal() ? 0 : request.getAdvertiserReqFailTotal()));
                tmp.setAdvertiserRespFailTotal(tmp.getAdvertiserRespFailTotal() + (null == request.getAdvertiserRespFailTotal() ? 0 : request.getAdvertiserRespFailTotal()));
                tmp.setAdvertiserReqTimeoutTotal(tmp.getAdvertiserReqTimeoutTotal() + (null == request.getAdvertiserReqTimeoutTotal() ? 0 : request.getAdvertiserReqTimeoutTotal()));
                tmp.setAdvertiserParticipatingTotal(tmp.getAdvertiserParticipatingTotal() + (null == request.getAdvertiserParticipatingTotal() ? 0 : request.getAdvertiserParticipatingTotal()));
                tmp.setAdvertiserWinTotal(tmp.getAdvertiserWinTotal() + (null == request.getAdvertiserWinTotal() ? 0 : request.getAdvertiserWinTotal()));

                BigDecimal advertiserAmount = BigDecimal.ZERO;
                if (null != request.getAdvertiserAmount()) {
                    advertiserAmount = BigDecimalUtils.div(request.getAdvertiserAmount(), new BigDecimal(1000), 4);
                }
                tmp.setAdvertiserAmount(BigDecimalUtils.add(tmp.getAdvertiserAmount(), advertiserAmount));
                if (null != request.getAdvertiserMaxTime()) {
                    tmp.setAdvertiserMaxTime(Math.max(tmp.getAdvertiserMaxTime(), request.getAdvertiserMaxTime()));
                }
                if (null != request.getAdvertiserMinTime()) {
                    tmp.setAdvertiserMinTime(Math.min(tmp.getAdvertiserMinTime(), request.getAdvertiserMinTime()));
                }
                tmp.setAdvertiserUseTimeTotal(tmp.getAdvertiserUseTimeTotal() + (null == request.getAdvertiserUseTimeTotal() ? 0 : request.getAdvertiserUseTimeTotal()));

            }

        }
        if (!requestMap.isEmpty()) {
            requestMap.values().forEach(item -> {
                if (item.getMediaReqTotal() == 0) {
                    item.setMediaAvgTime(0L);
                } else {
                    if (item.getMediaUseTimeTotal() > 0) {
                        item.setMediaAvgTime(item.getMediaReqTotal() / item.getMediaUseTimeTotal());
                    } else {
                        item.setMediaAvgTime(0L);
                    }
                }
                if (item.getAdvertiserReqTotal() == 0) {
                    item.setAdvertiserAvgTime(0L);
                } else {
                    if (item.getAdvertiserUseTimeTotal() > 0) {
                        item.setAdvertiserAvgTime(item.getAdvertiserReqTotal() / item.getAdvertiserUseTimeTotal());
                    } else {
                        item.setAdvertiserAvgTime(0L);
                    }
                }
            });
            statisticsMediaAdvertiserRequestService.saveList(new ArrayList<>(requestMap.values()));
        }
        log.info("media adv package request statistics minute success {} : size {}", minute, requestMap.size());
    }
}
