package cn.taken.ad.task.media;

import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.pojo.media.MediaPackage;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserRequest;
import cn.taken.ad.core.service.media.MediaPackageService;
import cn.taken.ad.core.service.statistics.StatisticsMediaPackageAdvertiserRequestService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component
public class MediaPackageSaveTask {

    @Resource
    private StatisticsMediaPackageAdvertiserRequestService statisticsMediaAdvertiserRequestService;
    @Resource
    private MediaPackageService mediaPackageService;

    @SuperScheduled(cron = "0 */5 * * * ?", only = true)
    public void execute() {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MINUTE, -10);
        String beginTime = DateUtils.toString(c.getTime(), StatisticsType.MINUTE.getFormat());
        String endTime = DateUtils.toString(c.getTime(), StatisticsType.MINUTE.getFormat());
        //SQL已经筛选过重复的数据
        List<StatisticsMediaPackageAdvertiserRequest> list = statisticsMediaAdvertiserRequestService.findMediaBundles(beginTime, endTime);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<MediaPackage> result = new ArrayList<>();
        for (StatisticsMediaPackageAdvertiserRequest adv : list) {
            MediaPackage mp = new MediaPackage();
            mp.setMediaId(adv.getMediaId());
            mp.setMediaAppId(adv.getMediaAppId());
            mp.setMediaTagId(adv.getMediaTagId());
            mp.setPackageName(adv.getMediaReqPackageName());
            mp.setCreateTime(new Date());
            mp.setOperatorId(0L);
            result.add(mp);
            if (result.size() >= 200) {
                mediaPackageService.saveBatch(result);
                result.clear();
            }
        }
        if (!result.isEmpty()) {
            mediaPackageService.saveBatch(result);
        }
    }
}
