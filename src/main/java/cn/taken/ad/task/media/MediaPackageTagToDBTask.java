package cn.taken.ad.task.media;

import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.core.dto.business.media.MediaPackageTagDTO;
import cn.taken.ad.core.pojo.media.Media;
import cn.taken.ad.core.pojo.media.MediaProtocol;
import cn.taken.ad.core.pojo.media.MediaTag;
import cn.taken.ad.core.pojo.strategy.Strategy;
import cn.taken.ad.core.pojo.strategy.StrategyTagAdvertiser;
import cn.taken.ad.core.service.media.MediaTagService;
import cn.taken.ad.core.service.strategy.StrategyService;
import cn.taken.ad.core.service.strategy.StrategyTagAdvertiserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class MediaPackageTagToDBTask {

    private static final Logger log = LoggerFactory.getLogger(MediaPackageTagToDBTask.class);
    @Resource(name = "BaseRedis")
    private RedisClient redis;
    @Resource
    private MediaTagService mediaTagService;
    @Resource
    private StrategyService strategyService;
    @Resource
    private StrategyTagAdvertiserService strategyTagAdvertiserService;
    @Resource(name = "BaseRedisL2Cache")
    private BaseRedisL2Cache baseRedisL2Cache;

    @SuperScheduled(cron = "*/30 * * * * ?", only = true)
    public void exec() {
        List<MediaPackageTagDTO> mediaTags = redis.rpop(BaseRedisKeys.QUEUE_MEDIA_PACKAGE_TAG_SAVE, 100, MediaPackageTagDTO.class);
        if (!mediaTags.isEmpty()) {
            //map的key是mediaId_mediaAppId_code  这三个字段组合 value是单个MediaPackageTagDTO
            Map<String, MediaPackageTagDTO> map = new HashMap<>();
            mediaTags.forEach(mediaTag -> {
                String key = mediaTag.getMediaId() + "_" + mediaTag.getMediaAppId() + "_" + mediaTag.getCode();
                map.put(key, mediaTag);
            });
            Set<MediaPackageTagDTO> saves = new HashSet<>();
            //判断是否存在
            map.forEach((k, v) -> {
                Media media = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_ID_ + v.getMediaId(), Media.class);
                if (media == null) {
                    return;
                }
                MediaProtocol protocol = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_PROTOCOL_ID_ + media.getProtocolId(), MediaProtocol.class);
                if (protocol == null) {
                    return;
                }
                if (protocol.getIsNeedAppCode()) {
                    MediaTag exist = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_TAG_CODE_ + v.getMediaId() + "_" + v.getMediaAppId() + "_" + v.getCode(), MediaTag.class);
                    if (exist == null) {
                        //查询数据库是存在
                        List<MediaTag> check = mediaTagService.findMediaByCode(v.getMediaId(), v.getMediaAppId(), v.getCode());
                        if (check.isEmpty()) {
                            saves.add(v);
                        }
                    }
                } else {
                    MediaTag exist = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_NOAPPCODE_TAG_CODE_ + v.getMediaId() + "_" + v.getCode(), MediaTag.class);
                    if (exist == null) {
                        //查询数据库是存在
                        List<MediaTag> check = mediaTagService.findMediaByCode(v.getMediaId(), v.getMediaAppId(), v.getCode());
                        if (check.isEmpty()) {
                            saves.add(v);
                        }
                    }
                }
            });

            if (!saves.isEmpty()) {
                saves.forEach(mediaTag -> {
                    try {
                        mediaTag.setId(null);
                        log.info("save media tag:{}", JsonHelper.toJsonString(mediaTag));
                        Strategy strategy = strategyService.findByMediaParams(mediaTag.getMediaId(), mediaTag.getMediaAppId(), mediaTag.getMediaBaseTagId());
                        if (strategy != null) {
                            Strategy newStrategy = JsonHelper.fromJson(Strategy.class, JsonHelper.toJsonString(strategy));
                            List<StrategyTagAdvertiser> saveStrategyTagAdvertisers = strategyTagAdvertiserService.findStrategyTags(strategy.getId());
                            mediaTagService.saveMediaTagAndStrategy(mediaTag, newStrategy, saveStrategyTagAdvertisers);
                        } else {
                            log.error("not found strategy mediaId:{} mediaAppId:{} mediaBaseTagId:{}", mediaTag.getMediaId(), mediaTag.getMediaAppId(), mediaTag.getMediaBaseTagId());
                            mediaTagService.save(mediaTag);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                });
            }
        }
    }
}
