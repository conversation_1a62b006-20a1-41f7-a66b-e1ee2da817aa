package cn.taken.ad.task;

import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.configuration.monitor.RtbPackageMonitor;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.dto.business.statistics.MediaPackageAdvertiserReqMonitorDto;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserEvent;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserRequest;
import com.google.common.util.concurrent.AtomicDouble;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class RtbPackageMonitorTask {

    @Resource
    private RtbPackageMonitor rtbPackageMonitor;

    @Resource(name = "BaseRedis")
    private RedisClient redis;

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final AtomicBoolean FORCE_CLOSE = new AtomicBoolean(false);

    public void setForceClose() {
        FORCE_CLOSE.getAndSet(true);
    }

    @SuperScheduled(cron = "1 */1 * * * ?")
    public synchronized void executeMediaAdvRequest() {
        Date last = new Date(System.currentTimeMillis() - (1000 * 60));
        String lastTime = DateUtils.toString(last, "yyyy-MM-dd HH:mm");
        String beginTime = lastTime + ":00";
        Date begin = DateUtils.parseDate(beginTime, "yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(begin);
        String statisticsTime = DateUtils.toString(last, StatisticsType.MINUTE.getFormat());
        Map<String, StatisticsMediaPackageAdvertiserRequest> minuteMediaAdvReq = new HashMap<>();
        Set<String> temKeys = new HashSet<>();
        //停止前统计当前时间的数据
        int ticket = FORCE_CLOSE.get() ? 120 : 60;
        for (int i = 0; i < ticket; i++) {
            long time = calendar.getTimeInMillis() / 1000L;
            calendar.add(Calendar.SECOND, 1);
            temKeys.clear();
            Map<String, MediaPackageAdvertiserReqMonitorDto> tmp = rtbPackageMonitor.getMediaAdvRequestMonitor().remove(time);
            if (tmp != null) {
                temKeys.addAll(tmp.keySet());
            }
            Map<String, AtomicInteger> windMap = rtbPackageMonitor.getMediaAdvWindMonitor().remove(time);
            if (windMap != null) {
                temKeys.addAll(windMap.keySet());
            }
            Map<String, AtomicDouble> amountMediaMap = rtbPackageMonitor.getMediaAdvEventAmountOfMediaMonitor().remove(time);
            if (amountMediaMap != null) {
                temKeys.addAll(amountMediaMap.keySet());
            }
            Map<String, AtomicDouble> amountAdvMap = rtbPackageMonitor.getMediaAdvEventAmountOfAdvMonitor().remove(time);
            if (amountAdvMap != null) {
                temKeys.addAll(amountAdvMap.keySet());
            }
            for (String key : temKeys) {
                String[] arr = key.split("-");
                long wind = windMap == null ? 0 : (windMap.get(key) == null ? 0 : windMap.get(key).get());
                double ma = amountMediaMap == null ? 0 : (amountMediaMap.get(key) == null ? 0 : amountMediaMap.get(key).get());
                double aa = amountAdvMap == null ? 0 : (amountAdvMap.get(key) == null ? 0 : amountAdvMap.get(key).get());

                StatisticsMediaPackageAdvertiserRequest minute = minuteMediaAdvReq.computeIfAbsent(key, k -> {
                    StatisticsMediaPackageAdvertiserRequest req;
                    req = new StatisticsMediaPackageAdvertiserRequest();
                    req.setStrategyId(Long.valueOf(arr[0]));
                    req.setStrategyTagAdvId(Long.valueOf(arr[1]));
                    req.setMediaId(Long.valueOf(arr[2]));
                    req.setMediaAppId(Long.valueOf(arr[3]));
                    req.setMediaTagId(Long.valueOf(arr[4]));
                    req.setAdvertiserId(Long.valueOf(arr[5]));
                    req.setAdvertiserAppId(Long.valueOf(arr[6]));
                    req.setAdvertiserTagId(Long.valueOf(arr[7]));
                    req.setStrategyPackageId(Long.valueOf(arr[8]));
                    req.setMediaReqPackageName(arr[9]);
                    req.setStatisticsTime(statisticsTime);
                    req.setStatisticsType(StatisticsType.MINUTE.getCode());
                    req.setMediaAmount(new BigDecimal(0));
                    req.setAdvertiserAmount(new BigDecimal(0));
                    req.setMediaReqTotal(0L);
                    req.setMediaRespFailTotal(0L);
                    req.setMediaReqInvalidTotal(0L);
                    req.setMediaUseTimeTotal(0L);
                    req.setMediaMinTime(0L);
                    req.setMediaMaxTime(0L);

                    req.setAdvertiserReqTotal(0L);
                    req.setAdvertiserReqSuccessTotal(0L);
                    req.setAdvertiserReqFailTotal(0L);
                    req.setAdvertiserRespFailTotal(0L);
                    req.setAdvertiserReqTimeoutTotal(0L);
                    req.setAdvertiserUseTimeTotal(0L);
                    req.setAdvertiserWinTotal(0L);
                    req.setAdvertiserParticipatingTotal(0L);
                    req.setAdvertiserMinTime(0L);
                    req.setAdvertiserMaxTime(0L);
                    return req;
                });

                MediaPackageAdvertiserReqMonitorDto mediaAdvReq = tmp != null ? tmp.get(key) : null;
                if (mediaAdvReq != null) {
                    minute.setMediaReqTotal(mediaAdvReq.getMediaReqTotal().longValue() + minute.getMediaReqTotal());
                    minute.setMediaRespFailTotal(mediaAdvReq.getMediaRespFailTotal().longValue() + minute.getMediaRespFailTotal());
                    minute.setMediaReqInvalidTotal(mediaAdvReq.getMediaReqInvalidTotal().longValue() + minute.getMediaReqInvalidTotal());
                    minute.setMediaParticipatingTotal(getValue(minute.getMediaParticipatingTotal()) + mediaAdvReq.getMediaParticipatingTotal().longValue());
                    minute.setMediaUseTimeTotal(mediaAdvReq.getMediaUseTimeTotal().longValue() + minute.getMediaUseTimeTotal());
                    if (mediaAdvReq.getMediaMinTime().longValue() < minute.getMediaMinTime()) {
                        minute.setMediaMinTime(mediaAdvReq.getMediaMinTime().longValue());
                    }
                    if (mediaAdvReq.getMediaMaxTime().longValue() > minute.getMediaMaxTime()) {
                        minute.setMediaMaxTime(mediaAdvReq.getMediaMaxTime().longValue());
                    }

                    minute.setAdvertiserReqTotal(mediaAdvReq.getAdvertiserReqTotal().longValue() + minute.getAdvertiserReqTotal());
                    minute.setAdvertiserReqSuccessTotal(mediaAdvReq.getAdvertiserReqSuccessTotal().longValue() + minute.getAdvertiserReqSuccessTotal());
                    minute.setAdvertiserReqFailTotal(mediaAdvReq.getAdvertiserReqFailTotal().longValue() + minute.getAdvertiserReqFailTotal());
                    minute.setAdvertiserRespFailTotal(mediaAdvReq.getAdvertiserRespFailTotal().longValue() + minute.getAdvertiserRespFailTotal());
                    minute.setAdvertiserReqTimeoutTotal(mediaAdvReq.getAdvertiserReqTimeoutTotal().longValue() + minute.getAdvertiserReqTimeoutTotal());
                    minute.setAdvertiserUseTimeTotal(mediaAdvReq.getAdvertiserUseTimeTotal().longValue() + minute.getAdvertiserUseTimeTotal());
                    minute.setAdvertiserParticipatingTotal(getValue(minute.getAdvertiserParticipatingTotal()) + mediaAdvReq.getAdvertiserParticipatingTotal().longValue());
                    if (mediaAdvReq.getAdvertiserMinTime().longValue() < minute.getAdvertiserMinTime()) {
                        minute.setAdvertiserMinTime(mediaAdvReq.getAdvertiserMinTime().longValue());
                    }
                    if (mediaAdvReq.getAdvertiserMaxTime().longValue() > minute.getAdvertiserMaxTime()) {
                        minute.setAdvertiserMaxTime(mediaAdvReq.getAdvertiserMaxTime().longValue());
                    }
                }

                minute.setMediaWinTotal(getValue(minute.getMediaWinTotal()) + wind);
                minute.setAdvertiserWinTotal(getValue(minute.getAdvertiserWinTotal()) + wind);
                minute.setMediaAmount(minute.getMediaAmount().add(new BigDecimal(ma)));
                minute.setAdvertiserAmount(minute.getAdvertiserAmount().add(new BigDecimal(aa)));

            }
            temKeys.clear();
        }

        if (minuteMediaAdvReq.size() > 0) {
            minuteMediaAdvReq.forEach((k, v) -> {
                //有些预算是没有被请求到的
                if (v.getMediaReqTotal() != null && v.getMediaReqTotal() != 0) {
                    v.setMediaAvgTime(v.getMediaUseTimeTotal() / v.getMediaReqTotal());
                }
                if (v.getAdvertiserReqTotal() != null && v.getAdvertiserReqTotal() != 0) {
                    v.setAdvertiserAvgTime(v.getAdvertiserUseTimeTotal() / v.getAdvertiserReqTotal());
                }
            });
            redis.lpush(BaseRedisKeys.QUEUE_STATISTICS_MEDIA_PACK_ADV_REQ, -1, minuteMediaAdvReq.values());
        }
        log.info("rtb package minute Request: mediaAdvReq:{}", JsonHelper.toJsonString(minuteMediaAdvReq));
    }

    @SuperScheduled(cron = "1 */1 * * * ?")
    public synchronized void executeEvent() {
        Date last = new Date(System.currentTimeMillis() - (1000 * 60));
        String lastTime = DateUtils.toString(last, "yyyy-MM-dd HH:mm");
        String beginTime = lastTime + ":00";
        Date begin = DateUtils.parseDate(beginTime, "yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(begin);
        String statisticTime = DateUtils.toString(last, StatisticsType.MINUTE.getFormat());
        Map<String, StatisticsMediaPackageAdvertiserEvent> smae = new HashMap<>();
        int tick = FORCE_CLOSE.get() ? 120 : 60;
        for (int i = 0; i < tick; i++) {
            long time = calendar.getTimeInMillis() / 1000L;
            calendar.add(Calendar.SECOND, 1);
            Map<String, AtomicInteger> smaeMap = rtbPackageMonitor.getMediaAdvEventMonitor().remove(time);
            if (!CollectionUtils.isEmpty(smaeMap)) {
                Iterator<Map.Entry<String, AtomicInteger>> it = smaeMap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, AtomicInteger> entry = it.next();
                    String key = entry.getKey();
                    AtomicInteger value = entry.getValue();
                    StatisticsMediaPackageAdvertiserEvent event = smae.computeIfAbsent(key, k -> createMediaAdv(key, statisticTime));
                    event.setTotal(value.get() + event.getTotal());
                }
            }
        }

        if (smae.size() > 0) {
            redis.lpush(BaseRedisKeys.QUEUE_STATISTICS_MEDIA_PACK_ADV_EVENT, -1, smae.values());
        }
        log.info("minute package Event: mediaAdv:{},media:{},advertiser:{}", JsonHelper.toJsonString(smae));
    }

    private StatisticsMediaPackageAdvertiserEvent createMediaAdv(String key, String statisticTime) {
        String[] arr = key.split("-");
        StatisticsMediaPackageAdvertiserEvent dto = new StatisticsMediaPackageAdvertiserEvent();
        dto.setStrategyId(Long.valueOf(arr[0]));
        dto.setStrategyTagAdvId(Long.valueOf(arr[1]));
        dto.setMediaId(Long.valueOf(arr[2]));
        dto.setMediaAppId(Long.valueOf(arr[3]));
        dto.setMediaTagId(Long.valueOf(arr[4]));
        dto.setAdvertiserId(Long.valueOf(arr[5]));
        dto.setAdvertiserAppId(Long.valueOf(arr[6]));
        dto.setAdvertiserTagId(Long.valueOf(arr[7]));
        dto.setStrategyPackageId(Long.valueOf(arr[8]));
        dto.setMediaReqPackageName(arr[9]);
        dto.setEventType(Integer.valueOf(arr[10]));
        dto.setTotal(0L);
        dto.setRepeatTotal(0L);
        dto.setStatisticsType(StatisticsType.MINUTE.getCode());
        dto.setStatisticsTime(statisticTime);
        return dto;
    }

    private long getValue(Long v) {
        return v == null ? 0 : v;
    }
}
