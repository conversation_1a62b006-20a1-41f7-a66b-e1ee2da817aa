package cn.taken.ad.task;

import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.configuration.monitor.RtbEventMonitor;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.constant.redis.QueueRedisKeys;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.pojo.statistics.StatisticsAdvertiserEvent;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaAdvertiserEvent;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class RtbEventMonitorTask {
    @Resource(name = "BaseRedis")
    private RedisClient redis;
    @Resource(name = "BaseRedis")
    private RedisClient redis;
    @Resource
    private RtbEventMonitor rtbEventMonitor;

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final AtomicBoolean FORCE_CLOSE = new AtomicBoolean(false);

    public void setForceClose() {
        FORCE_CLOSE.getAndSet(true);
    }

    private StatisticsMediaAdvertiserEvent createMediaAdv(String key, String statisticTime) {
        String[] arr = key.split("-");
        StatisticsMediaAdvertiserEvent dto = new StatisticsMediaAdvertiserEvent();
        dto.setStrategyId(Long.valueOf(arr[0]));
        dto.setStrategyTagAdvId(Long.valueOf(arr[1]));
        dto.setMediaId(Long.valueOf(arr[2]));
        dto.setMediaAppId(Long.valueOf(arr[3]));
        dto.setMediaTagId(Long.valueOf(arr[4]));
        dto.setAdvertiserId(Long.valueOf(arr[5]));
        dto.setAdvertiserAppId(Long.valueOf(arr[6]));
        dto.setAdvertiserTagId(Long.valueOf(arr[7]));
        dto.setEventType(Integer.valueOf(arr[8]));
        dto.setTotal(0L);
        dto.setRepeatTotal(0L);
        dto.setStatisticsType(StatisticsType.MINUTE.getCode());
        dto.setStatisticsTime(statisticTime);
        return dto;
    }


    private StatisticsMediaEvent createMedia(String key, String statisticTime) {
        String[] arr = key.split("-");
        StatisticsMediaEvent dto = new StatisticsMediaEvent();
        dto.setStrategyId(Long.valueOf(arr[0]));
        dto.setMediaId(Long.valueOf(arr[1]));
        dto.setMediaAppId(Long.valueOf(arr[2]));
        dto.setMediaTagId(Long.valueOf(arr[3]));
        dto.setEventType(Integer.valueOf(arr[4]));
        dto.setStatisticsType(StatisticsType.MINUTE.getCode());
        dto.setStatisticsTime(statisticTime);
        dto.setTotal(0L);
        dto.setRepeatTotal(0L);
        return dto;
    }

    private StatisticsAdvertiserEvent createAdv(String key, String statisticTime) {
        String[] arr = key.split("-");
        StatisticsAdvertiserEvent dto = new StatisticsAdvertiserEvent();
        dto.setAdvertiserId(Long.valueOf(arr[0]));
        dto.setAdvertiserAppId(Long.valueOf(arr[1]));
        dto.setAdvertiserTagId(Long.valueOf(arr[2]));
        dto.setEventType(Integer.valueOf(arr[3]));
        dto.setStatisticsType(StatisticsType.MINUTE.getCode());
        dto.setStatisticsTime(statisticTime);
        dto.setTotal(0L);
        dto.setRepeatTotal(0L);
        return dto;
    }

    @SuperScheduled(cron = "1 */1 * * * ?")
    public synchronized void execute() {
        Date last = new Date(System.currentTimeMillis() - (1000 * 60));
        String lastTime = DateUtils.toString(last, "yyyy-MM-dd HH:mm");
        String beginTime = lastTime + ":00";
        Date begin = DateUtils.parseDate(beginTime, "yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(begin);
        String statisticTime = DateUtils.toString(last, StatisticsType.MINUTE.getFormat());
        Map<String, StatisticsMediaAdvertiserEvent> smae = new HashMap<>();
        Map<String, StatisticsMediaEvent> sme = new HashMap<>();
        Map<String, StatisticsAdvertiserEvent> sae = new HashMap<>();
        int tick = FORCE_CLOSE.get() ? 120 : 60;
        for (int i = 0; i < tick; i++) {
            long time = calendar.getTimeInMillis() / 1000L;
            calendar.add(Calendar.SECOND, 1);
            Map<String, AtomicInteger> smaeMap = rtbEventMonitor.getMediaAdvEventMonitor().remove(time);
            if (!CollectionUtils.isEmpty(smaeMap)) {
                Iterator<Map.Entry<String, AtomicInteger>> it = smaeMap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, AtomicInteger> entry = it.next();
                    String key = entry.getKey();
                    AtomicInteger value = entry.getValue();
                    StatisticsMediaAdvertiserEvent event = smae.computeIfAbsent(key, k -> createMediaAdv(key, statisticTime));
                    event.setTotal(value.get() + event.getTotal());
                }
            }

            Map<String, AtomicInteger> smaeRepeatMap = rtbEventMonitor.getMediaAdvRepeatEventMonitor().remove(time);
            if (!CollectionUtils.isEmpty(smaeRepeatMap)) {
                Iterator<Map.Entry<String, AtomicInteger>> it = smaeRepeatMap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, AtomicInteger> entry = it.next();
                    String key = entry.getKey();
                    AtomicInteger value = entry.getValue();
                    StatisticsMediaAdvertiserEvent event = smae.computeIfAbsent(key, k -> createMediaAdv(key, statisticTime));
                    event.setRepeatTotal(value.get() + event.getRepeatTotal());
                }
            }

            Map<String, AtomicInteger> smeMap = rtbEventMonitor.getMediaEventMonitor().remove(time);
            if (!CollectionUtils.isEmpty(smeMap)) {
                Iterator<Map.Entry<String, AtomicInteger>> it = smeMap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, AtomicInteger> entry = it.next();
                    String key = entry.getKey();
                    AtomicInteger value = entry.getValue();
                    StatisticsMediaEvent event = sme.computeIfAbsent(key, k -> createMedia(key, statisticTime));
                    event.setTotal(event.getTotal() + value.get());
                }
            }

            Map<String, AtomicInteger> smeRepeatMap = rtbEventMonitor.getMediaRepeatEventMonitor().remove(time);
            if (!CollectionUtils.isEmpty(smeRepeatMap)) {
                Iterator<Map.Entry<String, AtomicInteger>> it = smeRepeatMap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, AtomicInteger> entry = it.next();
                    String key = entry.getKey();
                    AtomicInteger value = entry.getValue();
                    StatisticsMediaEvent event = sme.computeIfAbsent(key, k -> createMedia(key, statisticTime));
                    event.setRepeatTotal(event.getRepeatTotal() + value.get());
                }
            }

            Map<String, AtomicInteger> saeMap = rtbEventMonitor.getAdvEventMonitor().remove(time);
            if (!CollectionUtils.isEmpty(saeMap)) {
                Iterator<Map.Entry<String, AtomicInteger>> it = saeMap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, AtomicInteger> entry = it.next();
                    String key = entry.getKey();
                    StatisticsAdvertiserEvent event = sae.computeIfAbsent(key, k -> createAdv(key, statisticTime));
                    AtomicInteger value = entry.getValue();
                    event.setTotal(event.getTotal() + value.get());
                }
            }

            Map<String, AtomicInteger> saeRepeatMap = rtbEventMonitor.getAdvRepeatEventMonitor().remove(time);
            if (!CollectionUtils.isEmpty(saeRepeatMap)) {
                Iterator<Map.Entry<String, AtomicInteger>> it = saeRepeatMap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, AtomicInteger> entry = it.next();
                    String key = entry.getKey();
                    StatisticsAdvertiserEvent event = sae.computeIfAbsent(key, k -> createAdv(key, statisticTime));
                    AtomicInteger value = entry.getValue();
                    event.setRepeatTotal(event.getRepeatTotal() + value.get());
                }
            }
        }

        if (smae.size() > 0) {
            redis.lpush(QueueRedisKeys.QUEUE_STATISTICS_MEDIA_ADV_EVENT, -1, smae.values());
        }
        if (sme.size() > 0) {
            redis.lpush(QueueRedisKeys.QUEUE_STATISTICS_MEDIA_EVENT, -1, sme.values());
        }
        if (sae.size() > 0) {
            redis.lpush(QueueRedisKeys.QUEUE_STATISTICS_ADV_EVENT, -1, sae.values());
        }
        log.info("minute Event: mediaAdv:{},media:{},advertiser:{}", JsonHelper.toJsonString(smae),JsonHelper.toJsonString(sme),JsonHelper.toJsonString(sae));
    }
}
