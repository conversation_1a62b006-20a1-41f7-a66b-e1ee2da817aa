package cn.taken.ad.core.dao.media;

import cn.taken.ad.component.orm.BaseSuperDao;
import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.core.dto.web.oper.media.mpackage.*;
import cn.taken.ad.core.dto.web.oper.media.tag.MediaTagListReq;
import cn.taken.ad.core.pojo.media.MediaPackage;

import java.util.List;

public interface MediaPackageDao extends BaseSuperDao<MediaPackage> {

    List<MediaPackage> findByTagIdAndPackName(List<Long> tagIds, List<String> bundles);

    Page<MediaPackageInfo> findPage(MediaPackagePageReq req);

    List<MediaPackageInfo> findList(MediaPackageListReq req);

    MediaPackageInfo findInfoById(Long id);
}
