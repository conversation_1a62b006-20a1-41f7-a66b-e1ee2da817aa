package cn.taken.ad.core.dao.media.impl;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.configuration.database.BasePojoSuperDaoImpl;
import cn.taken.ad.core.dao.media.MediaPackageDao;
import cn.taken.ad.core.dto.web.oper.media.mpackage.MediaPackageInfo;
import cn.taken.ad.core.dto.web.oper.media.mpackage.MediaPackageListReq;
import cn.taken.ad.core.dto.web.oper.media.mpackage.MediaPackagePageReq;
import cn.taken.ad.core.pojo.media.MediaPackage;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 *
 */
@Repository
public class MediaPackageDaoImpl extends BasePojoSuperDaoImpl<MediaPackage> implements MediaPackageDao {

    @Override
    public List<MediaPackage> findByTagIdAndPackName(List<Long> tagIds, List<String> bundles) {
        String hql = "FROM MediaPackage where mediaTagId in (:mediaTagId) and packageName in(:packageName)";
        Map<String, Object> params = new HashMap<>();
        params.put("mediaTagId", tagIds);
        params.put("packageName", bundles);
        return this.getListResult(MediaPackage.class, hql, params);
    }

    @Override
    public Page<MediaPackageInfo> findPage(MediaPackagePageReq req) {
        String sql = "select " +
                "p.* , " +
                "m.name as media_name , m.code as media_code ,  " +
                "a.name as media_app_name , a.code as media_app_code , a.type as media_app_type,  " +
                "t.type as type,t.code as media_tag_code,t.name as media_tag_name " +
                "from media_package p  " +
                "left join media m on m.id = p.media_id " +
                "left join media_app a on a.id = p.media_app_id " +
                "left join media_tag t on t.id = p.media_tag_id " +
                "where 1=1 ";
        List<Object> objects = new ArrayList<>();

        if (StringUtils.isNotEmpty(req.getName())) {
            sql += " and (p.name like ? or p.package_name like ? or p.app_name like ?)";
            objects.add("%" + req.getName() + "%");
            objects.add("%" + req.getName() + "%");
            objects.add("%" + req.getName() + "%");
        }
        if (StringUtils.isNotEmpty(req.getPackageName())) {
            sql += " and (p.package_name like ? or p.app_name like ?)";
            objects.add("%" + req.getPackageName() + "%");
            objects.add("%" + req.getPackageName() + "%");
        }
        if (req.getMediaId() != null) {
            sql += " and p.media_id = ? ";
            objects.add(req.getMediaId());
        }
        if (req.getMediaAppId() != null) {
            sql += " and p.media_app_id = ? ";
            objects.add(req.getMediaAppId());
        }
        if (req.getMediaTagId() != null) {
            sql += " and p.media_tag_id = ? ";
            objects.add(req.getMediaTagId());
        }
        sql += " order by p.id desc ";
        return this.findObjectPageByClassInMysql(MediaPackageInfo.class, sql, req.getStart(), req.getLimit(), objects.toArray());
    }

    @Override
    public List<MediaPackageInfo> findList(MediaPackageListReq req) {
        String sql = "select " +
                "p.* , " +
                "m.name as media_name , m.code as media_code ,  " +
                "a.name as media_app_name , a.code as media_app_code , a.type as media_app_type,  " +
                "t.type as type,t.code as media_tag_code,t.name as media_tag_name " +
                "from media_package p  " +
                "left join media m on m.id = p.media_id " +
                "left join media_app a on a.id = p.media_app_id " +
                "left join media_tag t on t.id = p.media_tag_id " +
                "where 1=1 ";
        List<Object> objects = new ArrayList<>();
        if (StringUtils.isNotEmpty(req.getName())) {
            sql += " and (p.name like ? or p.code = ? or p.app_name like ?) ";
            objects.add("%" + req.getName() + "%");
            objects.add(req.getName());
        }
        if (req.getMediaAppId() != null) {
            sql += " and p.media_app_id = ? ";
            objects.add(req.getMediaAppId());
        }
        if (req.getMediaId() != null) {
            sql += " and p.media_id = ? ";
            objects.add(req.getMediaId());
        }
        if (req.getMediaTagId() != null) {
            sql += " and p.media_tag_id = ? ";
            objects.add(req.getMediaTagId());
        }
        sql += " order by p.id desc limit 50";
        return this.findObjectListByClass(MediaPackageInfo.class, sql, objects.toArray());
    }

    @Override
    public MediaPackageInfo findInfoById(Long id) {
        String sql = "select " +
                "pa.* , " +
                "p.name as protocol_name ,p.code as protocol_code ,p.type as protocol_type ,p.tag_param as protocol_tag_param , " +
                "m.name as media_name , m.code as media_code ,  " +
                "a.name as media_app_name , a.code as media_app_code , a.type as media_app_type,  " +
                "t.name as media_tag_name , t.code as media_tag_code , t.type " +
                "from media_package pa  " +
                "left join media m on m.id = pa.media_id " +
                "left join media_app a on a.id = pa.media_app_id " +
                "left join media_tag t on pa.media_tag_id = t.id " +
                "left join media_protocol p on p.id = m.protocol_id " +
                "where pa.id = ? ";
        return this.findObjectUnique(MediaPackageInfo.class, sql, id);
    }
}
