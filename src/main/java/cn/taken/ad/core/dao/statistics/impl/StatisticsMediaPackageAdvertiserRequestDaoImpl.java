package cn.taken.ad.core.dao.statistics.impl;

import cn.taken.ad.configuration.database.BasePojoSuperDaoImpl;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.dao.statistics.StatisticsMediaPackageAdvertiserRequestDao;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserRequest;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Repository
public class StatisticsMediaPackageAdvertiserRequestDaoImpl extends BasePojoSuperDaoImpl<StatisticsMediaPackageAdvertiserRequest> implements StatisticsMediaPackageAdvertiserRequestDao {

    @Override
    public List<StatisticsMediaPackageAdvertiserRequest> findStatistics(String beginTime, String endTime, StatisticsType type) {
        String sql = "SELECT " +
                "sum(media_req_total) AS media_req_total," +
                "sum(media_req_invalid_total) AS media_req_invalid_total," +
                "sum(media_resp_fail_total) AS media_resp_fail_total," +
                "sum(media_participating_total) AS media_participating_total," +
                "sum(media_win_total) AS media_win_total," +
                "sum(media_amount) AS media_amount," +
                "sum(media_use_time_total) AS media_use_time_total," +
                "sum(media_use_time_total)/sum(media_req_total) AS media_avg_time," +
                "max(media_max_time) AS media_max_time," +
                "min(media_min_time) AS media_min_time," +

                "sum(advertiser_req_total) AS advertiser_req_total," +
                "sum(advertiser_req_success_total) AS advertiser_req_success_total," +
                "sum(advertiser_req_fail_total) AS advertiser_req_fail_total," +
                "sum(advertiser_resp_fail_total) AS advertiser_resp_fail_total," +
                "sum(advertiser_req_timeout_total) AS advertiser_req_timeout_total," +
                "sum(advertiser_participating_total) AS advertiser_participating_total," +
                "sum(advertiser_win_total) AS advertiser_win_total," +
                "sum(advertiser_amount) AS advertiser_amount," +
                "sum(advertiser_use_time_total) AS advertiser_use_time_total," +
                "sum(advertiser_use_time_total)/sum(advertiser_req_total) AS advertiser_avg_time," +
                "max(advertiser_max_time) AS advertiser_max_time," +
                "min(advertiser_min_time) AS advertiser_min_time," +
                "strategy_id," +
                "strategy_tag_adv_id," +
                "media_id," +
                "media_app_id," +
                "media_tag_id," +
                "advertiser_id," +
                "advertiser_app_id," +
                "advertiser_tag_id, " +
                "strategy_package_id, " +
                "media_req_package_name " +
                "FROM statistics_media_advertiser_request " +
                "WHERE statistics_time>=? AND statistics_time<=? AND statistics_type=? " +
                "GROUP BY strategy_id,strategy_tag_adv_id,media_id,media_app_id,media_tag_id,advertiser_id,advertiser_app_id,advertiser_tag_id,strategy_package_id,media_req_package_name";
        List<Object> params = new LinkedList<>();
        params.add(beginTime);
        params.add(endTime);
        params.add(type.getCode());
        return this.findObjectListByClass(StatisticsMediaPackageAdvertiserRequest.class, sql, params.toArray());
    }

    @Override
    public void deleteByTime(StatisticsType type, String time) {
        String hql = "delete from StatisticsMediaPackageAdvertiserRequest where statisticsType=:statisticsType and statisticsTime=:statisticsTime";
        Map<String, Object> params = new HashMap<>();
        params.put("statisticsType", type.getCode());
        params.put("statisticsTime", time);
        this.execByHql(hql, params);
    }

    @Override
    public List<StatisticsMediaPackageAdvertiserRequest> findMediaBundles(String beginTime, String endTime) {
        String sql = "select a.* from  (select count(*) as media_req_total ,media_id,media_app_id,media_tag_id,media_req_package_name FROM statistics_media_package_advertiser_request ";
        sql += " WHERE statistics_time>=? AND statistics_time<=? AND statistics_type=? GROUP BY media_id,media_app_id,media_tag_id,media_req_package_name ) a left join media_package b ";
        sql += " on a.media_id=b.media_id AND a.media_app_id=b.media_app_id AND a.media_tag_id=b.media_tag_id AND a.media_req_package_name=b.package_name WHERE b.media_id IS NULL";
        List<Object> params = new LinkedList<>();
        params.add(beginTime);
        params.add(endTime);
        params.add(StatisticsType.MINUTE.getCode());
        return this.findObjectListByClass(StatisticsMediaPackageAdvertiserRequest.class, sql, params.toArray());
    }
}
