package cn.taken.ad.core.dao.statistics;

import cn.taken.ad.component.orm.BaseSuperDao;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserEvent;

import java.util.List;

public interface StatisticsMediaPackageAdvertiserEventDao extends BaseSuperDao<StatisticsMediaPackageAdvertiserEvent> {

    List<StatisticsMediaPackageAdvertiserEvent> findStatistics(String beginTime, String endTime, StatisticsType type);

    void deleteByTime(StatisticsType type, String time);

}
