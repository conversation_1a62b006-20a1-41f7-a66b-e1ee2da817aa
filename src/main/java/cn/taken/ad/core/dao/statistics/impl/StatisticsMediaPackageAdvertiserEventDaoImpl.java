package cn.taken.ad.core.dao.statistics.impl;

import cn.taken.ad.configuration.database.BasePojoSuperDaoImpl;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.dao.statistics.StatisticsMediaPackageAdvertiserEventDao;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserEvent;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Repository
public class StatisticsMediaPackageAdvertiserEventDaoImpl extends BasePojoSuperDaoImpl<StatisticsMediaPackageAdvertiserEvent> implements StatisticsMediaPackageAdvertiserEventDao {

    @Override
    public List<StatisticsMediaPackageAdvertiserEvent> findStatistics(String beginTime, String endTime, StatisticsType type) {
        String sql = "SELECT " +
                "sum(total) AS total," +
                "sum(repeat_total) AS repeat_total," +
                "strategy_id," +
                "strategy_tag_adv_id," +
                "advertiser_id," +
                "advertiser_app_id," +
                "advertiser_tag_id," +
                "media_id," +
                "media_app_id," +
                "media_tag_id," +
                "strategy_package_id," +
                "media_req_package_name," +
                "event_type " +
                "FROM statistics_media_advertiser_event " +
                "WHERE statistics_time>=? AND statistics_time<=? AND statistics_type=? " +
                "GROUP BY strategy_id,strategy_tag_adv_id,advertiser_id,advertiser_app_id,advertiser_tag_id,media_id,media_app_id,media_tag_id,event_type,strategy_package_id,media_req_package_name";
        List<Object> params = new LinkedList<>();
        params.add(beginTime);
        params.add(endTime);
        params.add(type.getCode());
        return this.findObjectListByClass(StatisticsMediaPackageAdvertiserEvent.class, sql, params.toArray());
    }

    @Override
    public void deleteByTime(StatisticsType type, String time) {
        String hql = "delete from StatisticsMediaPackageAdvertiserEvent where statisticsType=:statisticsType and statisticsTime=:statisticsTime";
        Map<String, Object> params = new HashMap<>();
        params.put("statisticsType", type.getCode());
        params.put("statisticsTime", time);
        this.execByHql(hql, params);
    }
}
