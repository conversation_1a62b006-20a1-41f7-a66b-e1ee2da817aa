package cn.taken.ad.core.dao.statistics;

import cn.taken.ad.component.orm.BaseSuperDao;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserRequest;

import java.util.List;

public interface StatisticsMediaPackageAdvertiserRequestDao extends BaseSuperDao<StatisticsMediaPackageAdvertiserRequest> {

    List<StatisticsMediaPackageAdvertiserRequest> findStatistics(String beginTime, String endTime, StatisticsType type);

    void deleteByTime(StatisticsType type, String time);

    List<StatisticsMediaPackageAdvertiserRequest> findMediaBundles(String beginTime, String endTime);
}
