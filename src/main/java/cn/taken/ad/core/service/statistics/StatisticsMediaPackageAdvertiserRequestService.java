package cn.taken.ad.core.service.statistics;

import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserRequest;

import java.util.List;

public interface StatisticsMediaPackageAdvertiserRequestService {

    void saveList(List<StatisticsMediaPackageAdvertiserRequest> list);

    List<StatisticsMediaPackageAdvertiserRequest> findStatistics(String beginTime, String endTime, StatisticsType type);

    void deleteByTime(StatisticsType type, String time);

    void deleteBeforeTime(StatisticsType statisticsType, String lastTime);

    List<StatisticsMediaPackageAdvertiserRequest> findMediaBundles(String beginTime, String endTime);
}
