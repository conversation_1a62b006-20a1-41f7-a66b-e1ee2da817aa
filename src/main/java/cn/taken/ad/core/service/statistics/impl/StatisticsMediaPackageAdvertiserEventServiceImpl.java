package cn.taken.ad.core.service.statistics.impl;

import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.dao.statistics.StatisticsMediaPackageAdvertiserEventDao;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserEvent;
import cn.taken.ad.core.service.statistics.StatisticsMediaPackageAdvertiserEventService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class StatisticsMediaPackageAdvertiserEventServiceImpl implements StatisticsMediaPackageAdvertiserEventService {

    @Resource
    private StatisticsMediaPackageAdvertiserEventDao statisticsMediaAdvertiserEventDao;

    @Override
    public void saveList(List<StatisticsMediaPackageAdvertiserEvent> list) {
        statisticsMediaAdvertiserEventDao.saveBatch(list);
    }

    @Override
    public List<StatisticsMediaPackageAdvertiserEvent> findStatistics(String beginTime, String endTime, StatisticsType type) {
        return statisticsMediaAdvertiserEventDao.findStatistics(beginTime, endTime, type);
    }

    @Override
    public void deleteByTime(StatisticsType type, String time) {
        statisticsMediaAdvertiserEventDao.deleteByTime(type, time);
    }

    @Override
    public void deleteBeforeTime(StatisticsType statisticsType, String lastTime) {
        statisticsMediaAdvertiserEventDao.deleteBeforeTime(statisticsType, lastTime);
    }
}
