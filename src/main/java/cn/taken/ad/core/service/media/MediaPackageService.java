package cn.taken.ad.core.service.media;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.core.dto.web.oper.media.mpackage.*;
import cn.taken.ad.core.dto.web.oper.media.tag.MediaTagListReq;
import cn.taken.ad.core.pojo.media.MediaPackage;

import java.util.Date;
import java.util.List;

public interface MediaPackageService {
    List<MediaPackage> findByLastUpdateTime(Date lastTime, int start, int limit);

    void saveBatch(List<MediaPackage> list);

    Page<MediaPackageInfo> findPage(MediaPackagePageReq req);

    List<MediaPackageInfo> findList(MediaPackageListReq req);

    SuperResult<String> add(MediaPackageAddReq req, Long userId);

    SuperResult<String> modify(MediaPackage req);

    MediaPackageInfo findInfoById(Long id);

    MediaPackage findById(Long id);
}
