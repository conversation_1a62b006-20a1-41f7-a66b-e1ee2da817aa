package cn.taken.ad.core.service.statistics;

import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserEvent;

import java.util.List;

public interface StatisticsMediaPackageAdvertiserEventService {

    void saveList(List<StatisticsMediaPackageAdvertiserEvent> list);

    List<StatisticsMediaPackageAdvertiserEvent> findStatistics(String beginTime, String endTime, StatisticsType type);

    void deleteByTime(StatisticsType type, String time);

    void deleteBeforeTime(StatisticsType statisticsType, String lastTime);
}
