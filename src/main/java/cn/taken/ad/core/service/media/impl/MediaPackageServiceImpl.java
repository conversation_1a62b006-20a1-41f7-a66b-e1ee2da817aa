package cn.taken.ad.core.service.media.impl;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.core.dao.media.MediaPackageDao;
import cn.taken.ad.core.dto.web.oper.media.mpackage.MediaPackageAddReq;
import cn.taken.ad.core.dto.web.oper.media.mpackage.MediaPackageInfo;
import cn.taken.ad.core.dto.web.oper.media.mpackage.MediaPackageListReq;
import cn.taken.ad.core.dto.web.oper.media.mpackage.MediaPackagePageReq;
import cn.taken.ad.core.dto.web.oper.media.tag.MediaTagListReq;
import cn.taken.ad.core.pojo.media.MediaPackage;
import cn.taken.ad.core.service.media.MediaPackageService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class MediaPackageServiceImpl implements MediaPackageService {

    @Resource
    private MediaPackageDao mediaPackageDao;

    @Override
    public List<MediaPackage> findByLastUpdateTime(Date lastTime, int start, int limit) {
        return mediaPackageDao.findByLastUpdateTime(lastTime, start, limit);
    }

    @Override
    public void saveBatch(List<MediaPackage> list) {
        mediaPackageDao.saveBatch(list);
    }

    @Override
    public Page<MediaPackageInfo> findPage(MediaPackagePageReq req) {
        return mediaPackageDao.findPage(req);
    }

    @Override
    public List<MediaPackageInfo> findList(MediaPackageListReq req) {
        return mediaPackageDao.findList(req);
    }

    @Override
    public SuperResult<String> add(MediaPackageAddReq req, Long userId) {
        MediaPackage mediaPackage = new MediaPackage(req, userId);
        mediaPackageDao.save(mediaPackage);
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<String> modify(MediaPackage req) {
        mediaPackageDao.update(req);
        return SuperResult.rightResult();
    }

    @Override
    public MediaPackageInfo findInfoById(Long id) {
        return mediaPackageDao.findInfoById(id);
    }

    @Override
    public MediaPackage findById(Long id) {
        return mediaPackageDao.findById(id);
    }


}
