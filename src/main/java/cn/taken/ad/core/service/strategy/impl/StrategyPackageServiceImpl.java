package cn.taken.ad.core.service.strategy.impl;

import cn.taken.ad.core.dao.strategy.StrategyPackageDao;
import cn.taken.ad.core.pojo.strategy.StrategyPackage;
import cn.taken.ad.core.service.strategy.StrategyPackageService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class StrategyPackageServiceImpl implements StrategyPackageService {

    @Resource
    private StrategyPackageDao strategyPackageDao;

    @Override
    public List<StrategyPackage> findByLastUpDateTime(Date lastTime, int start, int limit) {
        return strategyPackageDao.findByLastUpdateTime(lastTime, start, limit);
    }
}
