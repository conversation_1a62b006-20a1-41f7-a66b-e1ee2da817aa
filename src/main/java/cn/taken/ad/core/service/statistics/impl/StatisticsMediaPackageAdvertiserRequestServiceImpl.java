package cn.taken.ad.core.service.statistics.impl;

import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.dao.statistics.StatisticsMediaPackageAdvertiserRequestDao;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaPackageAdvertiserRequest;
import cn.taken.ad.core.service.statistics.StatisticsMediaPackageAdvertiserRequestService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class StatisticsMediaPackageAdvertiserRequestServiceImpl implements StatisticsMediaPackageAdvertiserRequestService {

    @Resource
    private StatisticsMediaPackageAdvertiserRequestDao statisticsMediaAdvertiserRequestDao;

    @Override
    public void saveList(List<StatisticsMediaPackageAdvertiserRequest> list) {
        statisticsMediaAdvertiserRequestDao.saveBatch(list);
    }

    @Override
    public List<StatisticsMediaPackageAdvertiserRequest> findStatistics(String beginTime, String endTime, StatisticsType type) {
        return statisticsMediaAdvertiserRequestDao.findStatistics(beginTime, endTime, type);
    }

    @Override
    public void deleteByTime(StatisticsType type, String time) {
        statisticsMediaAdvertiserRequestDao.deleteByTime(type, time);
    }

    @Override
    public void deleteBeforeTime(StatisticsType statisticsType, String lastTime) {
        statisticsMediaAdvertiserRequestDao.deleteBeforeTime(statisticsType, lastTime);
    }

    @Override
    public List<StatisticsMediaPackageAdvertiserRequest> findMediaBundles(String beginTime, String endTime) {
        return statisticsMediaAdvertiserRequestDao.findMediaBundles(beginTime, endTime);
    }

}
