package cn.taken.ad.core.dto.web.oper.media.mpackage;


import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class MediaPackageAddReq {

    @NotNull(message = "名称未填写")
    private String name;
    @NotNull(message = "应用名未填写")
    private String appName;
    @NotNull(message = "包名未填写")
    private String packageName;
    @NotNull(message = "所属媒体未填写")
    private Long mediaId;
    @NotNull(message = "所属媒体APPID")
    private Long mediaAppId;
    @NotNull(message = "所属媒体广告位")
    private Long mediaTagId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }
}
