package cn.taken.ad.core.dto.web.oper.media.mpackage;

import cn.taken.ad.core.pojo.media.MediaPackage;
import cn.taken.ad.core.pojo.media.MediaTag;

import java.math.BigDecimal;

public class MediaPackageInfo extends MediaPackage {

    private String mediaName;
    private String mediaCode;
    private String mediaAppName;
    private String mediaAppCode;
    private Integer mediaAppType;
    private String protocolName;
    private String protocolCode;
    private String protocolTagParam;
    private Integer protocolType;

    private String mediaTagCode;
    private String mediaTagName;
    private Integer type; // 广告位类型

    private Long reqTotal;
    private Long reqInvalidTotal;
    private Long participatingTotal;
    private Long winTotal;
    private BigDecimal amount;
    private Long maxTime;
    private Long minTime;
    private Long avgTime;

    // 上报
    private Long eventExposureTotal;
    private Long eventClickTotal;

    private Double mediaSettlementRatio;


    public String getMediaName() {
        return mediaName;
    }

    public void setMediaName(String mediaName) {
        this.mediaName = mediaName;
    }

    public String getMediaCode() {
        return mediaCode;
    }

    public void setMediaCode(String mediaCode) {
        this.mediaCode = mediaCode;
    }

    public String getMediaAppName() {
        return mediaAppName;
    }

    public void setMediaAppName(String mediaAppName) {
        this.mediaAppName = mediaAppName;
    }

    public String getMediaAppCode() {
        return mediaAppCode;
    }

    public void setMediaAppCode(String mediaAppCode) {
        this.mediaAppCode = mediaAppCode;
    }

    public Integer getMediaAppType() {
        return mediaAppType;
    }

    public void setMediaAppType(Integer mediaAppType) {
        this.mediaAppType = mediaAppType;
    }

    public String getProtocolName() {
        return protocolName;
    }

    public void setProtocolName(String protocolName) {
        this.protocolName = protocolName;
    }

    public String getProtocolCode() {
        return protocolCode;
    }

    public void setProtocolCode(String protocolCode) {
        this.protocolCode = protocolCode;
    }

    public String getProtocolTagParam() {
        return protocolTagParam;
    }

    public void setProtocolTagParam(String protocolTagParam) {
        this.protocolTagParam = protocolTagParam;
    }

    public Integer getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(Integer protocolType) {
        this.protocolType = protocolType;
    }

    public Long getReqTotal() {
        return reqTotal;
    }

    public void setReqTotal(Long reqTotal) {
        this.reqTotal = reqTotal;
    }

    public Long getReqInvalidTotal() {
        return reqInvalidTotal;
    }

    public void setReqInvalidTotal(Long reqInvalidTotal) {
        this.reqInvalidTotal = reqInvalidTotal;
    }

    public Long getParticipatingTotal() {
        return participatingTotal;
    }

    public void setParticipatingTotal(Long participatingTotal) {
        this.participatingTotal = participatingTotal;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getWinTotal() {
        return winTotal;
    }

    public void setWinTotal(Long winTotal) {
        this.winTotal = winTotal;
    }

    public Long getEventExposureTotal() {
        return eventExposureTotal;
    }

    public void setEventExposureTotal(Long eventExposureTotal) {
        this.eventExposureTotal = eventExposureTotal;
    }

    public Long getEventClickTotal() {
        return eventClickTotal;
    }

    public void setEventClickTotal(Long eventClickTotal) {
        this.eventClickTotal = eventClickTotal;
    }

    public Long getMaxTime() {
        return maxTime;
    }

    public void setMaxTime(Long maxTime) {
        this.maxTime = maxTime;
    }

    public Long getMinTime() {
        return minTime;
    }

    public void setMinTime(Long minTime) {
        this.minTime = minTime;
    }

    public Long getAvgTime() {
        return avgTime;
    }

    public void setAvgTime(Long avgTime) {
        this.avgTime = avgTime;
    }

    public Double getMediaSettlementRatio() {
        return mediaSettlementRatio;
    }

    public void setMediaSettlementRatio(Double mediaSettlementRatio) {
        this.mediaSettlementRatio = mediaSettlementRatio;
    }

    public String getMediaTagCode() {
        return mediaTagCode;
    }

    public void setMediaTagCode(String mediaTagCode) {
        this.mediaTagCode = mediaTagCode;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMediaTagName() {
        return mediaTagName;
    }

    public void setMediaTagName(String mediaTagName) {
        this.mediaTagName = mediaTagName;
    }

}
