package cn.taken.ad.core.dto.web.oper.media.mpackage;


import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class MediaPackageModifyReq {

    @NotNull(message = "信息不存在")
    private Long id ;
    @NotNull(message = "名称未填写")
    private String name;
    @NotNull(message = "包名未填写")
    private String packageName;
    @NotNull(message = "应用名未填写")
    private String appName;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }
}
