package cn.taken.ad.core.dto.business.statistics;

import java.util.concurrent.atomic.AtomicInteger;

public class MediaPackageAdvertiserReqMonitorDto {
    //key
    private Long strategyId;
    private Long strategyTagAdvId;
    private Long strategyPackageId;
    private String mediaReqPackageName;
    private Long mediaId;
    private Long mediaAppId;
    private Long mediaTagId;
    private Long advertiserId;
    private Long advertiserAppId;
    private Long advertiserTagId;
    //媒体计数
    private AtomicInteger mediaReqTotal = new AtomicInteger(0);
    private AtomicInteger mediaReqInvalidTotal = new AtomicInteger(0);
    private AtomicInteger mediaRespFailTotal = new AtomicInteger(0);
    private AtomicInteger mediaParticipatingTotal = new AtomicInteger(0);

    //媒体耗时
    private AtomicInteger mediaMinTime = new AtomicInteger(Integer.MAX_VALUE);
    private AtomicInteger mediaMaxTime = new AtomicInteger(0);
    private AtomicInteger mediaUseTimeTotal = new AtomicInteger(0);

    //预算计数
    private AtomicInteger advertiserReqTotal = new AtomicInteger(0);
    private AtomicInteger advertiserReqSuccessTotal = new AtomicInteger(0);
    private AtomicInteger advertiserReqFailTotal = new AtomicInteger(0);
    private AtomicInteger advertiserRespFailTotal = new AtomicInteger(0);
    private AtomicInteger advertiserReqTimeoutTotal = new AtomicInteger(0);
    private AtomicInteger advertiserParticipatingTotal = new AtomicInteger(0);
    //最小计数
    private AtomicInteger advertiserMinTime = new AtomicInteger(Integer.MAX_VALUE);
    //最大计数
    private AtomicInteger advertiserMaxTime = new AtomicInteger(0);
    //总耗时
    private AtomicInteger advertiserUseTimeTotal = new AtomicInteger(0);

    public MediaPackageAdvertiserReqMonitorDto() {
    }

    public MediaPackageAdvertiserReqMonitorDto(Long strategyId, Long strategyTagAdvId, Long mediaId, Long mediaAppId, Long mediaTagId, Long advertiserId, Long advertiserAppId, Long advertiserTagId, Long strategyPackageId, String mediaReqPackageName) {
        this.strategyId = strategyId;
        this.strategyTagAdvId = strategyTagAdvId;
        this.mediaId = mediaId;
        this.mediaAppId = mediaAppId;
        this.mediaTagId = mediaTagId;
        this.advertiserId = advertiserId;
        this.advertiserAppId = advertiserAppId;
        this.advertiserTagId = advertiserTagId;
        this.strategyPackageId = strategyPackageId;
        this.mediaReqPackageName = mediaReqPackageName;
    }

    public Long getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    public Long getStrategyTagAdvId() {
        return strategyTagAdvId;
    }

    public void setStrategyTagAdvId(Long strategyTagAdvId) {
        this.strategyTagAdvId = strategyTagAdvId;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserAppId() {
        return advertiserAppId;
    }

    public void setAdvertiserAppId(Long advertiserAppId) {
        this.advertiserAppId = advertiserAppId;
    }

    public Long getAdvertiserTagId() {
        return advertiserTagId;
    }

    public void setAdvertiserTagId(Long advertiserTagId) {
        this.advertiserTagId = advertiserTagId;
    }

    public AtomicInteger getMediaReqTotal() {
        return mediaReqTotal;
    }

    public void setMediaReqTotal(AtomicInteger mediaReqTotal) {
        this.mediaReqTotal = mediaReqTotal;
    }

    public AtomicInteger getMediaReqInvalidTotal() {
        return mediaReqInvalidTotal;
    }

    public void setMediaReqInvalidTotal(AtomicInteger mediaReqInvalidTotal) {
        this.mediaReqInvalidTotal = mediaReqInvalidTotal;
    }

    public AtomicInteger getMediaRespFailTotal() {
        return mediaRespFailTotal;
    }

    public void setMediaRespFailTotal(AtomicInteger mediaRespFailTotal) {
        this.mediaRespFailTotal = mediaRespFailTotal;
    }

    public AtomicInteger getMediaParticipatingTotal() {
        return mediaParticipatingTotal;
    }

    public void setMediaParticipatingTotal(AtomicInteger mediaParticipatingTotal) {
        this.mediaParticipatingTotal = mediaParticipatingTotal;
    }

    public AtomicInteger getMediaMinTime() {
        return mediaMinTime;
    }

    public void setMediaMinTime(AtomicInteger mediaMinTime) {
        this.mediaMinTime = mediaMinTime;
    }

    public AtomicInteger getMediaMaxTime() {
        return mediaMaxTime;
    }

    public void setMediaMaxTime(AtomicInteger mediaMaxTime) {
        this.mediaMaxTime = mediaMaxTime;
    }

    public AtomicInteger getMediaUseTimeTotal() {
        return mediaUseTimeTotal;
    }

    public void setMediaUseTimeTotal(AtomicInteger mediaUseTimeTotal) {
        this.mediaUseTimeTotal = mediaUseTimeTotal;
    }

    public AtomicInteger getAdvertiserReqTotal() {
        return advertiserReqTotal;
    }

    public void setAdvertiserReqTotal(AtomicInteger advertiserReqTotal) {
        this.advertiserReqTotal = advertiserReqTotal;
    }

    public AtomicInteger getAdvertiserReqSuccessTotal() {
        return advertiserReqSuccessTotal;
    }

    public void setAdvertiserReqSuccessTotal(AtomicInteger advertiserReqSuccessTotal) {
        this.advertiserReqSuccessTotal = advertiserReqSuccessTotal;
    }

    public AtomicInteger getAdvertiserReqFailTotal() {
        return advertiserReqFailTotal;
    }

    public void setAdvertiserReqFailTotal(AtomicInteger advertiserReqFailTotal) {
        this.advertiserReqFailTotal = advertiserReqFailTotal;
    }

    public AtomicInteger getAdvertiserRespFailTotal() {
        return advertiserRespFailTotal;
    }

    public void setAdvertiserRespFailTotal(AtomicInteger advertiserRespFailTotal) {
        this.advertiserRespFailTotal = advertiserRespFailTotal;
    }

    public AtomicInteger getAdvertiserReqTimeoutTotal() {
        return advertiserReqTimeoutTotal;
    }

    public void setAdvertiserReqTimeoutTotal(AtomicInteger advertiserReqTimeoutTotal) {
        this.advertiserReqTimeoutTotal = advertiserReqTimeoutTotal;
    }

    public AtomicInteger getAdvertiserParticipatingTotal() {
        return advertiserParticipatingTotal;
    }

    public void setAdvertiserParticipatingTotal(AtomicInteger advertiserParticipatingTotal) {
        this.advertiserParticipatingTotal = advertiserParticipatingTotal;
    }


    public AtomicInteger getAdvertiserMinTime() {
        return advertiserMinTime;
    }

    public void setAdvertiserMinTime(AtomicInteger advertiserMinTime) {
        this.advertiserMinTime = advertiserMinTime;
    }

    public AtomicInteger getAdvertiserMaxTime() {
        return advertiserMaxTime;
    }

    public void setAdvertiserMaxTime(AtomicInteger advertiserMaxTime) {
        this.advertiserMaxTime = advertiserMaxTime;
    }

    public AtomicInteger getAdvertiserUseTimeTotal() {
        return advertiserUseTimeTotal;
    }

    public void setAdvertiserUseTimeTotal(AtomicInteger advertiserUseTimeTotal) {
        this.advertiserUseTimeTotal = advertiserUseTimeTotal;
    }

    public void updateMediaMaxConcurrentValue(int value) {
        this.mediaMaxTime.updateAndGet(current -> Math.max(current, value));
    }

    public void updateMediaMinConcurrentValue(int value) {
        this.mediaMinTime.updateAndGet(current -> Math.min(current, value));
    }


    public void updateMaxConcurrentValue(int value) {
        this.advertiserMaxTime.updateAndGet(current -> Math.max(current, value));
    }

    public void updateMinConcurrentValue(int value) {
        this.advertiserMinTime.updateAndGet(current -> Math.min(current, value));
    }

    public Long getStrategyPackageId() {
        return strategyPackageId;
    }

    public void setStrategyPackageId(Long strategyPackageId) {
        this.strategyPackageId = strategyPackageId;
    }

    public String getMediaReqPackageName() {
        return mediaReqPackageName;
    }

    public void setMediaReqPackageName(String mediaReqPackageName) {
        this.mediaReqPackageName = mediaReqPackageName;
    }
}
