package cn.taken.ad.core.dto.web.oper.strategy.main;

import cn.taken.ad.core.dto.global.PageReq;

import javax.validation.constraints.NotNull;

/**
 * 策略分包分页查询请求
 */
public class StrategyPackagePageReq extends PageReq {

    /**
     * 媒体广告位ID
     */
    @NotNull(message = "媒体广告位ID不能为空")
    private Long mediaTagId;

    /**
     * 策略状态：0:停用,1:启用
     */
    private Integer state;

    /**
     * 策略模式，1：统一模式，2:分包模式
     */
    private Integer model;

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getModel() {
        return model;
    }

    public void setModel(Integer model) {
        this.model = model;
    }
}
