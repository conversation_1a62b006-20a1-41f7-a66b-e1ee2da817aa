package cn.taken.ad.core.dto.web.oper.advertiser.tag;


import cn.taken.ad.core.pojo.advertiser.AdvertiserTag;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Valid
public class AdvertiserTagAddReqBase extends AdvertiserTagOperReqBase {

    @NotNull(message = "未选择预算")
    private Long advertiserId;
    @NotNull(message = "未选择预算应用")
    private Long advertiserAppId;
    private Integer dspType;
    private Long accountId;

    public AdvertiserTag toAdvertiserTag(Long operatorId) {
        AdvertiserTag tag = new AdvertiserTag();
        tag.setAdvertiserId(advertiserId);
        tag.setAdvertiserAppId(advertiserAppId);
        this.fillValue(tag);
        tag.setCreateTime(new Date());
        tag.setOperatorId(operatorId);
        tag.setDspType(this.dspType);
        tag.setAccountId(this.getAccountId());
        return tag;
    }

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserAppId() {
        return advertiserAppId;
    }

    public void setAdvertiserAppId(Long advertiserAppId) {
        this.advertiserAppId = advertiserAppId;
    }

    public Integer getDspType() {
        return dspType;
    }

    public void setDspType(Integer dspType) {
        this.dspType = dspType;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }
}
