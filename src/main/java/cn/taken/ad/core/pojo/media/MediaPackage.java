package cn.taken.ad.core.pojo.media;

import cn.taken.ad.core.dto.web.oper.media.mpackage.MediaPackageAddReq;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "media_package")
public class MediaPackage implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 媒体id
     */
    @Column(name = "media_id")
    private Long mediaId;
    /**
     * 媒体appId
     */
    @Column(name = "media_app_id")
    private Long mediaAppId;

    /**
     * 媒体广告位id
     */
    @Column(name = "media_tag_id")
    private Long mediaTagId;
    /***
     * APP名称
     */
    @Column(name = "name")
    private String name;
    /***
     * 包名
     */
    @Column(name = "package_name")
    private String packageName;

    /***
     * 包名
     */
    @Column(name = "app_name")
    private String appName;


    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private Long operatorId;
    /**
     * 创建时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time")
    private Date createTime;

    public MediaPackage() {
    }

    public MediaPackage(MediaPackageAddReq req, Long userId) {
        this.mediaId = req.getMediaId();
        this.mediaAppId = req.getMediaAppId();
        this.mediaTagId = req.getMediaTagId();
        this.name = req.getName();
        this.packageName = req.getPackageName();
        this.appName = req.getAppName();
        this.operatorId = userId;
        this.createTime = new Date();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
