package cn.taken.ad.core.pojo.statistics;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;


@Entity
@Table(name = "statistics_media_package_advertiser_request")
public class StatisticsMediaPackageAdvertiserRequest implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "statistics_type")
    private String statisticsType;

    @Column(name = "statistics_time")
    private String statisticsTime;

    /***
     * 分量策略ID
     */
    @Column(name = "strategy_id")
    private Long strategyId;

    @Column(name = "strategy_package_id")
    private Long strategyPackageId;

    @Column(name = "strategy_tag_adv_id")
    private Long strategyTagAdvId;


    @Column(name = "media_id")
    private Long mediaId;

    @Column(name = "media_app_id")
    private Long mediaAppId;

    @Column(name = "media_tag_id")
    private Long mediaTagId;

    @Column(name = "media_req_package_name")
    private String mediaReqPackageName;

    @Column(name = "media_req_app_name")
    private String mediaReqAppName;


    @Column(name = "media_req_total")
    private Long mediaReqTotal;

    /**
     * 无效请求量
     */
    @Column(name = "media_req_invalid_total")
    private Long mediaReqInvalidTotal;

    /**
     * 失败响应量
     */
    @Column(name = "media_resp_fail_total")
    private Long mediaRespFailTotal;

    /**
     * 参竟量
     */
    @Column(name = "media_participating_total")
    private Long mediaParticipatingTotal;

    /**
     * 参竟量
     */
    @Column(name = "media_win_total")
    private Long mediaWinTotal;

    /***
     * 消耗金额: 元
     */
    @Column(name = "media_amount")
    private BigDecimal mediaAmount;

    /***
     * 最高请求耗时
     */
    @Column(name = "media_max_time")
    private Long mediaMaxTime;

    /***
     * 平均请求耗时
     */
    @Column(name = "media_avg_time")
    private Long mediaAvgTime;

    /***
     * 最小请求耗时
     */
    @Column(name = "media_min_time")
    private Long mediaMinTime;

    /***
     * 总耗时
     */
    @Column(name = "media_use_time_total")
    private Long mediaUseTimeTotal;


    /***
     * 预算ID
     */
    @Column(name = "advertiser_id")
    private Long advertiserId;

    /***
     * 预算APPID
     */
    @Column(name = "advertiser_app_id")
    private Long advertiserAppId;

    /***
     * 预算广告位ID
     */
    @Column(name = "advertiser_tag_id")
    private Long advertiserTagId;

    /***
     * 请求总数
     */
    @Column(name = "advertiser_req_total")
    private Long advertiserReqTotal;

    /**
     * 请求成功量
     */
    @Column(name = "advertiser_req_success_total")
    private Long advertiserReqSuccessTotal;

    /**
     * 请求失败总数
     */
    @Column(name = "advertiser_req_fail_total")
    private Long advertiserReqFailTotal;

    /**
     * 响应失败量
     */
    @Column(name = "advertiser_resp_fail_total")
    private Long advertiserRespFailTotal;

    /**
     * 请求超时量
     */
    @Column(name = "advertiser_req_timeout_total")
    private Long advertiserReqTimeoutTotal;

    /***
     * 填充量
     */
    @Column(name = "advertiser_participating_total")
    private Long advertiserParticipatingTotal;

    /***
     * 竟胜数量
     */
    @Column(name = "advertiser_win_total")
    private Long advertiserWinTotal;

    /***
     * 消耗金额: 元
     */
    @Column(name = "advertiser_amount")
    private BigDecimal advertiserAmount;

    /***
     * 最高请求耗时
     */
    @Column(name = "advertiser_max_time")
    private Long advertiserMaxTime;

    /***
     * 平均请求耗时
     */
    @Column(name = "advertiser_avg_time")
    private Long advertiserAvgTime;

    /***
     * 最小请求耗时
     */
    @Column(name = "advertiser_min_time")
    private Long advertiserMinTime;

    /***
     * 总耗时
     */
    @Column(name = "advertiser_use_time_total")
    private Long advertiserUseTimeTotal;

    public StatisticsMediaPackageAdvertiserRequest() {
    }

    public StatisticsMediaPackageAdvertiserRequest(String statisticsType, String statisticsTime) {
        this.statisticsType = statisticsType;
        this.statisticsTime = statisticsTime;
        this.mediaReqTotal = 0L;
        this.mediaReqInvalidTotal = 0L;
        this.mediaRespFailTotal = 0L;
        this.mediaParticipatingTotal = 0L;
        this.mediaWinTotal = 0L;
        this.mediaAmount = BigDecimal.ZERO;
        this.mediaMaxTime = 0L;
        this.mediaAvgTime = 0L;
        this.mediaMinTime = 0L;
        this.mediaUseTimeTotal = 0L;
        this.advertiserId = 0L;
        this.advertiserAppId = 0L;
        this.advertiserTagId = 0L;
        this.advertiserReqTotal = 0L;
        this.advertiserReqSuccessTotal = 0L;
        this.advertiserReqFailTotal = 0L;
        this.advertiserRespFailTotal = 0L;
        this.advertiserReqTimeoutTotal = 0L;
        this.advertiserParticipatingTotal = 0L;
        this.advertiserWinTotal = 0L;
        this.advertiserAmount = BigDecimal.ZERO;
        this.advertiserMaxTime = 0L;
        this.advertiserAvgTime = 0L;
        this.advertiserMinTime = 0L;
        this.advertiserUseTimeTotal = 0L;
    }


    public StatisticsMediaPackageAdvertiserRequest(String statisticsType, String statisticsTime, Long strategyId, Long strategyTagAdvId, Long mediaId, Long mediaAppId, Long mediaTagId, Long advertiserId, Long advertiserAppId, Long advertiserTagId, Long strategyPackageId, String mediaReqPackageName) {
        this.statisticsType = statisticsType;
        this.statisticsTime = statisticsTime;
        this.strategyId = strategyId;
        this.strategyTagAdvId = strategyTagAdvId;
        this.mediaId = mediaId;
        this.mediaAppId = mediaAppId;
        this.mediaTagId = mediaTagId;
        this.mediaReqTotal = 0L;
        this.mediaReqInvalidTotal = 0L;
        this.mediaRespFailTotal = 0L;
        this.mediaParticipatingTotal = 0L;
        this.mediaWinTotal = 0L;
        this.mediaAmount = BigDecimal.ZERO;
        this.mediaMaxTime = 0L;
        this.mediaAvgTime = 0L;
        this.mediaMinTime = 0L;
        this.mediaUseTimeTotal = 0L;


        this.advertiserId = advertiserId;
        this.advertiserAppId = advertiserAppId;
        this.advertiserTagId = advertiserTagId;
        this.advertiserReqTotal = 0L;
        this.advertiserReqSuccessTotal = 0L;
        this.advertiserReqFailTotal = 0L;
        this.advertiserRespFailTotal = 0L;
        this.advertiserReqTimeoutTotal = 0L;
        this.advertiserParticipatingTotal = 0L;
        this.advertiserWinTotal = 0L;
        this.advertiserAmount = BigDecimal.ZERO;
        this.advertiserMaxTime = 0L;
        this.advertiserAvgTime = 0L;
        this.advertiserMinTime = 0L;
        this.advertiserUseTimeTotal = 0L;
        this.strategyPackageId = strategyPackageId;
        this.mediaReqPackageName = mediaReqPackageName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatisticsType() {
        return statisticsType;
    }

    public void setStatisticsType(String statisticsType) {
        this.statisticsType = statisticsType;
    }

    public String getStatisticsTime() {
        return statisticsTime;
    }

    public void setStatisticsTime(String statisticsTime) {
        this.statisticsTime = statisticsTime;
    }

    public Long getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    public Long getStrategyTagAdvId() {
        return strategyTagAdvId;
    }

    public void setStrategyTagAdvId(Long strategyTagAdvId) {
        this.strategyTagAdvId = strategyTagAdvId;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Long getMediaReqTotal() {
        return mediaReqTotal;
    }

    public void setMediaReqTotal(Long mediaReqTotal) {
        this.mediaReqTotal = mediaReqTotal;
    }

    public Long getMediaReqInvalidTotal() {
        return mediaReqInvalidTotal;
    }

    public void setMediaReqInvalidTotal(Long mediaReqInvalidTotal) {
        this.mediaReqInvalidTotal = mediaReqInvalidTotal;
    }

    public Long getMediaRespFailTotal() {
        return mediaRespFailTotal;
    }

    public void setMediaRespFailTotal(Long mediaRespFailTotal) {
        this.mediaRespFailTotal = mediaRespFailTotal;
    }

    public Long getMediaParticipatingTotal() {
        return mediaParticipatingTotal;
    }

    public void setMediaParticipatingTotal(Long mediaParticipatingTotal) {
        this.mediaParticipatingTotal = mediaParticipatingTotal;
    }

    public Long getMediaWinTotal() {
        return mediaWinTotal;
    }

    public void setMediaWinTotal(Long mediaWinTotal) {
        this.mediaWinTotal = mediaWinTotal;
    }

    public BigDecimal getMediaAmount() {
        return mediaAmount;
    }

    public void setMediaAmount(BigDecimal mediaAmount) {
        this.mediaAmount = mediaAmount;
    }

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserAppId() {
        return advertiserAppId;
    }

    public void setAdvertiserAppId(Long advertiserAppId) {
        this.advertiserAppId = advertiserAppId;
    }

    public Long getAdvertiserTagId() {
        return advertiserTagId;
    }

    public void setAdvertiserTagId(Long advertiserTagId) {
        this.advertiserTagId = advertiserTagId;
    }

    public Long getAdvertiserReqTotal() {
        return advertiserReqTotal;
    }

    public void setAdvertiserReqTotal(Long advertiserReqTotal) {
        this.advertiserReqTotal = advertiserReqTotal;
    }

    public Long getAdvertiserReqSuccessTotal() {
        return advertiserReqSuccessTotal;
    }

    public void setAdvertiserReqSuccessTotal(Long advertiserReqSuccessTotal) {
        this.advertiserReqSuccessTotal = advertiserReqSuccessTotal;
    }

    public Long getAdvertiserReqFailTotal() {
        return advertiserReqFailTotal;
    }

    public void setAdvertiserReqFailTotal(Long advertiserReqFailTotal) {
        this.advertiserReqFailTotal = advertiserReqFailTotal;
    }

    public Long getAdvertiserRespFailTotal() {
        return advertiserRespFailTotal;
    }

    public void setAdvertiserRespFailTotal(Long advertiserRespFailTotal) {
        this.advertiserRespFailTotal = advertiserRespFailTotal;
    }

    public Long getAdvertiserReqTimeoutTotal() {
        return advertiserReqTimeoutTotal;
    }

    public void setAdvertiserReqTimeoutTotal(Long advertiserReqTimeoutTotal) {
        this.advertiserReqTimeoutTotal = advertiserReqTimeoutTotal;
    }

    public Long getAdvertiserParticipatingTotal() {
        return advertiserParticipatingTotal;
    }

    public void setAdvertiserParticipatingTotal(Long advertiserParticipatingTotal) {
        this.advertiserParticipatingTotal = advertiserParticipatingTotal;
    }

    public Long getAdvertiserWinTotal() {
        return advertiserWinTotal;
    }

    public void setAdvertiserWinTotal(Long advertiserWinTotal) {
        this.advertiserWinTotal = advertiserWinTotal;
    }

    public BigDecimal getAdvertiserAmount() {
        return advertiserAmount;
    }

    public void setAdvertiserAmount(BigDecimal advertiserAmount) {
        this.advertiserAmount = advertiserAmount;
    }

    public Long getAdvertiserMaxTime() {
        return advertiserMaxTime;
    }

    public void setAdvertiserMaxTime(Long advertiserMaxTime) {
        this.advertiserMaxTime = advertiserMaxTime;
    }

    public Long getAdvertiserAvgTime() {
        return advertiserAvgTime;
    }

    public void setAdvertiserAvgTime(Long advertiserAvgTime) {
        this.advertiserAvgTime = advertiserAvgTime;
    }

    public Long getAdvertiserMinTime() {
        return advertiserMinTime;
    }

    public void setAdvertiserMinTime(Long advertiserMinTime) {
        this.advertiserMinTime = advertiserMinTime;
    }

    public Long getAdvertiserUseTimeTotal() {
        return advertiserUseTimeTotal;
    }

    public void setAdvertiserUseTimeTotal(Long advertiserUseTimeTotal) {
        this.advertiserUseTimeTotal = advertiserUseTimeTotal;
    }

    public Long getMediaMaxTime() {
        return mediaMaxTime;
    }

    public void setMediaMaxTime(Long mediaMaxTime) {
        this.mediaMaxTime = mediaMaxTime;
    }

    public Long getMediaAvgTime() {
        return mediaAvgTime;
    }

    public void setMediaAvgTime(Long mediaAvgTime) {
        this.mediaAvgTime = mediaAvgTime;
    }

    public Long getMediaMinTime() {
        return mediaMinTime;
    }

    public void setMediaMinTime(Long mediaMinTime) {
        this.mediaMinTime = mediaMinTime;
    }

    public Long getMediaUseTimeTotal() {
        return mediaUseTimeTotal;
    }

    public void setMediaUseTimeTotal(Long mediaUseTimeTotal) {
        this.mediaUseTimeTotal = mediaUseTimeTotal;
    }

    public Long getStrategyPackageId() {
        return strategyPackageId;
    }

    public void setStrategyPackageId(Long strategyPackageId) {
        this.strategyPackageId = strategyPackageId;
    }

    public String getMediaReqPackageName() {
        return mediaReqPackageName;
    }

    public void setMediaReqPackageName(String mediaReqPackageName) {
        this.mediaReqPackageName = mediaReqPackageName;
    }

    public String getMediaReqAppName() {
        return mediaReqAppName;
    }

    public void setMediaReqAppName(String mediaReqAppName) {
        this.mediaReqAppName = mediaReqAppName;
    }
}
