package cn.taken.ad.core.pojo.strategy;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "strategy_package")
public class StrategyPackage implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "strategy_id")
    private Long strategyId;

    @Column(name = "media_package_id")
    private Long mediaPackageId;

    /**
     * 媒体id
     */
    @Column(name = "media_id")
    private Long mediaId;
    /**
     * 媒体appId
     */
    @Column(name = "media_app_id")
    private Long mediaAppId;

    /**
     * 媒体广告位id
     */
    @Column(name = "media_tag_id")
    private Long mediaTagId;

    /**
     * 状态：0:停用,1:启用
     */
    @Column(name = "state")
    private Integer state;
    /**
     * 分量规则，1:随机，2:并行
     */
    @Column(name = "quantity_limit_type")
    private Integer quantityLimitType;
    /**
     * 并行最大数量，分量规则为并行时，最多请求几个预算
     */
    @Column(name = "parallel_max_adv")
    private Integer parallelMaxAdv;
    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private Long operatorId;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "is_delete")
    private Integer isDelete;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getQuantityLimitType() {
        return quantityLimitType;
    }

    public void setQuantityLimitType(Integer quantityLimitType) {
        this.quantityLimitType = quantityLimitType;
    }

    public Integer getParallelMaxAdv() {
        return parallelMaxAdv;
    }

    public void setParallelMaxAdv(Integer parallelMaxAdv) {
        this.parallelMaxAdv = parallelMaxAdv;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getMediaPackageId() {
        return mediaPackageId;
    }

    public void setMediaPackageId(Long mediaPackageId) {
        this.mediaPackageId = mediaPackageId;
    }

    public Long getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }
}
