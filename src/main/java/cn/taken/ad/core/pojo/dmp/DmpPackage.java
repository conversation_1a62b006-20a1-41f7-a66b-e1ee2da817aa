package cn.taken.ad.core.pojo.dmp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "dmp_package")
public class DmpPackage implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "name")
    private String name;
    /**
     * 类型：1-上传，2-实时
     */
    @Column(name = "type")
    private Integer type;
    /**
     * 基础类型：1-曝光，2-点击, 3-转化
     */
    @Column(name = "base_type")
    private Integer baseType;
    /**
     * 媒体ID集合
     */
    @Column(name = "base_media_ids")
    private String baseMediaIds;
    /**
     * 媒体APPID集合
     */
    @Column(name = "base_media_app_ids")
    private String baseMediaAppIds;
    /**
     * 媒体广告位ID集合
     */
    @Column(name = "base_media_tag_ids")
    private String baseMediaTagIds;
    /**
     * 预算ID集合
     */
    @Column(name = "base_adv_ids")
    private String baseAdvIds;
    /**
     * 预算APPID集合
     */
    @Column(name = "base_adv_app_ids")
    private String baseAdvAppIds;
    /**
     * 预算广告位ID集合
     */
    @Column(name = "base_adv_tag_ids")
    private String baseAdvTagIds;
    @Column(name = "remark")
    private String remark;
    /**
     * 总计数量
     */
    @Column(name = "total")
    private Long total;
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 1当前2永久
     */
    @Column(name = "cache_type")
    private Integer cacheType;
    @Column(name = "cache_day")
    private Integer cacheDay;

    @Column(name = "operator_id")
    private Long operatorId;
    /**
     * 状态(0-关闭,1-开启)
     */
    @Column(name = "state")
    private Integer state;
    /**
     * 构建状态0待构建1构建成功-1构建失败'
     */
    @Column(name = "base_build_state")
    private Integer baseBuildState;
    /**
     * 待构建的文件
     */
    @Column(name = "base_build_file")
    private String baseBuildFile;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getBaseType() {
        return baseType;
    }

    public void setBaseType(Integer baseType) {
        this.baseType = baseType;
    }

    public String getBaseMediaIds() {
        return baseMediaIds;
    }

    public void setBaseMediaIds(String baseMediaIds) {
        this.baseMediaIds = baseMediaIds;
    }

    public String getBaseMediaAppIds() {
        return baseMediaAppIds;
    }

    public void setBaseMediaAppIds(String baseMediaAppIds) {
        this.baseMediaAppIds = baseMediaAppIds;
    }

    public String getBaseMediaTagIds() {
        return baseMediaTagIds;
    }

    public void setBaseMediaTagIds(String baseMediaTagIds) {
        this.baseMediaTagIds = baseMediaTagIds;
    }

    public String getBaseAdvIds() {
        return baseAdvIds;
    }

    public void setBaseAdvIds(String baseAdvIds) {
        this.baseAdvIds = baseAdvIds;
    }

    public String getBaseAdvAppIds() {
        return baseAdvAppIds;
    }

    public void setBaseAdvAppIds(String baseAdvAppIds) {
        this.baseAdvAppIds = baseAdvAppIds;
    }

    public String getBaseAdvTagIds() {
        return baseAdvTagIds;
    }

    public void setBaseAdvTagIds(String baseAdvTagIds) {
        this.baseAdvTagIds = baseAdvTagIds;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getBaseBuildState() {
        return baseBuildState;
    }

    public void setBaseBuildState(Integer baseBuildState) {
        this.baseBuildState = baseBuildState;
    }

    public String getBaseBuildFile() {
        return baseBuildFile;
    }

    public void setBaseBuildFile(String baseBuildFile) {
        this.baseBuildFile = baseBuildFile;
    }

    public Integer getCacheType() {
        return cacheType;
    }

    public void setCacheType(Integer cacheType) {
        this.cacheType = cacheType;
    }

    public Integer getCacheDay() {
        return cacheDay;
    }

    public void setCacheDay(Integer cacheDay) {
        this.cacheDay = cacheDay;
    }
}
