package cn.taken.ad.core.pojo.statistics;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "statistics_media_package_advertiser_event")
public class StatisticsMediaPackageAdvertiserEvent implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "statistics_type")
    private String statisticsType;

    @Column(name = "statistics_time")
    private String statisticsTime;

    @Column(name = "strategy_id")
    private Long strategyId;

    @Column(name = "strategy_package_id")
    private Long strategyPackageId;

    @Column(name = "strategy_tag_adv_id")
    private Long strategyTagAdvId;

    @Column(name = "media_id")
    private Long mediaId;

    @Column(name = "media_app_id")
    private Long mediaAppId;

    @Column(name = "media_tag_id")
    private Long mediaTagId;

    @Column(name = "media_req_package_name")
    private String mediaReqPackageName;

    @Column(name = "media_req_app_name")
    private String mediaReqAppName;

    @Column(name = "advertiser_id")
    private Long advertiserId;

    @Column(name = "advertiser_app_id")
    private Long advertiserAppId;

    @Column(name = "advertiser_tag_id")
    private Long advertiserTagId;

    @Column(name = "event_type")
    private Integer eventType;

    @Column(name = "total")
    private Long total;

    @Column(name = "repeat_total")
    private Long repeatTotal;

    public StatisticsMediaPackageAdvertiserEvent() {
    }

    public StatisticsMediaPackageAdvertiserEvent(String statisticsType, String statisticsTime, Long strategyId, Long strategyTagAdvId, Long mediaId, Long mediaAppId, Long mediaTagId, Long advertiserId, Long advertiserAppId, Long advertiserTagId, Integer eventType, Long strategyPackageId, String mediaReqPackageName) {
        this.statisticsType = statisticsType;
        this.statisticsTime = statisticsTime;
        this.strategyId = strategyId;
        this.strategyTagAdvId = strategyTagAdvId;
        this.mediaId = mediaId;
        this.mediaAppId = mediaAppId;
        this.mediaTagId = mediaTagId;
        this.advertiserId = advertiserId;
        this.advertiserAppId = advertiserAppId;
        this.advertiserTagId = advertiserTagId;
        this.eventType = eventType;
        this.total = 0L;
        this.repeatTotal = 0L;
        this.strategyPackageId = strategyPackageId;
        this.mediaReqPackageName = mediaReqPackageName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatisticsType() {
        return statisticsType;
    }

    public void setStatisticsType(String statisticsType) {
        this.statisticsType = statisticsType;
    }

    public String getStatisticsTime() {
        return statisticsTime;
    }

    public void setStatisticsTime(String statisticsTime) {
        this.statisticsTime = statisticsTime;
    }

    public Long getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserAppId() {
        return advertiserAppId;
    }

    public void setAdvertiserAppId(Long advertiserAppId) {
        this.advertiserAppId = advertiserAppId;
    }

    public Long getAdvertiserTagId() {
        return advertiserTagId;
    }

    public void setAdvertiserTagId(Long advertiserTagId) {
        this.advertiserTagId = advertiserTagId;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getStrategyTagAdvId() {
        return strategyTagAdvId;
    }

    public void setStrategyTagAdvId(Long strategyTagAdvId) {
        this.strategyTagAdvId = strategyTagAdvId;
    }

    public Long getRepeatTotal() {
        return repeatTotal;
    }

    public void setRepeatTotal(Long repeatTotal) {
        this.repeatTotal = repeatTotal;
    }

    public Long getStrategyPackageId() {
        return strategyPackageId;
    }

    public void setStrategyPackageId(Long strategyPackageId) {
        this.strategyPackageId = strategyPackageId;
    }

    public String getMediaReqPackageName() {
        return mediaReqPackageName;
    }

    public void setMediaReqPackageName(String mediaReqPackageName) {
        this.mediaReqPackageName = mediaReqPackageName;
    }

    public String getMediaReqAppName() {
        return mediaReqAppName;
    }

    public void setMediaReqAppName(String mediaReqAppName) {
        this.mediaReqAppName = mediaReqAppName;
    }
}
