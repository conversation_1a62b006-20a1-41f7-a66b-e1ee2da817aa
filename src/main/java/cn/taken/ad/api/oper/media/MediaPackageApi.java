package cn.taken.ad.api.oper.media;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.constant.web.WebAuth;
import cn.taken.ad.constant.web.XssIgnore;
import cn.taken.ad.core.dto.global.InfoReq;
import cn.taken.ad.core.dto.web.oper.media.mpackage.*;
import cn.taken.ad.core.pojo.media.MediaPackage;
import cn.taken.ad.core.service.media.MediaPackageService;
import cn.taken.ad.core.service.system.OperLogService;
import cn.taken.ad.utils.web.OperWebUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 媒体广告位接口
 */
@RestController
@RequestMapping(value = "/o/media/package")
public class MediaPackageApi {

    @Resource
    private MediaPackageService mediaPackageService;
    @Resource
    private OperLogService operLogService;

    private static final String MODEL = "媒体分包";

    @WebAuth("MEDIA_PACKAGE_VIEW")
    @RequestMapping(value = "page", method = RequestMethod.POST)
    public SuperResult<Page<MediaPackageInfo>> page(@RequestBody MediaPackagePageReq req) {
        Page<MediaPackageInfo> page = mediaPackageService.findPage(req);

        operLogService.saveOperLog(MODEL, "查询", OperWebUtils.getWebToken().getUserId());
        return SuperResult.rightResult(page);
    }

    @WebAuth()
    @RequestMapping(value = "list")
    public SuperResult<List<MediaPackageInfo>> findList(@RequestBody MediaPackageListReq req) {
        return SuperResult.rightResult(mediaPackageService.findList(req));
    }

    @XssIgnore
    @WebAuth("MEDIA_PACKAGE_ADD")
    @RequestMapping(value = "add", method = RequestMethod.POST)
    public SuperResult<String> add(@RequestBody @Valid MediaPackageAddReq req) {
        SuperResult<String> result = mediaPackageService.add(req, OperWebUtils.getWebToken().getUserId());
        if (result.getSuccess()) {
            operLogService.saveOperLog(MODEL, "新增:" + req.getName(), OperWebUtils.getWebToken().getUserId());
        }
        return result;

    }

    @XssIgnore
    @WebAuth("MEDIA_PACKAGE_MODIFY")
    @RequestMapping(value = "modify", method = RequestMethod.POST)
    public SuperResult<String> modify(@RequestBody @Valid MediaPackageModifyReq req) {
        MediaPackage mediaPackage = mediaPackageService.findById(req.getId());
        if (mediaPackage == null) {
            return SuperResult.badResult("未找到分包");
        }
        mediaPackage.setName(req.getName());
        mediaPackage.setPackageName(req.getPackageName());
        mediaPackage.setAppName(req.getAppName());
        SuperResult<String> result = mediaPackageService.modify(mediaPackage);
        if (result.getSuccess()) {
            operLogService.saveOperLog(MODEL, "修改：" + req.getId(), OperWebUtils.getWebToken().getUserId());
        }
        return result;
    }

    @WebAuth("MEDIA_PACKAGE_VIEW")
    @RequestMapping(value = "info")
    public SuperResult<MediaPackageInfo> load(@RequestBody @Valid InfoReq req) {
        MediaPackageInfo mediaTag = mediaPackageService.findInfoById(req.getId());
        operLogService.saveOperLog(MODEL, "查看：" + mediaTag.getName(), OperWebUtils.getWebToken().getUserId());
        return SuperResult.rightResult(mediaTag);
    }


}
