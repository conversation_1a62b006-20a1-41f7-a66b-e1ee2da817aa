package cn.taken.ad.logic.media.ubix;

import cn.taken.ad.component.utils.encryption.Aes;
import cn.taken.ad.component.utils.encryption.HexByte;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.core.pojo.media.Media;
import cn.taken.ad.core.pojo.media.MediaApp;
import cn.taken.ad.core.pojo.media.MediaTag;
import cn.taken.ad.logic.AbstractMediaProcessor;
import cn.taken.ad.logic.base.bill.RtbBillDto;
import cn.taken.ad.logic.base.event.RtbEventDto;
import cn.taken.ad.logic.base.rtb.RtbMediaDto;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseMiniProgramDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.media.ubix.dto.*;
import cn.taken.ad.utils.time.TimeUtils;
import cn.taken.ad.utils.web.HttpRequestUtils;
import cn.taken.ad.utils.web.HttpResponseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * https://alidocs.dingtalk.com/i/p/5YRBGvapJeygmDArkoz3wOML7YVq5z6L
 */
@Component("UBIX" + LogicSuffix.MEDIA_LOGIC_SUFFIX)
public class UBiXMediaProcessor extends AbstractMediaProcessor {
    private static final Logger log = LoggerFactory.getLogger(UBiXMediaProcessor.class);
    @Resource
    private BaseRedisL2Cache baseRedisL2Cache;

    @Override
    public RtbRequestDto parseRtb(RtbMediaDto mediaDto, String rtbId) throws Throwable {
        byte[] bytes = HttpRequestUtils.readBytes();
        if (bytes == null) {
            return null;
        }
        BidRequest bidRequest = BidRequest.parseFrom(bytes);
        mediaDto.setReqObj(bidRequest);
//        log.info("request:{}", JsonFormat.printer().omittingInsignificantWhitespace().print(bidRequest));
        RtbRequestDto request = new RtbRequestDto();
        request.setReqId(bidRequest.getRequestId());
        RequestAppDto appDto = createReqApp(bidRequest);
        request.setApp(appDto);
        request.setGeo(createReqGeo(bidRequest));
        request.setTag(createReqTag(bidRequest, mediaDto, appDto));
        RequestNetworkDto networkDto = new RequestNetworkDto();
        request.setNetwork(networkDto);
        RequestDeviceDto deviceDto = createReqDevice(bidRequest, networkDto);
        request.setDevice(deviceDto);
        request.setUser(createReqUser(bidRequest, deviceDto));
        return request;
    }

    private RequestUserDto createReqUser(BidRequest bidRequest, RequestDeviceDto deviceDto) {
        if (bidRequest.hasUser()) {
            RequestUserDto userDto = new RequestUserDto();
            User user = bidRequest.getUser();
            userDto.setUserId(user.getUid());
            switch (user.getGender()) {
                case 1:
                    userDto.setGender("M");
                    break;
                case 2:
                    userDto.setGender("F");
                    break;
            }
            userDto.setAge(user.getAge());
            if (!CollectionUtils.isEmpty(user.getTagsList())) {
                userDto.setInterest(user.getTagsList().toArray(new String[0]));
            }
            if (!CollectionUtils.isEmpty(user.getInstalledAppsList())) {
                List<RequestInstalledAppDto> appDtos = new ArrayList<>();
                user.getInstalledAppsList().forEach(v -> {
                    RequestInstalledAppDto appDto = new RequestInstalledAppDto();
                    appDto.setPackageName(v);
                    appDtos.add(appDto);
                });
                deviceDto.setInstalledAppInfo(appDtos);
            }
            return userDto;
        }
        return null;
    }

    private RequestDeviceDto createReqDevice(BidRequest bidRequest, RequestNetworkDto networkDto) {
        RequestDeviceDto deviceDto = new RequestDeviceDto();
        Device device = bidRequest.getDevice();
        deviceDto.setUserAgent(bidRequest.getUa());
        networkDto.setIp(bidRequest.getIpv4());
        networkDto.setIpv6(bidRequest.getIpv6());
        Device.DeviceId did = device.getDid();
        deviceDto.setIdfa(did.getIdfa());
        deviceDto.setIdfv(did.getIdfv());
        deviceDto.setImei(did.getImei());
        deviceDto.setImeiMd5(did.getImeiMd5());
        deviceDto.setAndroidId(did.getAndroidId());
        deviceDto.setOaid(did.getOaid());
        networkDto.setMac(did.getMac());
        networkDto.setWifiMac(did.getWifiMac());
        networkDto.setSsid(did.getSsid());
        deviceDto.setImsi(did.getImsi());
        deviceDto.setIdfaMd5(did.getIdfamd5());
        deviceDto.setAndroidIdMd5(did.getAndroidIdMd5());
        networkDto.setMacMd5(did.getMacMd5());
        deviceDto.setOaidMd5(did.getOaidMd5());
        deviceDto.setPaid(did.getPaid());
        if (!CollectionUtils.isEmpty(did.getCaidsList())) {
            List<RequestCaidDto> caidDtoList = new ArrayList<>();
            did.getCaidsList().forEach(v -> {
                RequestCaidDto caid = new RequestCaidDto();
                caid.setCaid(v.getCaid());
                caid.setVersion(v.getVersion());
                caidDtoList.add(caid);
            });
            deviceDto.setCaids(caidDtoList);
        }
        switch (device.getDeviceType()) {
            case 1:
                deviceDto.setDeviceType(DeviceType.PHONE);
                break;
            case 3:
                deviceDto.setDeviceType(DeviceType.TV);
                break;
            default:
                deviceDto.setDeviceType(DeviceType.UNKNOWN);
                break;
        }
        switch (device.getOsType()) {
            case 1:
                deviceDto.setOsType(OsType.ANDROID);
                break;
            case 2:
                deviceDto.setOsType(OsType.IOS);
                break;
            case 3:
                deviceDto.setOsType(OsType.WINDOWS_PHONE);
                break;
            default:
                deviceDto.setOsType(OsType.UNKNOWN);
        }
        deviceDto.setOsVersion(device.getOsVersion());
        deviceDto.setVendor(device.getVendor());
        deviceDto.setBrand(device.getVendor());
        deviceDto.setModel(device.getModel());
        if (deviceDto.getOsType().equals(OsType.IOS)) {
            deviceDto.setModel(device.getHwMachine());
        }
        deviceDto.setHardwareModel(device.getHwModel());
        deviceDto.setHardwareMachine(device.getHwMachine());
        deviceDto.setLanguage(device.getLanguage());
        switch (device.getConnType()) {
            case 1:
                networkDto.setConnectType(ConnectionType.WIFI);
                break;
            case 2:
                networkDto.setConnectType(ConnectionType.NETWORK_2G);
                break;
            case 3:
                networkDto.setConnectType(ConnectionType.NETWORK_3G);
                break;
            case 4:
                networkDto.setConnectType(ConnectionType.NETWORK_4G);
                break;
            case 5:
                networkDto.setConnectType(ConnectionType.NETWORK_5G);
                break;
            default:
                networkDto.setConnectType(ConnectionType.UNKNOWN);
                break;
        }
        if (device.hasScreenSize()) {
            Size size = device.getScreenSize();
            if (size.getWidth() > 0) {
                deviceDto.setWidth(size.getWidth());
            }
            if (size.getHeight() > 0) {
                deviceDto.setHeight(size.getHeight());
            }
        }
        if (device.getDpi() > 0) {
            deviceDto.setScreenDensity((double) device.getDpi());
        }
        deviceDto.setDeviceName(device.getDeviceName());
        switch (device.getOrientation()) {
            case 1:
                deviceDto.setOrientation(OrientationType.VERTICAL);
                break;
            case 2:
                deviceDto.setOrientation(OrientationType.HORIZONTAL);
                break;
            default:
                deviceDto.setOrientation(OrientationType.UNKNOWN);
                break;
        }
        switch (device.getCarrierType()) {
            case "1":
                networkDto.setCarrierType(CarrierType.CM);
                break;
            case "2":
                networkDto.setCarrierType(CarrierType.CU);
                break;
            case "3":
                networkDto.setCarrierType(CarrierType.CT);
                break;
            default:
                networkDto.setCarrierType(CarrierType.UNKNOWN);
                break;
        }
        if (StringUtils.isNotBlank(device.getStartupTime())) {
            String time = device.getStartupTime();
            if (deviceDto.getOsType() == OsType.ANDROID) {
                try {
                    long t = UUID.fromString(time).timestamp();
                    deviceDto.setSysStartTime(TimeUtils.formatUnixTimeWithPrecision(t, true));
                } catch (Exception e) {

                }
            } else if (deviceDto.getOsType() == OsType.IOS) {
                deviceDto.setSysStartTime(time + "000");
            } else {
                deviceDto.setSysStartTime(time);
            }
        }
        deviceDto.setSysUpdateTime(device.getMbTime());
        if (device.getCpuNum() > 0) {
            deviceDto.setCpuNum(device.getCpuNum());
        }
        if (device.getDiskCapacity() > 0) {
            deviceDto.setDeviceHardDisk(device.getDiskCapacity());
        }
        if (device.getMemCapacity() > 0) {
            deviceDto.setDeviceMemory(device.getMemCapacity());
        }
        if (device.getBatteryPower() > 0) {
            deviceDto.setBatteryPower(device.getBatteryPower());
        }
        deviceDto.setHmsVersion(device.getHuaweiVerCodeOfHms());
        deviceDto.setHmsAgVersion(device.getHuaweiVerCodeOfAg());
        deviceDto.setSysInitTime(device.getBirthTime());
        deviceDto.setBootMark(device.getBootMark());
        deviceDto.setUpdateMark(device.getUpdateMark());
        return deviceDto;
    }

    private RequestGeoDto createReqGeo(BidRequest bidRequest) {
        App app = bidRequest.getApp();
        if (app.hasGeo()) {
            RequestGeoDto geoDto = new RequestGeoDto();
            App.Geo geo = app.getGeo();
            if (geo.getLatitude() > 0) {
                geoDto.setLatitude(geo.getLatitude());
            }
            if (geo.getLongitude() > 0) {
                geoDto.setLongitude(geo.getLongitude());
            }
            geoDto.setCoordinateType(CoordinateType.OTHER);
            return geoDto;
        }
        return null;
    }

    private RequestAppDto createReqApp(BidRequest bidRequest) {
        RequestAppDto appDto = new RequestAppDto();
        App app = bidRequest.getApp();
        appDto.setAppId(app.getAppId());
        appDto.setAppName(app.getName());
        appDto.setBundle(app.getPackageName());
        appDto.setAppVersion(app.getVersion());
        return appDto;
    }


    private RequestTagDto createReqTag(BidRequest req, RtbMediaDto mediaDto, RequestAppDto appDto) {
        RequestTagDto tagDto = new RequestTagDto();
        tagDto.setSize(1);
        AdSlot adSlot = req.getAdSlotsList().get(0);
        if (StringUtils.isEmpty(adSlot.getId())) {
            return null;
        }
        MediaTag mediaTag = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_NOAPPCODE_TAG_CODE_ + mediaDto.getMediaId() + "_" + adSlot.getId(), MediaTag.class);
        if (mediaTag == null) {
            return null;
        }
        MediaApp mediaApp = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_APP_ID_ + mediaTag.getMediaAppId(), MediaApp.class);
        if (mediaApp == null) {
            return null;
        }
        appDto.setAppId(mediaApp.getCode());
        tagDto.setTagId(adSlot.getId());
        switch (adSlot.getAdType()) {
            case 1:
                tagDto.setTagType(TagType.OPEN);
                break;
            case 2:
                tagDto.setTagType(TagType.INFORMATION_FLOW);
                break;
            case 4:
                tagDto.setTagType(TagType.BANNER);
                break;
            case 6:
                tagDto.setTagType(TagType.INTERSTITIAL);
                break;
            case 9:
                tagDto.setTagType(TagType.INCENTIVE_VIDEO);
                break;
            default:
                tagDto.setTagType(TagType.OTHER);
                break;

        }
        tagDto.setNeedHttps(adSlot.getSecure());
        if (!CollectionUtils.isEmpty(adSlot.getAcceptedCreativeSpecsList())) {
            AdSlot.CreativeSpecs as = adSlot.getAcceptedCreativeSpecsList().get(0);
            Size size = as.getAcceptedSizesList().get(0);
            if (size.getWidth() > 0) {
                tagDto.setWidth(size.getWidth());
            }
            if (size.getHeight() > 0) {
                tagDto.setHeight(size.getHeight());
            }
            if (as.getVideoMinDuration() > 0) {
                tagDto.setMinDuration(as.getVideoMinDuration());
            }
            if (as.getVideoMaxDuration() > 0) {
                tagDto.setMaxDuration(as.getVideoMaxDuration());
            }
        }
        if (adSlot.hasSceneContext()) {
            SceneContext sc = adSlot.getSceneContext();
            if (sc.hasSearchContext()) {
                tagDto.setQuery(sc.getSearchContext().getSearchKeyword());
            }
        }
        if (adSlot.hasTradingMode()) {
            TradingMode tm = adSlot.getTradingMode();
            if (tm.getFloorCpm() > 0) {
                tagDto.setPrice((double) tm.getFloorCpm());
            }
        }
        return tagDto;
    }

    @Override
    public SuperResult<String> returnRtb(RtbMediaDto mediaDto) throws Throwable {
        BidResponse.Builder respBuilder = BidResponse.newBuilder();
        BidRequest bidRequest = (BidRequest) mediaDto.getReqObj();
        respBuilder.setRequestId(bidRequest.getRequestId());
        respBuilder.setStatusCode(202000);
        if (mediaDto.getResponse() != null && !CollectionUtils.isEmpty(mediaDto.getResponse().getTags())) {
            respBuilder.setStatusCode(200);
            mediaDto.getResponse().getTags().forEach(tag -> {
                Ad.Builder ad = Ad.newBuilder();
                ad.setAdId(mediaDto.getRequest().getTag().getTagId());
                if (tag.getRespMediaPrice() != null) {
                    ad.setBidPrice(tag.getRespMediaPrice().longValue());
                    MediaTag mt = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_TAG_CODE_ + mediaDto.getMediaId() + "_" + mediaDto.getMediaAppId() + "_" + ad.getAdId(), MediaTag.class);
                    if (mt.getBidType() == BidType.BID.getType()) {
                        ad.setBidType(1);
                        ad.setSettleType(1);
                    }
                }
                Ad.MaterialMeta.Builder amb = Ad.MaterialMeta.newBuilder();
                if (StringUtils.isNotBlank(tag.getCreativeId())) {
                    amb.setCreativeId(tag.getCreativeId());
                }
                if (tag.getMaterialType() != null) {
                    switch (tag.getMaterialType()) {
                        case VIDEO:
                            amb.setCreativeType(3);
                            break;
                        default:
                            if (!CollectionUtils.isEmpty(tag.getImgUrls()) && tag.getImgUrls().size() == 1) {
                                amb.setCreativeType(1);
                            } else {
                                amb.setCreativeType(2);
                            }
                            break;
                    }
                }
                if (tag.getActionType() != null) {
                    switch (tag.getActionType()) {
                        case DOWNLOAD:
                            amb.setInteractionType(4);
                            break;
                        case MINI_PROGRAM:
                            amb.setInteractionType(5);
                            break;
                        default:
                            amb.setInteractionType(3);
                            break;
                    }
                }
                if (StringUtils.isNotBlank(tag.getTitle())) {
                    amb.setTitle(tag.getTitle());
                }
                if (StringUtils.isNotBlank(tag.getDesc())) {
                    amb.setDescription(tag.getDesc());
                }

                ResponseAppDto appDto = tag.getAppInfo();
                if (appDto != null) {
                    if (StringUtils.isNotBlank(appDto.getPackageName())) {
                        amb.setPackageName(appDto.getPackageName());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppName())) {
                        amb.setAppName(appDto.getAppName());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppVersion())) {
                        amb.setDownAppVersion(appDto.getAppVersion());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppDeveloper())) {
                        amb.setAppPublisher(appDto.getAppDeveloper());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppPrivacyUrl())) {
                        amb.setPrivacyLink(appDto.getAppPrivacyUrl());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppPermissionInfoUrl())) {
                        amb.setPermissionLink(appDto.getAppPermissionInfoUrl());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppInfoUrl())) {
                        amb.setIntroduceLink(appDto.getAppInfoUrl());
                    }
                    if (StringUtils.isNotBlank(appDto.getRecordNumber())) {
                        amb.setAppLcpNumber(appDto.getRecordNumber());
                    }
                }
                if (StringUtils.isNotBlank(tag.getIconUrl())) {
                    amb.setIcon(tag.getIconUrl());
                }
                if (!CollectionUtils.isEmpty(tag.getImgUrls())) {
                    tag.getImgUrls().forEach(v -> {
                        Ad.MaterialMeta.Image.Builder imb = Ad.MaterialMeta.Image.newBuilder();
                        imb.setUrl(v);
                        if (tag.getMaterialWidth() != null) {
                            imb.setWidth(tag.getMaterialWidth());
                        }
                        if (tag.getMaterialHeight() != null) {
                            imb.setHeight(tag.getMaterialHeight());
                        }
                        amb.addImage(imb);
                    });
                }
                if (tag.getVideoInfo() != null) {
                    ResponseVideoDto videoDto = tag.getVideoInfo();
                    Ad.MaterialMeta.Video.Builder video = Ad.MaterialMeta.Video.newBuilder();
                    if (StringUtils.isNotEmpty(videoDto.getVideoUrl())) {
                        video.setUrl(videoDto.getVideoUrl());
                    }
                    if (!CollectionUtils.isEmpty(videoDto.getCoverImgUrls())) {
                        video.setCoverImage(videoDto.getCoverImgUrls().get(0));
                    }
                    if (videoDto.getDuration() != null) {
                        video.setDuration(videoDto.getDuration());
                    }
                    if (videoDto.getVideoSize() != null) {
                        video.setSize(videoDto.getVideoSize().floatValue());
                    }
                    if (videoDto.getVideoWidth() != null) {
                        video.setWidth(videoDto.getVideoWidth());
                    }
                    if (videoDto.getVideoHeight() != null) {
                        video.setHeight(videoDto.getVideoHeight());
                    }
                    amb.setVideo(video);
                }
                if (StringUtils.isNotBlank(tag.getClickUrl())) {
                    amb.setTargetUrl(tag.getClickUrl());
                    if (ActionType.DOWNLOAD == tag.getActionType()) {
                        amb.setDownloadUrl(tag.getClickUrl());
                    }
                }
                if (StringUtils.isNotBlank(tag.getDeepLinkUrl())) {
                    amb.setDeeplinkUrl(tag.getDeepLinkUrl());
                }
                if (!CollectionUtils.isEmpty(tag.getWinNoticeUrls())) {
                    List<String> urls = tag.getWinNoticeUrls();
                    urls = replaceMacro(MacroType.WIN_PRICE.getCode(), urls, "__AUCTION_PRICE__");
                    amb.addAllWinNoticeUrl(urls);
                }
                if (tag.getMiniProgram() != null) {
                    ResponseMiniProgramDto miniProgramDto = tag.getMiniProgram();
                    Ad.MaterialMeta.WxMpInfo.Builder wx = Ad.MaterialMeta.WxMpInfo.newBuilder();
                    if (StringUtils.isNotBlank(miniProgramDto.getId())) {
                        wx.setAppId(miniProgramDto.getId());
                    }
                    if (StringUtils.isNotBlank(miniProgramDto.getName())) {
                        wx.setUsername(miniProgramDto.getName());
                    }
                    if (StringUtils.isNotBlank(miniProgramDto.getPath())) {
                        wx.setPath(miniProgramDto.getPath());
                    }
                    amb.setMp(wx);
                }
                if (StringUtils.isNotBlank(tag.getUniversalLink())) {
                    amb.setUniversalLink(tag.getUniversalLink());
                }
                //事件处理
                if (!CollectionUtils.isEmpty(tag.getTracks())) {
                    tag.getTracks().forEach(track -> {
                        List<String> urls = track.getTrackUrls();
                        urls = replaceMacro(MacroType.TIME.getCode(), urls, "__TS__");
                        urls = replaceMacro(MacroType.ABS_DOWN_X.getCode(), urls, "__DOWN_X__");
                        urls = replaceMacro(MacroType.ABS_DOWN_Y.getCode(), urls, "__DOWN_Y__");
                        urls = replaceMacro(MacroType.ABS_UP_X.getCode(), urls, "__UP_X__");
                        urls = replaceMacro(MacroType.ABS_UP_Y.getCode(), urls, "__UP_Y__");
                        int type = track.getTrackType();
                        if (type == EventType.EXPOSURE.getType()) {
                            urls = replaceMacro(MacroType.WIN_PRICE.getCode(), urls, "__AUCTION_PRICE__");
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(101)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.CLICK.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(201)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.DOWNLOAD_BEGIN.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(301)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.DOWNLOAD_COMPLETED.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(302)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.INSTALL_BEGIN.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(303)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.INSTALL_COMPLETED.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(304)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.DEEPLINK_START.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(401)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.DEEPLINK_OPEN_SUCCESS.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(403)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.DEEPLINK_OPEN_FAIL.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(404)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.VIDEO_BEGIN.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(5000)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.VIDEO_25.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(5025)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.VIDEO_50.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(5050)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.VIDEO_75.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(5075)
                                    .addAllUrls(urls)
                                    .build());
                        } else if (type == EventType.VIDEO_END.getType()) {
                            amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                                    .setEvent(5100)
                                    .addAllUrls(urls)
                                    .build());
                        }
                    });
                }
                if (!CollectionUtils.isEmpty(tag.getFailNoticeUrls())) {
                    amb.addTrackingEvent(Ad.MaterialMeta.Tracking.newBuilder()
                            .setEvent(702)
                            .addAllUrls(tag.getFailNoticeUrls())
                            .build());
                }
                ad.setCreative(amb);
                respBuilder.addAds(ad);
            });
        }
        BidResponse response = respBuilder.build();
        mediaDto.setRespObj(response);
//        log.info("response:{}", JsonFormat.printer().omittingInsignificantWhitespace().print(response));
        HttpResponseUtils.outputBytes(response.toByteArray());
        return SuperResult.rightResult();
    }

    private List<String> replaceMacro(String macro, List<String> urls, String msg) {
        return urls.stream().map(url -> url.replace(macro, msg)).collect(Collectors.toList());
    }

    @Override
    public SuperResult<String> mediaParseBill(RtbBillDto bill, Media media) {
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<String> mediaParseEvent(RtbEventDto event, Media media) {
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<Double> decryptPrice(String price, Media media) {
        if (StringUtils.isBlank(price)) {
            // 未替换 价格宏
            return SuperResult.badResult("price empty");
        }
        if (price.equals("__AUCTION_PRICE__")) {
            return SuperResult.badResult("price not replaced");
        }
        try {
            String priceSecretKey = media.getPriceKey();
            if (priceSecretKey.length() > 16) {
                priceSecretKey = priceSecretKey.substring(0, 16);
            }
            byte[] data = Aes.decrypt(HexByte.hex2Byte(price), priceSecretKey.getBytes(StandardCharsets.UTF_8), priceSecretKey.getBytes(StandardCharsets.UTF_8), "AES/CBC/PKCS5Padding");
            return SuperResult.rightResult(Double.valueOf(new String(data)));
        } catch (Exception e) {
            return SuperResult.badResult("decrypt[" + price + "] error:" + e.getMessage());
        }
    }
}
