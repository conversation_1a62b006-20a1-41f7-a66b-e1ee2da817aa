package cn.taken.ad.logic.adv.jym.dto.response;

import java.io.Serializable;
import java.util.List;

public class JymResponseVideoDto implements Serializable {
    private static final long serialVersionUID = 2451170987240254016L;
    /**
     * 视频时长（秒）
     */
    private Integer video_duration;
    /**
     * 视频地址
     */
    private String video_url;
    /**
     * 视频播放开始监控地址
     */
    private List<String> play_start_urls;
    /**
     * 视频播放完成监控地址
     */
    private List<String> play_end_urls;
    /**
     * 视频被关闭监控地址
     */
    private List<String> play_close_urls;
    /**
     * 视频被跳过监控地址
     */
    private List<String> play_skip_urls;
    /**
     * 视频播放过程监控
     */
    private List<JymPlayTracerDto> play_tracers;
    /**
     * 是否存在视频后贴
     */
    private Boolean has_end;
    /**
     * 视频后贴
     */
    private JymVideoEndDto video_end;
    /**
     * 是否存在视频中贴
     */
    private Boolean has_mid;
    /**
     * 视频中贴
     */
    private JymVideoMidDto video_mid;

    public Integer getVideo_duration() {
        return video_duration;
    }

    public void setVideo_duration(Integer video_duration) {
        this.video_duration = video_duration;
    }

    public String getVideo_url() {
        return video_url;
    }

    public void setVideo_url(String video_url) {
        this.video_url = video_url;
    }

    public List<String> getPlay_start_urls() {
        return play_start_urls;
    }

    public void setPlay_start_urls(List<String> play_start_urls) {
        this.play_start_urls = play_start_urls;
    }

    public List<String> getPlay_end_urls() {
        return play_end_urls;
    }

    public void setPlay_end_urls(List<String> play_end_urls) {
        this.play_end_urls = play_end_urls;
    }

    public List<String> getPlay_close_urls() {
        return play_close_urls;
    }

    public void setPlay_close_urls(List<String> play_close_urls) {
        this.play_close_urls = play_close_urls;
    }

    public List<String> getPlay_skip_urls() {
        return play_skip_urls;
    }

    public void setPlay_skip_urls(List<String> play_skip_urls) {
        this.play_skip_urls = play_skip_urls;
    }

    public List<JymPlayTracerDto> getPlay_tracers() {
        return play_tracers;
    }

    public void setPlay_tracers(List<JymPlayTracerDto> play_tracers) {
        this.play_tracers = play_tracers;
    }

    public Boolean getHas_end() {
        return has_end;
    }

    public void setHas_end(Boolean has_end) {
        this.has_end = has_end;
    }

    public JymVideoEndDto getVideo_end() {
        return video_end;
    }

    public void setVideo_end(JymVideoEndDto video_end) {
        this.video_end = video_end;
    }

    public Boolean getHas_mid() {
        return has_mid;
    }

    public void setHas_mid(Boolean has_mid) {
        this.has_mid = has_mid;
    }

    public JymVideoMidDto getVideo_mid() {
        return video_mid;
    }

    public void setVideo_mid(JymVideoMidDto video_mid) {
        this.video_mid = video_mid;
    }
}
