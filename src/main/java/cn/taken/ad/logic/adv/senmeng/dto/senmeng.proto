syntax = "proto3";

//package com.sm.bidder.api.openrtb;
//option java_outer_classname = "Sm_Proto";

// option csharp_namespace = "RtbLab";

//protoc --csharp_out=../Rtb/ senmeng.proto

// 指定Java文件包路径
option java_package="cn.taken.ad.logic.adv.senmeng.dto";
// 是否拆分为多个Java类
option java_multiple_files = true;
// java文件名称
option java_outer_classname = "SenMengAdvRequest";

message Request {
  // 版本号
  int32      version        = 1;

  // 此请求的唯一id
  string     id             = 2;

  // 可展示的位置
  message Imp  {

    // 此impression在当前Request中的唯一id,从1开始
    int32  id        = 1;

    // 广告位id 由森萌提供
    string pid       = 2;

    // 广告位的宽
    int32  width     = 3;

    // 广告位的高
    int32  height    = 4;

    // RTB的底价，非RTB方式可不填, 单位：分
    int32  bid_floor = 5;

  }
  repeated Imp imp            = 3;

  // 设备信息
  message Device {
    // ipv4 点分十进制, 必须为终端真实IP地址
    string ip             = 1;

    // ipv6 点分十进制, 必须为终端真实IP地址
    string ipv6           = 2;

    // user agent，来自http头
    string user_agent     = 3;

    // IOS6.0及以上的idfa号
    string idfa           = 4;

    // 安卓设备的imei号
    string imei           = 5;

    // 安卓设备的imei号的md5值,若填写imei原值，则不用填此字段
    string imei_md5       = 6;

    // 设备的mac地址
    string mac            = 7;

    // 设备的mac地址的md5值, 若填写mac原值，则不用填此字段
    string mac_md5        = 8;

    // 设备类型，0-手机;1-平板;2-PC;3-互联网电视
    int32  device_type    = 9;

    // 设备品牌 例如 nokia, samsung
    string brand          = 10;

    // 设备型号 例如：n70, galaxy
    string model          = 11;

    // 操作系统 例如 Android,iOS
    string os             = 12;

    // 操作系统版本 例如 7.0.2
    string osv            = 13;

    // 设备所处网络环境 0-未识别, 1-wifi, 2-2g, 3-3g, 4-4g, 5-5g, 6-6g
    int32  network        = 14 ;

    // 设备的网络运营商 0-未知, 1-移动, 2-联通, 3-电信
    int32  operator       = 15;

    // 设备屏幕尺寸：宽
    int32  width          = 16;

    // 设备屏幕尺寸：高
    int32  height         = 17;

    // 设备密度，对应于pixel_ratio
    int32  pixel_ratio    = 18 ;

    message Geo {
      // 纬度, 取值范围[-90.0 , +90.0]
      double lat = 1;
      // 经度, 取值范围[-180.0 , +180.0]
      double lon = 2;
    }
    Geo    geo            = 21;

    // 用户已安装 app 列表
    repeated string installed_app  = 22;

    // oaid
    string oaid           = 23;

    // 广协CAID
    string caid           = 25;

    // 系统启动标识
    string boot_mark      = 26;

    // 系统更新标识
    string update_mark    = 27;

    // 原始IDFA的md5值，md5后的大写形式
    string idfa_md5       = 28;

    // 原始OAID(不做大小写转换)的md5值，md5后的大写形式
    string oaid_md5       = 29;

    // android_id
    string android_id     = 30;

    // android_id_md5
    string android_id_md5 = 31;
    
    //paid
    string paid           = 32;

    string caidver        = 33;

    string caid2          = 34;

    string caid2ver       = 35;

    // 设备初始化时间 1693452738.754997166 单位是秒
    string dit = 36;

    // 系统更新时间 单位：秒（保留6位小数）例如："1595214620.383940"
    string sut = 37;

    // 系统启动时间 (保留9位小数，纳秒，不足9位可补0)，例如:"1595214620.383940897
    string sst = 38;

    //平台类型，1:iphone,2:ipad,3:android_phone,4:android_pad
    int32 platform = 39;

    // 物理内存 单位 KB
    int64 physicalMemoryKBytes = 40;

    //硬盘大小 单位 KB
    int64 hardDiskSizeKBytes = 41;

    //国家，中国: CHN
    string country = 42;

    //时区 北京时间 GMT+0800
    string timeZone = 43;

    //设备名称
    string deviceName = 44;

    //设备名称的 MD5
    string deviceNameMd5 = 45;


  }
  Device     device         = 5;

  // APP属性
  message App {
    // 应用包名
    string package_name = 1;

    // 应用名
    string app_name     = 2;

    // app版本号
    string version      = 3;

  }
  App app            = 6;

  // 是否必须返回https广告
  bool       https_required = 10 ;

}

message Response {
  //对应Request中的id
  string id     = 1;

  // 0-ok，1 无广告返回 2其他异常情况
  int32  status = 2 ;

  // 一个位置上的广告
  message Seat {
    // 指定请求里的impression id ,从1开始
    int32 id = 1;

    // 广告字段
    message Ad {
      // 广告序号，从1开始
      int32      id                      = 1;

      // 创意类型 1 文字 2 图片 3 Flash 4 视频
      int32      creative_type           = 3;

      // 展现反馈地址
      repeated string     impression_tracking_url = 6;

      // 点击跳转地址(落地页)
      string     click_through_url       = 7;

      // 点击上报 url，客户端需要进行宏替换: 
      // 将“__WIDTH__”替换为实际广告位的宽，单位为像 素; 
      // 将“__HEIGHT__”替换为实际广告位的高，单位为像 素; 
      // 将“__DOWN_X__”替换为用户手指按下时的横坐标，用 int 值。
      // 将“__DOWN_Y__”替换为用户手指按下时的纵坐标，用 int 值。
      // 将“__UP_X__”替换为用户手指离开屏幕时的横坐标; 
      // 将“__UP_Y__”替换为用户手指离开屏幕时的纵坐标; 
      // 将“__P_DURATION__"替换为用户点击广告时广告 播放的时长(单位:ms)
      // 将"__P_RATE__"替换为用户点击广告时播放的百分 比(0-100%)。
      repeated string     click_tracking_url      = 8;

      message NativeAd {
        // 属性（描述）信息
        message Attr{
          // 属性名
          string name  = 1;

          // 属性值
          string value = 2;
        }
        repeated Attr attr = 1;
      }

      NativeAd   native_ad               = 10;

      //广告创意的唯一标识
      string     creative_id             = 11;

      //广告来源
      string     ad_source               = 12;

      //APP唤醒地址
      string     deeplink_url            = 13;

      //APP下载地址
      string     download_url            = 14;

      //返回报价供上游adx竞价, 单位(分)
      int32      bid_price               = 15;
      
      // 事件监测url
      message EventTrack {
        // 0 deeplink 尝试调起
        // 1 deeplink 调起成功
        // 2 deeplink 调起失败
        // 3 deeplink 检测 app 已安装
        // 4 deeplink 检测 app 未安装
        // 21 下载开始
        // 22 下载完成
        // 23 下载暂停
        // 24 下载继续
        // 25 下载删除
        // 26 安装开始
        // 27 安装完成
        // 28 安装激活完成
        // 31 视频开始播放
        // 32 视频开始播放至 25%
        // 33 视频开始播放至 50%
        // 34 视频开始播放至 75%
        // 35 视频播放结束
        // 36 视频跳过
        // 37 视频全屏播放
        // 38 视频退出全屏播放
        // 39 视频加载成功监测
        // 40 视频加载失败监测
        // 41 视频静音监测
        // 42 视频取消静音监测
        // 43 视频暂停播放监测
        // 44 视频继续播放监测
        // 45 视频播放错误监测
        // 46 视频重播监测
        // 47 视频上滑事件监测
        // 48 视频下滑事件监测
        // 49 视频关闭事件监测
        // 50 视频点击事件监测
        uint32 type = 1;
        repeated string url  = 2;
      }

      repeated EventTrack event_track             = 16;

      // 落地页打开方式 1是打开网页(包含deeplink) 2点击下载
      int32      open_type               = 17;
      // 废弃 请使用 winnotice_urls 
      string     winnotice_url           = 18;

      // 应用包名
      string     package_name            = 19;
      // 应用名
      string     app_name                = 20;
      /**
       * 下载类相关信息
       */
      string     app_desc                = 21;//app描述
      string     app_download_url        = 22;//下载地址
      string     permissions_url         = 23;//权限名称及权限描述列表
      string     function_desc_url       = 24;//产品功能url
      string     privacy_url             = 25;//隐私协议
      string     developer_name          = 26;//开发者公司名称
      string     app_version             = 27;//应用版本号
      string     app_icon_url            = 28;//App图标链接
      int64      app_size                = 29;//app大小
      string     file_md5                = 30;//app文件md5

      // 竞价失败上报URL 需要进行宏替换  
      // 1、__ADN_TYPE__ 替换为竞胜方类型 枚举值:1、快手其他广告 2、其他平台 同时将"__ADN_NAME__" 替换为平台名 枚举值 chuanshanjia;guangdiantong;baidu;other 
      // 2、__AD_N__ 竞胜方dsp的广告主名称，从物 料中获取 
      // 3、__AD_TI__ 竞胜方的广告标题，从物料中获取 
      // 4、__AD_REQID__ 媒体侧的请求 id(用于关 联竞胜和点击行为为回传的 llsid 必填) 
      // 5、__IS_S__ 竞胜方 dsp 本次请求是否展示(非快 手广告是否被展示)，值如下:0:未展示 1:展示 2:未知
      // 6、__IS_C__ 竞胜方 dsp 本次 pv 是否被点击(非 快手广告是否被点击)，值如下:0:未点击 1:点 击 2:未知
      // 7、__AUCTION_PRICE__ 竞胜方的 ecpm  类型为int 单位为分
      // 8、__TS__ 时间戳 毫秒
      // 9、__TS_S__ 时间戳 秒
      string     lurl           = 31;

      // 快手广告转化链路上报 url，客户端需要进行宏替换：
      // 将 __ACTION__ 宏替换为具体的行为，具体定义如下：
      // 30 : 开始下载
      // 31 : 下载完成
      // 32 : 安装完成
      // 33 : 下载暂停
      // 34 : 下载继续
      // 35 ：下载删除
      // 399 : 开始播放
      // 21：播放 3s（第一遍）
      // 22：播放 5s（第一遍）
      // 400 : 播放完成
      // 809 : 曝光失败通知
      // 3：广告关闭
      // 4 : 减少此类广告

      // 广告关闭时将"__P_DURATION__"替换为用户点击广告时广告播放的时长(单位：毫秒)
      // 广告关闭时将"__P_RATE__"替换为用户点击广告时播放的百分比

      // 曝光失败通知：
      // 1、"__IFR__" 曝光失败的原因,
      //    枚举值：
      //    0- 其他
      //    1- 媒体侧底价过滤
      //    2- 快手广告竞价失败
      //    3- 快手广告缓存失败
      //    4-快手广告曝光优先级降低

      // 2、"__ADN_TYPE__"替换为竞胜方类型
      //    枚举值：
      //    1-快手其他广告
      //    2-其他平台 需同时将"__ADN_NAME__" 替换为平台名 枚举值：chuanshanjia；guangdiantong；baidu；other
      //    3- 输给自售广告
      // 3、__AD_N__ ：竞胜方 dsp 的广告主名称，从物料中获取
      // 4、__AD_TI__：竞胜方的广告标题，从物料中获取
      // 6、__IS_S__：竞胜方 dsp 本次请求是否展示（非快手广告是否被展示），值如下：0：未展示 1：展示2：未知
      // 7、__IS_C__：竞胜方 dsp 本次 pv 是否被点击（非快手广告是否被点击），值如下：0：未点击 1：点击 2：未知
      // 8、__AD_ECPM__：竞胜方的 ecpm
      // 9、__AD_MT__：物料类型，
      //    枚举值：
      //    1 - 横版图片
      //    2 - 竖版图片
      //    3 - 横版视频
      //    4 - 竖版视频
      //    5 - 三图
      //    6 - 横幅
      //    7 - 其他
      // 10、__AD_URL__：竞胜方广告素材 url，需要encode
      string convUrl  = 32;

      // 汇川 视频播放打点
      string videoPlayUrl=33;
      
      // 竞价成功通知 需要宏替换
      // __TS__ 时间戳 毫秒
      // __TS_S__ 时间戳 秒
      // %%PRICE%% 加密后的价格
      repeated string     winnotice_urls           = 34;
    }
    repeated Ad    ad = 2;
  }
  repeated Seat   seat   = 3;
}