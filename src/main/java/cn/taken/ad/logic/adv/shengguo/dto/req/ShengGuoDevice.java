package cn.taken.ad.logic.adv.shengguo.dto.req;

import java.util.List;

public class ShengGuoDevice {
    private Integer device_type; //Y 设备类型 1：手机; 2：平板; 3：智能电视 ;
    private Integer os; //Y 操作系统 1：Android; 2：Ios; 3：HarmonyOS;
    private String os_ver; //Y 操作系统版本 例：12.0.1
    private String brand; //Y 设备品牌 例：SAMSUNG
    private String vendor; //Y 设备厂商 例：SAMSUNG
    private String model; //Y 设备型号 安卓设备 例·: SM-G9750 IOS设备 例: iPhone11,8
    private String model_code; //N 设备型号码，IOS操作系统必传. 例如:D22AP
    private Integer screen_width; //Y 屏幕宽度，例：1080
    private Integer screen_height; //Y 屏幕高度，例：1920
    private String ua; //Y 设备浏览器UA
    private Float density; //Y 屏幕像素密度系数（dpi/160），例：3.0
    private Integer dpi; //Y 屏幕每英寸像素点数，例：440
    private String imei; //Y Android设备唯一标识码， 例：865579032332410
    private String imei_md5; //N 设备 IMEI 的 MD5 值，仅当安卓设备无法提供设备 IMEI，只能提供 IMEI_MD5 值时需传，其余情况不需要.
    private String oaid; //Y Android设备唯一标识码（系统版本10及以上），例：662A5866-1312-4884-AEB9-03728DBF524E
    private String oaid_md5; //N 设备 oaid 的 MD5值. 无法提供设备 oaid，只能提供 MD5 值时需传，其余情况不需要.
    private String android_id; //Y Android设备ID，例：d2914b5664d733e4
    private String android_id_md5; //N 设备 androidId 的MD5值. 无法提供设备 id，只能提供 MD5 值时需传，其余情况不需要.
    private String idfa; //Y IOS设备唯一标识码，例：11FD4B38-91E0-42B1-ADBE-733AA166CA0C
    private String idfa_md5; //N 设备 idfa 的MD5值. 无法提供设备 id，只能提供 MD5 值时需传，其余情况不需要.
    private String idfv; //N IOS设备的应用开发商标识符
    private String idfv_md5; //N 设备 idfv 的MD5值. 无法提供设备 idfv，只能提供 MD5 值时需传，其余情况不需要.
    private String imsi; //Y 移动客户识别码，例：460002114003208
    private String mac; //Y Mac地址，例：CE:BD:EA:32:65:FB
    private String mac_md5; //N 设备 mac 地址 MD5值. 无法提供 mac 地址，只能提供 MD5 值时需传，其余情况不需要.
    private String open_id; //N IOS设备的openudid值
    private String caid; //N 中国广告协会互联网广告标识ID
    private String caid_md5; //N caid md5值
    private String caid_version; //N CAID 版本，iOS14 以上必填
    private Integer caid_vendor; //N CAID供应商, 0:热云, 1:信通院, 2:阿里因子AAID
    private List<ShengGuoCaidList> caid_list; //N iOS caid 列表数组；
    private Integer orientation; //N 屏幕方向, 1：竖屏; 2：横屏;
    private String boot_mark; //N 安卓/IOS系统启动标识
    private String update_mark; //N 安卓/IOS系统更新标识
    private String os_com_time; //N 手机系统的编译时间，IOS必传，秒级时间戳
    private String os_update_time; //N 手机系统的更新时间，IOS必传 ，秒级时间戳
    private String os_update_time_nano; //N 手机系统的更新时间 精度纳秒，格 式为： 1519571543.478583932, 前部分为秒，后部分为纳秒，纳秒为9位。
    private String boot_time_sec; //N 设备最近一次开机时间，IOS必传，秒级时间戳
    private String boot_time_nano; //N 设备最近一次开机时间，精确到纳秒，格式：1519571543.573239746，纳秒为9位。
    private Long disk_size; //N 硬盘大小 单位kb IOS操作系统建议传
    private Integer battery_status; //N 电池充电状态 IOS操作系统建议传 0:未知; 1:不充电; 2:充电; 3:满电 ;
    private Integer battery_power; //N 电池电量百分比 IOS操作系统建议传 例如: 60
    private Long memory_size; //N 物理内存大小 IOS操作系统建议传 单位kb
    private Integer cpu_num; //N CPU数量 IOS操作系统建议传
    private Float cpu_frequency; //N 手机CPU频率 单位: GHz IOS操作系统建议传，例如: 2.2
    private String time_zone; //N 时区 例 北京 “GMT+0800”
    private Integer lmt; //N 当前设备是否允许获取idfa；IOS必传 0：未确定，开发者尚未发起授权请求； 1：受限制； 2：被拒绝； 3：已授权；
    private String appstore_ver; //N OPPO、VIVO、HUAWEI设备 应用商店版本号
    private String kernel_ver; //N OPPO、VIVO设备 OriginOS、ColorOs版本号，小米设备（红米） Miui版本号
    private String hms_ver; //N 华为设备HMS Core版本号
    private String sys_init_time; //N 设备初始化时间，格式：1649783466.444164583（ios推荐填写）
    private String device_name; //N IOS必填，设备名称, 例如: 李四的iPhone
    private String device_name_md5; //N IOS 设备名称无法获取时, 传 MD5值
    private Double screen_size; //Y 屏幕尺寸 例:4.7 , 5.5 单位:英寸
    private String rom_version; //N 手机ROM版本
    private String api_level; //N Android 操作系统版本，IOS 无需此参数
    private String paid; //N 拼多多的PAID, PAID = MD5(设备初始化时间) + "-" + MD5(系统更新时间) + "-" + MD5(系统启动时间)

    public Integer getDevice_type() {
        return device_type;
    }

    public void setDevice_type(Integer device_type) {
        this.device_type = device_type;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public String getOs_ver() {
        return os_ver;
    }

    public void setOs_ver(String os_ver) {
        this.os_ver = os_ver;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getModel_code() {
        return model_code;
    }

    public void setModel_code(String model_code) {
        this.model_code = model_code;
    }

    public Integer getScreen_width() {
        return screen_width;
    }

    public void setScreen_width(Integer screen_width) {
        this.screen_width = screen_width;
    }

    public Integer getScreen_height() {
        return screen_height;
    }

    public void setScreen_height(Integer screen_height) {
        this.screen_height = screen_height;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public Float getDensity() {
        return density;
    }

    public void setDensity(Float density) {
        this.density = density;
    }

    public Integer getDpi() {
        return dpi;
    }

    public void setDpi(Integer dpi) {
        this.dpi = dpi;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getImei_md5() {
        return imei_md5;
    }

    public void setImei_md5(String imei_md5) {
        this.imei_md5 = imei_md5;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getOaid_md5() {
        return oaid_md5;
    }

    public void setOaid_md5(String oaid_md5) {
        this.oaid_md5 = oaid_md5;
    }

    public String getAndroid_id() {
        return android_id;
    }

    public void setAndroid_id(String android_id) {
        this.android_id = android_id;
    }

    public String getAndroid_id_md5() {
        return android_id_md5;
    }

    public void setAndroid_id_md5(String android_id_md5) {
        this.android_id_md5 = android_id_md5;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getIdfa_md5() {
        return idfa_md5;
    }

    public void setIdfa_md5(String idfa_md5) {
        this.idfa_md5 = idfa_md5;
    }

    public String getIdfv() {
        return idfv;
    }

    public void setIdfv(String idfv) {
        this.idfv = idfv;
    }

    public String getIdfv_md5() {
        return idfv_md5;
    }

    public void setIdfv_md5(String idfv_md5) {
        this.idfv_md5 = idfv_md5;
    }

    public String getImsi() {
        return imsi;
    }

    public void setImsi(String imsi) {
        this.imsi = imsi;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getMac_md5() {
        return mac_md5;
    }

    public void setMac_md5(String mac_md5) {
        this.mac_md5 = mac_md5;
    }

    public String getOpen_id() {
        return open_id;
    }

    public void setOpen_id(String open_id) {
        this.open_id = open_id;
    }

    public String getCaid() {
        return caid;
    }

    public void setCaid(String caid) {
        this.caid = caid;
    }

    public String getCaid_md5() {
        return caid_md5;
    }

    public void setCaid_md5(String caid_md5) {
        this.caid_md5 = caid_md5;
    }

    public String getCaid_version() {
        return caid_version;
    }

    public void setCaid_version(String caid_version) {
        this.caid_version = caid_version;
    }

    public Integer getCaid_vendor() {
        return caid_vendor;
    }

    public void setCaid_vendor(Integer caid_vendor) {
        this.caid_vendor = caid_vendor;
    }

    public Integer getOrientation() {
        return orientation;
    }

    public void setOrientation(Integer orientation) {
        this.orientation = orientation;
    }

    public String getBoot_mark() {
        return boot_mark;
    }

    public void setBoot_mark(String boot_mark) {
        this.boot_mark = boot_mark;
    }

    public String getUpdate_mark() {
        return update_mark;
    }

    public void setUpdate_mark(String update_mark) {
        this.update_mark = update_mark;
    }

    public String getOs_com_time() {
        return os_com_time;
    }

    public void setOs_com_time(String os_com_time) {
        this.os_com_time = os_com_time;
    }

    public String getOs_update_time() {
        return os_update_time;
    }

    public void setOs_update_time(String os_update_time) {
        this.os_update_time = os_update_time;
    }

    public String getOs_update_time_nano() {
        return os_update_time_nano;
    }

    public void setOs_update_time_nano(String os_update_time_nano) {
        this.os_update_time_nano = os_update_time_nano;
    }

    public String getBoot_time_sec() {
        return boot_time_sec;
    }

    public void setBoot_time_sec(String boot_time_sec) {
        this.boot_time_sec = boot_time_sec;
    }

    public String getBoot_time_nano() {
        return boot_time_nano;
    }

    public void setBoot_time_nano(String boot_time_nano) {
        this.boot_time_nano = boot_time_nano;
    }

    public Long getDisk_size() {
        return disk_size;
    }

    public void setDisk_size(Long disk_size) {
        this.disk_size = disk_size;
    }

    public Integer getBattery_status() {
        return battery_status;
    }

    public void setBattery_status(Integer battery_status) {
        this.battery_status = battery_status;
    }

    public Integer getBattery_power() {
        return battery_power;
    }

    public void setBattery_power(Integer battery_power) {
        this.battery_power = battery_power;
    }

    public Long getMemory_size() {
        return memory_size;
    }

    public void setMemory_size(Long memory_size) {
        this.memory_size = memory_size;
    }

    public Integer getCpu_num() {
        return cpu_num;
    }

    public void setCpu_num(Integer cpu_num) {
        this.cpu_num = cpu_num;
    }

    public Float getCpu_frequency() {
        return cpu_frequency;
    }

    public void setCpu_frequency(Float cpu_frequency) {
        this.cpu_frequency = cpu_frequency;
    }

    public String getTime_zone() {
        return time_zone;
    }

    public void setTime_zone(String time_zone) {
        this.time_zone = time_zone;
    }

    public Integer getLmt() {
        return lmt;
    }

    public void setLmt(Integer lmt) {
        this.lmt = lmt;
    }

    public String getAppstore_ver() {
        return appstore_ver;
    }

    public void setAppstore_ver(String appstore_ver) {
        this.appstore_ver = appstore_ver;
    }

    public String getKernel_ver() {
        return kernel_ver;
    }

    public void setKernel_ver(String kernel_ver) {
        this.kernel_ver = kernel_ver;
    }

    public String getHms_ver() {
        return hms_ver;
    }

    public void setHms_ver(String hms_ver) {
        this.hms_ver = hms_ver;
    }

    public String getSys_init_time() {
        return sys_init_time;
    }

    public void setSys_init_time(String sys_init_time) {
        this.sys_init_time = sys_init_time;
    }

    public String getDevice_name() {
        return device_name;
    }

    public void setDevice_name(String device_name) {
        this.device_name = device_name;
    }

    public String getDevice_name_md5() {
        return device_name_md5;
    }

    public void setDevice_name_md5(String device_name_md5) {
        this.device_name_md5 = device_name_md5;
    }

    public Double getScreen_size() {
        return screen_size;
    }

    public void setScreen_size(Double screen_size) {
        this.screen_size = screen_size;
    }

    public String getRom_version() {
        return rom_version;
    }

    public void setRom_version(String rom_version) {
        this.rom_version = rom_version;
    }

    public String getApi_level() {
        return api_level;
    }

    public void setApi_level(String api_level) {
        this.api_level = api_level;
    }

    public String getPaid() {
        return paid;
    }

    public void setPaid(String paid) {
        this.paid = paid;
    }

    public List<ShengGuoCaidList> getCaid_list() {
        return caid_list;
    }

    public void setCaid_list(List<ShengGuoCaidList> caid_list) {
        this.caid_list = caid_list;
    }
}
