// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: qihang.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.qihang.dto;

public final class QiHangAdvRequest {
  private QiHangAdvRequest() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      QiHangAdvRequest.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidRequest_Device_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidRequest_Device_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidRequest_Device_CaidInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidRequest_Device_CaidInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidRequest_Imp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidRequest_Imp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidRequest_Imp_Asset_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidRequest_Imp_Asset_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidRequest_App_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidRequest_App_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidRequest_Pmp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidRequest_Pmp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidRequest_CustomizedUserTag_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidRequest_CustomizedUserTag_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidRequest_CustomizedUserTag_InstalledApp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidRequest_CustomizedUserTag_InstalledApp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidRequest_Others_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidRequest_Others_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidResponse_SeatBid_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidResponse_SeatBid_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidResponse_Bid_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidResponse_Bid_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidResponse_Bid_Adm_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidResponse_Bid_Adm_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidResponse_Bid_Adm_Image_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidResponse_Bid_Adm_Image_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_adx_surge_BidResponse_Bid_Adm_Video_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_proto_adx_surge_BidResponse_Bid_Adm_Video_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014qihang.proto\022\017proto.adx.surge\"\251\030\n\nBidR" +
      "equest\022\017\n\002id\030\001 \001(\tH\000\210\001\001\022\030\n\013api_version\030\002" +
      " \001(\tH\001\210\001\001\022\021\n\004test\030\003 \001(\rH\002\210\001\001\022,\n\003imp\030\004 \003(" +
      "\0132\037.proto.adx.surge.BidRequest.Imp\0221\n\003ap" +
      "p\030\005 \001(\0132\037.proto.adx.surge.BidRequest.App" +
      "H\003\210\001\001\0227\n\006device\030\006 \001(\0132\".proto.adx.surge." +
      "BidRequest.DeviceH\004\210\001\001\0221\n\003pmp\030\007 \001(\0132\037.pr" +
      "oto.adx.surge.BidRequest.PmpH\005\210\001\001\022O\n\023cus" +
      "tomized_user_tag\030\010 \001(\0132-.proto.adx.surge" +
      ".BidRequest.CustomizedUserTagH\006\210\001\001\022\024\n\007ti" +
      "meout\030\t \001(\005H\007\210\001\001\022\027\n\nmedia_time\030\n \001(\003H\010\210\001" +
      "\001\022\025\n\010ssp_time\030\013 \001(\003H\t\210\001\001\0227\n\006others\030\014 \001(\013" +
      "2\".proto.adx.surge.BidRequest.OthersH\n\210\001" +
      "\001\032\341\007\n\006Device\022\017\n\002ua\030\001 \001(\tH\000\210\001\001\022\017\n\002ip\030\002 \001(" +
      "\tH\001\210\001\001\022\030\n\013device_type\030\003 \001(\005H\002\210\001\001\022\021\n\004make" +
      "\030\004 \001(\tH\003\210\001\001\022\022\n\005model\030\005 \001(\tH\004\210\001\001\022\021\n\004idfa\030" +
      "\006 \001(\tH\005\210\001\001\022\025\n\010idfa_md5\030\007 \001(\tH\006\210\001\001\022\021\n\004oai" +
      "d\030\010 \001(\tH\007\210\001\001\022\025\n\010oaid_md5\030\t \001(\tH\010\210\001\001\022\021\n\004i" +
      "mei\030\n \001(\tH\t\210\001\001\022\025\n\010imei_md5\030\013 \001(\tH\n\210\001\001\022\017\n" +
      "\002os\030\014 \001(\tH\013\210\001\001\022\020\n\003osv\030\r \001(\tH\014\210\001\001\022\024\n\007carr" +
      "ier\030\016 \001(\005H\r\210\001\001\022\034\n\017connection_type\030\017 \001(\005H" +
      "\016\210\001\001\022\027\n\nandroid_id\030\020 \001(\tH\017\210\001\001\022\033\n\016android" +
      "_id_md5\030\021 \001(\tH\020\210\001\001\022\020\n\003mac\030\022 \001(\tH\021\210\001\001\022\024\n\007" +
      "mac_md5\030\023 \001(\tH\022\210\001\001\022\031\n\014caid_version\030\024 \001(\t" +
      "H\023\210\001\001\022\021\n\004caid\030\025 \001(\tH\024\210\001\001\022\025\n\010caid_md5\030\026 \001" +
      "(\tH\025\210\001\001\022\026\n\tboot_mark\030\027 \001(\tH\026\210\001\001\022\030\n\013updat" +
      "e_mark\030\030 \001(\tH\027\210\001\001\022?\n\ncaid_infos\030\031 \003(\0132+." +
      "proto.adx.surge.BidRequest.Device.CaidIn" +
      "fo\032l\n\010CaidInfo\022\024\n\007version\030\001 \001(\tH\000\210\001\001\022\021\n\004" +
      "caid\030\002 \001(\tH\001\210\001\001\022\025\n\010caid_md5\030\003 \001(\tH\002\210\001\001B\n" +
      "\n\010_versionB\007\n\005_caidB\013\n\t_caid_md5B\005\n\003_uaB" +
      "\005\n\003_ipB\016\n\014_device_typeB\007\n\005_makeB\010\n\006_mode" +
      "lB\007\n\005_idfaB\013\n\t_idfa_md5B\007\n\005_oaidB\013\n\t_oai" +
      "d_md5B\007\n\005_imeiB\013\n\t_imei_md5B\005\n\003_osB\006\n\004_o" +
      "svB\n\n\010_carrierB\022\n\020_connection_typeB\r\n\013_a" +
      "ndroid_idB\021\n\017_android_id_md5B\006\n\004_macB\n\n\010" +
      "_mac_md5B\017\n\r_caid_versionB\007\n\005_caidB\013\n\t_c" +
      "aid_md5B\014\n\n_boot_markB\016\n\014_update_mark\032\246\004" +
      "\n\003Imp\022\017\n\002id\030\001 \001(\tH\000\210\001\001\022\023\n\006tag_id\030\002 \001(\tH\001" +
      "\210\001\001\022\027\n\nsub_tag_id\030\003 \001(\tH\002\210\001\001\022\024\n\007ad_type\030" +
      "\004 \001(\005H\003\210\001\001\022\025\n\010bid_type\030\005 \001(\005H\004\210\001\001\022\026\n\tbid" +
      "_floor\030\006 \001(\003H\005\210\001\001\0224\n\005asset\030\007 \003(\0132%.proto" +
      ".adx.surge.BidRequest.Imp.Asset\022\032\n\rcpc_b" +
      "id_floor\030\010 \001(\003H\006\210\001\001\022\026\n\tads_count\030\t \001(\005H\007" +
      "\210\001\001\022\023\n\006ad_ctr\030\n \001(\002H\010\210\001\001\022\030\n\013ad_pv_level\030" +
      "\013 \001(\005H\t\210\001\001\022\016\n\006ad_emb\030\014 \003(\001\032o\n\005Asset\022\030\n\013t" +
      "emplate_id\030\001 \001(\tH\000\210\001\001\022\022\n\005width\030\002 \001(\005H\001\210\001" +
      "\001\022\023\n\006height\030\003 \001(\005H\002\210\001\001B\016\n\014_template_idB\010" +
      "\n\006_widthB\t\n\007_heightB\005\n\003_idB\t\n\007_tag_idB\r\n" +
      "\013_sub_tag_idB\n\n\010_ad_typeB\013\n\t_bid_typeB\014\n" +
      "\n_bid_floorB\020\n\016_cpc_bid_floorB\014\n\n_ads_co" +
      "untB\t\n\007_ad_ctrB\016\n\014_ad_pv_level\032a\n\003App\022\021\n" +
      "\004name\030\001 \001(\tH\000\210\001\001\022\023\n\006bundle\030\002 \001(\tH\001\210\001\001\022\023\n" +
      "\006verion\030\003 \001(\tH\002\210\001\001B\007\n\005_nameB\t\n\007_bundleB\t" +
      "\n\007_verion\032\245\001\n\003Pmp\022\024\n\007deal_id\030\001 \001(\tH\000\210\001\001\022" +
      "\031\n\014pmp_bid_type\030\002 \001(\005H\001\210\001\001\022\026\n\tpmp_price\030" +
      "\003 \001(\005H\002\210\001\001\022\031\n\014support_quit\030\004 \001(\010H\003\210\001\001B\n\n" +
      "\010_deal_idB\017\n\r_pmp_bid_typeB\014\n\n_pmp_price" +
      "B\017\n\r_support_quit\032\373\004\n\021CustomizedUserTag\022" +
      "V\n\022installed_app_list\030\001 \003(\0132:.proto.adx." +
      "surge.BidRequest.CustomizedUserTag.Insta" +
      "lledApp\022\016\n\006u_tags\030\002 \003(\t\022\022\n\005u_ctr\030\003 \001(\002H\000" +
      "\210\001\001\022\r\n\005u_emb\030\004 \003(\001\022\027\n\nu_pv_level\030\005 \001(\005H\001" +
      "\210\001\001\022\030\n\013u_buy_level\030\006 \001(\005H\002\210\001\001\022\034\n\017u_wake2" +
      "pay_rate\030\007 \001(\002H\003\210\001\001\022\037\n\022u_e_com_time_leve" +
      "l\030\010 \001(\005H\004\210\001\001\022\030\n\020u_e_com_category\030\t \003(\t\022\034" +
      "\n\017open_e_com_time\030\013 \001(\003H\005\210\001\001\022\036\n\021active_e" +
      "_com_time\030\014 \001(\002H\006\210\001\001\022\037\n\022active_device_ti" +
      "me\030\r \001(\002H\007\210\001\001\022\027\n\017active_app_list\030\016 \003(\t\032B" +
      "\n\014InstalledApp\022\017\n\002id\030\001 \001(\rH\000\210\001\001\022\021\n\004name\030" +
      "\002 \001(\tH\001\210\001\001B\005\n\003_idB\007\n\005_nameB\010\n\006_u_ctrB\r\n\013" +
      "_u_pv_levelB\016\n\014_u_buy_levelB\022\n\020_u_wake2p" +
      "ay_rateB\025\n\023_u_e_com_time_levelB\022\n\020_open_" +
      "e_com_timeB\024\n\022_active_e_com_timeB\025\n\023_act" +
      "ive_device_time\032$\n\006Others\022\021\n\004pctr\030\001 \001(\002H" +
      "\000\210\001\001B\007\n\005_pctrB\005\n\003_idB\016\n\014_api_versionB\007\n\005" +
      "_testB\006\n\004_appB\t\n\007_deviceB\006\n\004_pmpB\026\n\024_cus" +
      "tomized_user_tagB\n\n\010_timeoutB\r\n\013_media_t" +
      "imeB\013\n\t_ssp_timeB\t\n\007_others\"\365\r\n\013BidRespo" +
      "nse\022\017\n\002id\030\001 \001(\tH\000\210\001\001\022\023\n\006bid_id\030\002 \001(\tH\001\210\001" +
      "\001\022\020\n\003nbr\030\003 \001(\005H\002\210\001\001\0226\n\010seat_bid\030\004 \003(\0132$." +
      "proto.adx.surge.BidResponse.SeatBid\022\032\n\rc" +
      "old_end_time\030\005 \001(\003H\003\210\001\001\022\025\n\010bid_time\030\006 \001(" +
      "\003H\004\210\001\001\0328\n\007SeatBid\022-\n\003bid\030\001 \003(\0132 .proto.a" +
      "dx.surge.BidResponse.Bid\032\317\013\n\003Bid\022\017\n\002id\030\001" +
      " \001(\tH\000\210\001\001\022\023\n\006imp_id\030\002 \001(\tH\001\210\001\001\022\022\n\005price\030" +
      "\003 \001(\003H\002\210\001\001\022\030\n\013creative_id\030\004 \001(\tH\003\210\001\001\022\021\n\004" +
      "nurl\030\005 \001(\tH\004\210\001\001\022\021\n\004lurl\030\006 \001(\tH\005\210\001\001\022\024\n\014im" +
      "p_trackers\030\007 \003(\t\022\024\n\014clk_trackers\030\010 \003(\t\0226" +
      "\n\003adm\030\t \001(\0132$.proto.adx.surge.BidRespons" +
      "e.Bid.AdmH\006\210\001\001\022\024\n\007deal_id\030\n \001(\tH\007\210\001\001\022\031\n\014" +
      "package_name\030\013 \001(\tH\010\210\001\001\022\025\n\010app_name\030\014 \001(" +
      "\tH\t\210\001\001\022\025\n\010icon_url\030\r \001(\tH\n\210\001\001\022\035\n\020user_sc" +
      "ore_level\030\016 \001(\005H\013\210\001\001\022\025\n\010bid_type\030\017 \001(\005H\014" +
      "\210\001\001\022\034\n\017origin_bid_type\030\020 \001(\005H\r\210\001\001\022\037\n\022ori" +
      "ginal_win_price\030\021 \001(\003H\016\210\001\001\032\313\006\n\003Adm\022\030\n\013te" +
      "mplate_id\030\001 \001(\tH\000\210\001\001\022\022\n\005title\030\002 \001(\tH\001\210\001\001" +
      "\022\021\n\004desc\030\003 \001(\tH\002\210\001\001\0229\n\005image\030\004 \003(\0132*.pro" +
      "to.adx.surge.BidResponse.Bid.Adm.Image\022>" +
      "\n\005video\030\005 \001(\0132*.proto.adx.surge.BidRespo" +
      "nse.Bid.Adm.VideoH\003\210\001\001\022\024\n\007ad_type\030\006 \001(\005H" +
      "\004\210\001\001\022\026\n\tdeep_link\030\007 \001(\tH\005\210\001\001\022\033\n\016universa" +
      "l_link\030\010 \001(\tH\006\210\001\001\022\031\n\014landing_site\030\t \001(\tH" +
      "\007\210\001\001\022\024\n\007deal_id\030\n \001(\tH\010\210\001\001\022\032\n\rcover_img_" +
      "url\030\013 \001(\tH\t\210\001\001\032{\n\005Image\022\020\n\003url\030\001 \001(\tH\000\210\001" +
      "\001\022\022\n\005width\030\002 \001(\005H\001\210\001\001\022\023\n\006height\030\003 \001(\005H\002\210" +
      "\001\001\022\021\n\004mime\030\004 \001(\tH\003\210\001\001B\006\n\004_urlB\010\n\006_widthB" +
      "\t\n\007_heightB\007\n\005_mime\032\351\001\n\005Video\022\026\n\tvideo_u" +
      "rl\030\001 \001(\tH\000\210\001\001\022\022\n\005width\030\002 \001(\005H\001\210\001\001\022\023\n\006hei" +
      "ght\030\003 \001(\005H\002\210\001\001\022\025\n\010duration\030\004 \001(\005H\003\210\001\001\022\024\n" +
      "\007bitrate\030\005 \001(\005H\004\210\001\001\022\021\n\004size\030\006 \001(\005H\005\210\001\001\022\021" +
      "\n\004mime\030\007 \001(\tH\006\210\001\001B\014\n\n_video_urlB\010\n\006_widt" +
      "hB\t\n\007_heightB\013\n\t_durationB\n\n\010_bitrateB\007\n" +
      "\005_sizeB\007\n\005_mimeB\016\n\014_template_idB\010\n\006_titl" +
      "eB\007\n\005_descB\010\n\006_videoB\n\n\010_ad_typeB\014\n\n_dee" +
      "p_linkB\021\n\017_universal_linkB\017\n\r_landing_si" +
      "teB\n\n\010_deal_idB\020\n\016_cover_img_urlB\005\n\003_idB" +
      "\t\n\007_imp_idB\010\n\006_priceB\016\n\014_creative_idB\007\n\005" +
      "_nurlB\007\n\005_lurlB\006\n\004_admB\n\n\010_deal_idB\017\n\r_p" +
      "ackage_nameB\013\n\t_app_nameB\013\n\t_icon_urlB\023\n" +
      "\021_user_score_levelB\013\n\t_bid_typeB\022\n\020_orig" +
      "in_bid_typeB\025\n\023_original_win_priceB\005\n\003_i" +
      "dB\t\n\007_bid_idB\006\n\004_nbrB\020\n\016_cold_end_timeB\013" +
      "\n\t_bid_timeB6\n cn.taken.ad.logic.adv.qih" +
      "ang.dtoB\020QiHangAdvRequestP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_proto_adx_surge_BidRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_proto_adx_surge_BidRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidRequest_descriptor,
        new java.lang.String[] { "Id", "ApiVersion", "Test", "Imp", "App", "Device", "Pmp", "CustomizedUserTag", "Timeout", "MediaTime", "SspTime", "Others", });
    internal_static_proto_adx_surge_BidRequest_Device_descriptor =
      internal_static_proto_adx_surge_BidRequest_descriptor.getNestedTypes().get(0);
    internal_static_proto_adx_surge_BidRequest_Device_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidRequest_Device_descriptor,
        new java.lang.String[] { "Ua", "Ip", "DeviceType", "Make", "Model", "Idfa", "IdfaMd5", "Oaid", "OaidMd5", "Imei", "ImeiMd5", "Os", "Osv", "Carrier", "ConnectionType", "AndroidId", "AndroidIdMd5", "Mac", "MacMd5", "CaidVersion", "Caid", "CaidMd5", "BootMark", "UpdateMark", "CaidInfos", });
    internal_static_proto_adx_surge_BidRequest_Device_CaidInfo_descriptor =
      internal_static_proto_adx_surge_BidRequest_Device_descriptor.getNestedTypes().get(0);
    internal_static_proto_adx_surge_BidRequest_Device_CaidInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidRequest_Device_CaidInfo_descriptor,
        new java.lang.String[] { "Version", "Caid", "CaidMd5", });
    internal_static_proto_adx_surge_BidRequest_Imp_descriptor =
      internal_static_proto_adx_surge_BidRequest_descriptor.getNestedTypes().get(1);
    internal_static_proto_adx_surge_BidRequest_Imp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidRequest_Imp_descriptor,
        new java.lang.String[] { "Id", "TagId", "SubTagId", "AdType", "BidType", "BidFloor", "Asset", "CpcBidFloor", "AdsCount", "AdCtr", "AdPvLevel", "AdEmb", });
    internal_static_proto_adx_surge_BidRequest_Imp_Asset_descriptor =
      internal_static_proto_adx_surge_BidRequest_Imp_descriptor.getNestedTypes().get(0);
    internal_static_proto_adx_surge_BidRequest_Imp_Asset_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidRequest_Imp_Asset_descriptor,
        new java.lang.String[] { "TemplateId", "Width", "Height", });
    internal_static_proto_adx_surge_BidRequest_App_descriptor =
      internal_static_proto_adx_surge_BidRequest_descriptor.getNestedTypes().get(2);
    internal_static_proto_adx_surge_BidRequest_App_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidRequest_App_descriptor,
        new java.lang.String[] { "Name", "Bundle", "Verion", });
    internal_static_proto_adx_surge_BidRequest_Pmp_descriptor =
      internal_static_proto_adx_surge_BidRequest_descriptor.getNestedTypes().get(3);
    internal_static_proto_adx_surge_BidRequest_Pmp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidRequest_Pmp_descriptor,
        new java.lang.String[] { "DealId", "PmpBidType", "PmpPrice", "SupportQuit", });
    internal_static_proto_adx_surge_BidRequest_CustomizedUserTag_descriptor =
      internal_static_proto_adx_surge_BidRequest_descriptor.getNestedTypes().get(4);
    internal_static_proto_adx_surge_BidRequest_CustomizedUserTag_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidRequest_CustomizedUserTag_descriptor,
        new java.lang.String[] { "InstalledAppList", "UTags", "UCtr", "UEmb", "UPvLevel", "UBuyLevel", "UWake2PayRate", "UEComTimeLevel", "UEComCategory", "OpenEComTime", "ActiveEComTime", "ActiveDeviceTime", "ActiveAppList", });
    internal_static_proto_adx_surge_BidRequest_CustomizedUserTag_InstalledApp_descriptor =
      internal_static_proto_adx_surge_BidRequest_CustomizedUserTag_descriptor.getNestedTypes().get(0);
    internal_static_proto_adx_surge_BidRequest_CustomizedUserTag_InstalledApp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidRequest_CustomizedUserTag_InstalledApp_descriptor,
        new java.lang.String[] { "Id", "Name", });
    internal_static_proto_adx_surge_BidRequest_Others_descriptor =
      internal_static_proto_adx_surge_BidRequest_descriptor.getNestedTypes().get(5);
    internal_static_proto_adx_surge_BidRequest_Others_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidRequest_Others_descriptor,
        new java.lang.String[] { "Pctr", });
    internal_static_proto_adx_surge_BidResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_proto_adx_surge_BidResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidResponse_descriptor,
        new java.lang.String[] { "Id", "BidId", "Nbr", "SeatBid", "ColdEndTime", "BidTime", });
    internal_static_proto_adx_surge_BidResponse_SeatBid_descriptor =
      internal_static_proto_adx_surge_BidResponse_descriptor.getNestedTypes().get(0);
    internal_static_proto_adx_surge_BidResponse_SeatBid_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidResponse_SeatBid_descriptor,
        new java.lang.String[] { "Bid", });
    internal_static_proto_adx_surge_BidResponse_Bid_descriptor =
      internal_static_proto_adx_surge_BidResponse_descriptor.getNestedTypes().get(1);
    internal_static_proto_adx_surge_BidResponse_Bid_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidResponse_Bid_descriptor,
        new java.lang.String[] { "Id", "ImpId", "Price", "CreativeId", "Nurl", "Lurl", "ImpTrackers", "ClkTrackers", "Adm", "DealId", "PackageName", "AppName", "IconUrl", "UserScoreLevel", "BidType", "OriginBidType", "OriginalWinPrice", });
    internal_static_proto_adx_surge_BidResponse_Bid_Adm_descriptor =
      internal_static_proto_adx_surge_BidResponse_Bid_descriptor.getNestedTypes().get(0);
    internal_static_proto_adx_surge_BidResponse_Bid_Adm_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidResponse_Bid_Adm_descriptor,
        new java.lang.String[] { "TemplateId", "Title", "Desc", "Image", "Video", "AdType", "DeepLink", "UniversalLink", "LandingSite", "DealId", "CoverImgUrl", });
    internal_static_proto_adx_surge_BidResponse_Bid_Adm_Image_descriptor =
      internal_static_proto_adx_surge_BidResponse_Bid_Adm_descriptor.getNestedTypes().get(0);
    internal_static_proto_adx_surge_BidResponse_Bid_Adm_Image_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidResponse_Bid_Adm_Image_descriptor,
        new java.lang.String[] { "Url", "Width", "Height", "Mime", });
    internal_static_proto_adx_surge_BidResponse_Bid_Adm_Video_descriptor =
      internal_static_proto_adx_surge_BidResponse_Bid_Adm_descriptor.getNestedTypes().get(1);
    internal_static_proto_adx_surge_BidResponse_Bid_Adm_Video_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_proto_adx_surge_BidResponse_Bid_Adm_Video_descriptor,
        new java.lang.String[] { "VideoUrl", "Width", "Height", "Duration", "Bitrate", "Size", "Mime", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
