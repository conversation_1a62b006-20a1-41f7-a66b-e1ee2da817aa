package cn.taken.ad.logic.adv.liyue;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Aes;
import cn.taken.ad.component.utils.encryption.Base64;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.liyue.dto.request.*;
import cn.taken.ad.logic.adv.liyue.dto.response.LiYueResponse;
import cn.taken.ad.logic.adv.liyue.dto.response.LiYueResponseApp;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.ParamParser;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 鲤跃
 */
@Component("LIYUE" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class LiYueAdvProcessor implements AdvProcessor {

    private static final Logger log = LoggerFactory.getLogger(LiYueAdvProcessor.class);

    public static final String AES_KEY = "secretKey";

    private static final String algorithm = "AES/CBC/PKCS5Padding";

    private static final Header userAgent = new BasicHeader("user-agent", "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.87 Safari/537.36");

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        LiYueEncryptRequest request = new LiYueEncryptRequest();
        LiYueBidRequest bidRequest = convertBidRequest(rtbDto, advDto);
        String dataJson = JsonHelper.toJsonString(bidRequest);
        // 获取APP 个性化参数 token
        String secretKey = ParamParser.parseParamByJson(advDto.getAppPnyParam()).get(AES_KEY);
        if (StringUtils.isEmpty(secretKey)) {
            // 获取 账户级参数
            secretKey = ParamParser.parseParamByJson(advDto.getPnyParam()).get(AES_KEY);
        }
        // 生成随机向量
        String iv = getIv();
        // 数据加密
        String encrypt = encryptData(secretKey, iv, dataJson);
        request.setIv(iv);
        request.setApp_key(advDto.getAppCode());
        request.setData(encrypt);
        advDto.setReqObj(request);
        String json = JsonHelper.toJsonString(request);
        HttpResult httpResult = httpClient.postJson(advDto.getRtburl(), json, "UTF-8", new Header[]{new BasicHeader("Content-Type", "application/json"), userAgent}, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        String resp = httpResult.getDataStringUTF8();
        return parseResponse(resp, advDto);
    }


    public RtbResponseDto parseResponse(String resp, RtbAdvDto advDto) {
        LiYueResponse response;
        try {
            advDto.setRespObj(resp);
            response = JsonHelper.fromJson(LiYueResponse.class, resp);
        } catch (Exception e) {
            log.error("Error", e);
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Serializable resp fail");
        }
        if (null == response) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Serializable resp fail");
        }
        if (null == response.getStatus()) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "resp no errorcode");
        }
        if (response.getStatus() != 1000) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), response.getStatus() + "");
        }
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", response.getStatus() + "");
        response.getData().forEach(tag -> {
            TagResponseDto tagResponseDto = new TagResponseDto();
            tagResponseDto.setTitle(tag.getTitle());
            tagResponseDto.setDesc(tag.getDesc());
            if (null != tag.getPrice()) {
                tagResponseDto.setPrice(tag.getPrice().doubleValue());
            }
            tagResponseDto.setLogoUrl(tag.getIcon_url());
            tagResponseDto.setImgUrls(tag.getImage_urls());
            tagResponseDto.setClickUrl(tag.getClick_url());
            if (tag.getAction_type() != null) {
                if (tag.getAction_type() == 1) {
                    tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
                } else if (tag.getAction_type() == 2) {
                    tagResponseDto.setActionType(ActionType.DOWNLOAD);
                } else if (tag.getAction_type() == 3) {
                    //微信小程序 暂无不支持
                }
            }
            tagResponseDto.setMaterialWidth(tag.getWidth());
            tagResponseDto.setMaterialHeight(tag.getHeight());

            tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
            // APP
            ResponseAppDto appInfo = new ResponseAppDto();
            appInfo.setPackageName(tag.getPackage_name());
            appInfo.setAppName(tag.getApp_name());
            //暂无 wx_mp_id 微信⼩程序 ID
            //暂无 wx_mp_path 微信⼩程序路径
            tagResponseDto.setAppInfo(appInfo);
            if (null != tag.getApp_info()) {
                LiYueResponseApp liYueApp = tag.getApp_info();
                if (StringUtils.isNotEmpty(liYueApp.getApp_name())) {
                    appInfo.setAppName(liYueApp.getApp_name());
                }
                appInfo.setAppVersion(liYueApp.getApp_version());
                appInfo.setAppDeveloper(liYueApp.getDeveloper());
                appInfo.setAppPrivacyUrl(liYueApp.getPrivacy_url());
                appInfo.setAppPermissionInfoUrl(liYueApp.getPermission_url());
                appInfo.setAppPermContent(liYueApp.getPermission_desc());
                appInfo.setAppInfo(liYueApp.getApp_intro());
                if (StringUtils.isNotEmpty(liYueApp.getPackage_name())) {
                    appInfo.setPackageName(liYueApp.getPackage_name());
                }
                // 暂无 app_icon_url 图标
                if (null != liYueApp.getSize()) {
                    appInfo.setAppSize(liYueApp.getSize().longValue());
                }
                if (StringUtils.isNotEmpty(liYueApp.getDownload_url())) {
                    tagResponseDto.setClickUrl(liYueApp.getDownload_url());
                }
            }

            // 暂无 btn_content 按钮⽂字（可能为空）
            // 暂无 ad_type
            // 暂无 exposure_time 表示该⼴告素材曝光请求次数的上界， =0 时则表示不限制上报次数， >0 时指定⼏次就是⼏次
            if (StringUtils.isNotEmpty(tag.getDeep_link_url())) {
                tagResponseDto.setActionType(ActionType.DEEPLINK);
                tagResponseDto.setDeepLinkUrl(tag.getDeep_link_url());
            }

            // Video
            ResponseVideoDto video = new ResponseVideoDto();
            if (video.getVideoUrl() != null) {
                tagResponseDto.setMaterialType(MaterialType.VIDEO);
                video.setVideoUrl(tag.getVideo_url());
            }
            if (null != tag.getVideo_duration()) {
                video.setDuration(tag.getVideo_duration().intValue());
            }
            if (null != tag.getVideo_size() && tag.getVideo_size() > 0) {
                video.setVideoSize(tag.getVideo_size().longValue());
            }
            if (StringUtils.isNotEmpty(tag.getCover_url())) {
                video.setCoverImgUrls(Collections.singletonList(tag.getCover_url()));
            }
            if (null != tag.getVideo_type()) {
                video.setOrientationType(1 == tag.getVideo_type() ? OrientationType.HORIZONTAL.getType() : OrientationType.VERTICAL.getType());
            }
            // 暂无 valid_time视频作为有效播放的开始时⻓
            tagResponseDto.setVideoInfo(video);
            // 监控事件
            tagResponseDto.setTracks(new ArrayList<>());
            // 曝光监控
            if (null != tag.getView_track_urls() && !tag.getView_track_urls().isEmpty()) {
                // 价格宏替换
                List<String> url = tag.getView_track_urls();
                if (null != tag.getPrice()) {
                    // 出价信息不为空时 按照出价替换
                    url = replaceMacro("$WL_PRICE$", url, Base64.encode(tag.getPrice().toString()));
                }
                tagResponseDto.getTracks().add(new ResponseTrackDto(EventType.EXPOSURE.getType(), url));
            }
            if (null != tag.getClick_track_urls() && !tag.getClick_track_urls().isEmpty()) {
                tagResponseDto.getTracks().add(new ResponseTrackDto(EventType.CLICK.getType(), tag.getView_track_urls()));
            }
            if (null != tag.getDownload_start_track_urls() && !tag.getDownload_start_track_urls().isEmpty()) {
                tagResponseDto.getTracks().add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), tag.getDownload_start_track_urls()));
            }
            if (null != tag.getDownload_success_track_urls() && !tag.getDownload_success_track_urls().isEmpty()) {
                tagResponseDto.getTracks().add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), tag.getDownload_start_track_urls()));
            }
            if (null != tag.getInstall_start_track_urls() && !tag.getInstall_start_track_urls().isEmpty()) {
                tagResponseDto.getTracks().add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), tag.getDownload_start_track_urls()));
            }
            if (null != tag.getInstall_success_track_urls() && !tag.getInstall_success_track_urls().isEmpty()) {
                tagResponseDto.getTracks().add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), tag.getDownload_start_track_urls()));
            }
            // deep_link_track_events DeepLink 的追踪事件，具体上报逻辑⻅ 2.6 DeepLink⼴告 事件上报（需要宏替换，需要匹配所有的指定类型的事件进⾏调⽤，并不是第⼀个）
            ResponseTrackDto deepLinkStart = new ResponseTrackDto(EventType.DEEPLINK_START.getType(), new ArrayList<>());
            ResponseTrackDto deepLinkOpenFail = new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), new ArrayList<>());
            ResponseTrackDto deepLinkOpenSuccess = new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), new ArrayList<>());
            if (null != tag.getDeep_link_track_events() && !tag.getDeep_link_track_events().isEmpty()) {
                tag.getDeep_link_track_events().forEach(item -> {
                    if (StringUtils.isNotEmpty(item.getUrl()) && StringUtils.isNotEmpty(item.getEvent())) {
                        switch (item.getEvent()) {
                            case "open_url_app":
                                // deeplink 检测到应⽤已安装
                                //deepLinkStart.getTrackUrls().add(item.getUrl());
                                break;
                            case "open_fallback_url":
                                // deeplink 检测未安装（未安装吊起失败）
                                //deepLinkOpenFail.getTrackUrls().add(item.getUrl());
                                break;
                            case "dpl_success":
                                // 已安装吊起成功
                                deepLinkOpenSuccess.getTrackUrls().add(item.getUrl());
                                break;
                            case "dpl_fail":
                                // 已安装吊起失败
                                //deepLinkOpenFail.getTrackUrls().add(item.getUrl());
                                break;
                        }
                    }
                });
            }
            // 视频宏 处理 比较复杂
            ResponseTrackDto begin = new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), new ArrayList<>());
            ResponseTrackDto repeat = new ResponseTrackDto(EventType.VIDEO_PLAY_REPLAY.getType(), new ArrayList<>());
            ResponseTrackDto pause = new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), new ArrayList<>());
            ResponseTrackDto end = new ResponseTrackDto(EventType.VIDEO_END.getType(), new ArrayList<>());
            // video_general_event_trackings 视频事件上报（需要宏替换）
            if (null != tag.getVideo_general_event_trackings() && !tag.getVideo_general_event_trackings().isEmpty()) {
                // 替换 __EVENT_TYPE__
                tag.getVideo_general_event_trackings().forEach(item -> {
                    if (null == tag.getAd_type() || tag.getAd_type() != 8) {
                        // 接口文档要求 对于激励视频不需要上传开始播放事件
                        begin.getTrackUrls().add(item.replace("__EVENT_TYPE__", 200 + ""));
                        repeat.getTrackUrls().addAll(begin.getTrackUrls());
                    }
                    pause.getTrackUrls().add(item.replace("__EVENT_TYPE__", 201 + ""));
                    end.getTrackUrls().add(item.replace("__EVENT_TYPE__", 205 + ""));
                });
            }
            // video_event_trackings 视频特定事件上报（需要宏替换），详⻅ 2.5 视频⼴告事件上报
            if (null != tag.getVideo_event_trackings() && !tag.getVideo_event_trackings().isEmpty()) {
                tag.getVideo_event_trackings().forEach(item -> {
                    if (StringUtils.isNotEmpty(item.getEvent()) && StringUtils.isNotEmpty(item.getUrl())) {
                        switch (item.getEvent()) {
                            case "start":
                                if (tag.getAd_type() == null || tag.getAd_type() != 8) {
                                    begin.getTrackUrls().add(item.getUrl().replace("__EVENT_TYPE__", 200 + ""));
                                    repeat.getTrackUrls().addAll(begin.getTrackUrls());
                                }
                                break;
                            case "pause":
                                pause.getTrackUrls().add(item.getUrl().replace("__EVENT_TYPE__", 201 + ""));
                                break;
                            case "complete":
                                end.getTrackUrls().add(item.getUrl().replace("__EVENT_TYPE__", 205 + ""));
                                break;
                        }
                    }
                });
            }
            if (!deepLinkStart.getTrackUrls().isEmpty()) {
                tagResponseDto.getTracks().add(deepLinkStart);
            }
            if (!deepLinkOpenFail.getTrackUrls().isEmpty()) {
                tagResponseDto.getTracks().add(deepLinkOpenFail);
            }
            if (!deepLinkOpenSuccess.getTrackUrls().isEmpty()) {
                tagResponseDto.getTracks().add(deepLinkOpenSuccess);
            }
            if (!begin.getTrackUrls().isEmpty()) {
                tagResponseDto.getTracks().add(begin);
            }
            if (repeat.getTrackUrls().isEmpty()) {
                tagResponseDto.getTracks().add(repeat);
            }
            if (!pause.getTrackUrls().isEmpty()) {
                tagResponseDto.getTracks().add(pause);
            }
            if (!end.getTrackUrls().isEmpty()) {
                tagResponseDto.getTracks().add(end);
            }
            // 统一进行宏替换
            tagResponseDto.getTracks().forEach(track -> {
                List<String> urls = track.getTrackUrls();
                urls = replaceMacro("$TS", urls, MacroType.TIME.getCode());
                urls = replaceMacro("$CLK_DOWN_X", urls, MacroType.DOWN_X.getCode());
                urls = replaceMacro("$CLK_DOWN_Y", urls, MacroType.DOWN_Y.getCode());

                urls = replaceMacro("$CLK_UP_X", urls, MacroType.UP_X.getCode());
                urls = replaceMacro("$CLK_UP_Y", urls, MacroType.UP_Y.getCode());
                // 暂无 视频宏 __OFFSET_PCT__ 播放进度百分⽐，⼩数表示，例如：0.25
                // 暂无 视频宏 __OFFSET__ 播放时⻓，单位为秒
                // 暂无 视频宏 __TOTAL_OFFSET__ 累计播放时⻓（到此次上报客户端⼀共播放了多少秒），单位为秒
                // 暂无 视频宏 __VALID_COUNT__  __TOTAL_OFFSET__ >= valid_time 后每次上报递增⼀次，表示是第⼏次上报__TOTAL_OFFSET__ >= valid_time 的事件，在 __TOTAL_OFFSET__ < valid_time 时传 0 即可
                // 暂无 __NETWORK__ ⽹络环境，0：未知；1：WIFI； 2：⾮WIFI
                track.setTrackUrls(urls);
            });
            responseDto.getTags().add(tagResponseDto);
        });
        return responseDto;
    }


    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam reqDto) {
        return SuperResult.rightResult();
    }


    private LiYueBidRequest convertBidRequest(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        RequestNetworkDto networkDto = rtbDto.getNetwork();
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        LiYueBidRequest request = new LiYueBidRequest();
        request.setPid(advDto.getTagCode());
        request.setTs(System.currentTimeMillis());
        request.setClient_ip(networkDto.getIp());
        request.setUser_agent(deviceDto.getUserAgent());
        // 底价
        if (null != rtbDto.getTag().getPrice()) {
            request.setPrice(rtbDto.getTag().getPrice().longValue());
        }
        // Device
        request.setDevice(convertDevice(deviceDto, rtbDto.getNetwork()));
        // APP
        RequestTagDto tagDto = rtbDto.getTag();
        RequestAppDto appDto = rtbDto.getApp();
        request.setApp(convertApp(advDto.getAppCode(), appDto.getAppName(), appDto.getBundle(), appDto.getAppVersion(), appDto.getAppVersionCode(), true));
        //Geo
        request.setGeo(convertGeo(rtbDto.getGeo()));
        //User
        request.setUser(convertUser(rtbDto.getUser()));
        return request;
    }

    private LiYueRequestDevice convertDevice(RequestDeviceDto deviceDto, RequestNetworkDto networkDto) {
        // Device
        LiYueRequestDevice requestDevice = new LiYueRequestDevice();
        if (deviceDto.getOsType().getType() == OsType.ANDROID.getType()) {
            requestDevice.setOs("Android");
        } else if (deviceDto.getOsType().getType() == OsType.IOS.getType()) {
            requestDevice.setOs("iOS");
        } else {
            requestDevice.setOs("");
        }
        requestDevice.setOsv(deviceDto.getOsVersion());
        CarrierType carrierType = networkDto.getCarrierType();
        if (carrierType == CarrierType.UNKNOWN) {
            requestDevice.setCarrier(0);
        } else if (carrierType == CarrierType.CM) {
            requestDevice.setCarrier(1);
        } else if (carrierType == CarrierType.CU) {
            requestDevice.setCarrier(2);
        } else if (carrierType == CarrierType.CT) {
            requestDevice.setCarrier(3);
        } else {
            requestDevice.setCarrier(0);
        }
        ConnectionType connectionType = networkDto.getConnectType();
        if (connectionType == ConnectionType.UNKNOWN) {
            requestDevice.setNetwork(0);
        } else if (connectionType == ConnectionType.ETHERNET) {
            requestDevice.setNetwork(1);
        } else if (connectionType == ConnectionType.WIFI) {
            requestDevice.setNetwork(2);
        } else if (connectionType == ConnectionType.NETWORK_CELLULAR) {
            requestDevice.setNetwork(3);
        } else if (connectionType == ConnectionType.NETWORK_2G) {
            requestDevice.setNetwork(4);
        } else if (connectionType == ConnectionType.NETWORK_3G) {
            requestDevice.setNetwork(5);
        } else if (connectionType == ConnectionType.NETWORK_4G) {
            requestDevice.setNetwork(6);
        } else if (connectionType == ConnectionType.NETWORK_5G) {
            requestDevice.setNetwork(7);
        }
        // 暂无 resolution设备屏幕的分辨率, 例如: 720*1280
        requestDevice.setResolution("");
        requestDevice.setDensity(null != deviceDto.getScreenDensity() ? deviceDto.getScreenDensity().toString() : "");
        requestDevice.setAid(StringUtils.isNotEmpty(deviceDto.getAndroidId()) ? deviceDto.getAndroidId() : "");
        requestDevice.setOaid(StringUtils.isNotEmpty(deviceDto.getOaid()) ? deviceDto.getOaid() : "");
        requestDevice.setImei(StringUtils.isNotEmpty(deviceDto.getImei()) ? deviceDto.getImei() : "");
        requestDevice.setImsi(StringUtils.isNotEmpty(deviceDto.getImsi()) ? deviceDto.getImsi() : "");
        requestDevice.setSsid(StringUtils.isNotBlank(networkDto.getSsid()) ? networkDto.getSsid() : "");
        // aaid Android设备的Advertising ID
        requestDevice.setAaid("");
        requestDevice.setIdfa(StringUtils.isNotEmpty(deviceDto.getIdfa()) ? deviceDto.getIdfa() : "");
        requestDevice.setIdfv(StringUtils.isNotEmpty(deviceDto.getIdfv()) ? deviceDto.getIdfv() : "");
        requestDevice.setOpen_udid(StringUtils.isNotEmpty(deviceDto.getOpenUdId()) ? deviceDto.getOpenUdId() : "");
        requestDevice.setMac(StringUtils.isNotEmpty(networkDto.getMac()) ? networkDto.getMac() : "");
        requestDevice.setOrientation(-1);
        if (null != deviceDto.getOrientation()) {
            if (deviceDto.getOrientation().getType() == OrientationType.VERTICAL.getType()) {
                requestDevice.setOrientation(0);
            } else if (deviceDto.getOrientation().getType() == OrientationType.HORIZONTAL.getType()) {
                requestDevice.setOrientation(1);
            }
        }
        if (StringUtils.isNotEmpty(deviceDto.getVendor())) {
            requestDevice.setVendor(deviceDto.getVendor());
        } else if (StringUtils.isNotEmpty(deviceDto.getBrand())) {
            requestDevice.setVendor(deviceDto.getBrand());
        }
        requestDevice.setModel(StringUtils.isNotEmpty(deviceDto.getModel()) ? deviceDto.getModel() : "");
        // 暂无 root Android设备是否root, iOS设备是否越狱, 1:是, 0:否
        if (null != deviceDto.getIsRoot()) {
            if (deviceDto.getIsRoot() == 1) {
                requestDevice.setRoot(1);
            } else {
                requestDevice.setRoot(0);
            }
        }
        // 暂无 sim_count ⼿机SIM卡数量
        // 暂无 dev_debug 是否处于开发者模式, 1:是, 0:否, -1:未知
        requestDevice.setBoot_mark(StringUtils.isNotEmpty(deviceDto.getBootMark()) ? deviceDto.getBootMark() : "");
        requestDevice.setUpdate_mark(StringUtils.isNotEmpty(deviceDto.getUpdateMark()) ? deviceDto.getUpdateMark() : "");
        requestDevice.setAppstore_version(StringUtils.isNotEmpty(deviceDto.getAppStoreVersion()) ? deviceDto.getAppStoreVersion() : "");
        requestDevice.setHms_version(StringUtils.isNotEmpty(deviceDto.getHmsVersion()) ? deviceDto.getHmsVersion() : "");
        requestDevice.setInstalled_apps(new ArrayList<>());
        if (null != deviceDto.getInstalledAppInfo() && !deviceDto.getInstalledAppInfo().isEmpty()) {
            deviceDto.getInstalledAppInfo().forEach(appInfo -> {
                if (StringUtils.isNotEmpty(appInfo.getPackageName())) {
                    requestDevice.getInstalled_apps().add(appInfo.getPackageName());
                }
            });
        }
        List<RequestCaidDto> caidDtos = deviceDto.getCaids();
        RequestCaidDto caidDto = null;
        if (null != caidDtos && !caidDtos.isEmpty()) {
            for (RequestCaidDto dto : caidDtos) {
                if (StringUtils.isNotEmpty(dto.getCaid())) {
                    caidDto = dto;
                    break;
                }
            }
        }
        if (null != caidDto) {
            requestDevice.setCaid(caidDto.getCaid());
            requestDevice.setCaid_version(caidDto.getVersion());
        }
        requestDevice.setBirth_time(StringUtils.isNotEmpty(deviceDto.getSysInitTime()) ? deviceDto.getSysInitTime() : "");
        requestDevice.setPaid(deviceDto.getPaid());
        requestDevice.setStartup_time(null != deviceDto.getSysStartTime() ? deviceDto.getSysStartTime().toString() : "");
        requestDevice.setMd_time("");
        if (null != deviceDto.getSysUpdateTime()) {
            requestDevice.setMd_time(deviceDto.getSysUpdateTime().toString());
        }
        requestDevice.setDevice_name(StringUtils.isNotEmpty(deviceDto.getDeviceName()) ? deviceDto.getDeviceName() : "");
        requestDevice.setHardware_machine(StringUtils.isNotEmpty(deviceDto.getHardwareMachine()) ? deviceDto.getHardwareMachine() : "");
        requestDevice.setModel(StringUtils.isNotEmpty(deviceDto.getModel()) ? deviceDto.getModel() : "");
        if (null != deviceDto.getDeviceHardDisk()) {
            requestDevice.setDisk_total(deviceDto.getDeviceHardDisk());
        }
        if (null != deviceDto.getDeviceMemory()) {
            requestDevice.setMem_total(deviceDto.getDeviceMemory());
        }
        requestDevice.setNation(StringUtils.isNotEmpty(deviceDto.getCountry()) ? deviceDto.getCountry() : "");
        requestDevice.setLan(StringUtils.isNotEmpty(deviceDto.getLanguage()) ? deviceDto.getLanguage() : "");
        requestDevice.setZone(StringUtils.isNotEmpty(deviceDto.getTimeZone()) ? deviceDto.getTimeZone() : "");
        if (null != deviceDto.getCpuNum()) {
            requestDevice.setCpu_num(deviceDto.getCpuNum());
        }
        if (null != deviceDto.getCpuFreq()) {
            requestDevice.setCpu_frequency(deviceDto.getCpuFreq().floatValue());
        }
        if (null != deviceDto.getBatteryStatus()) {
            requestDevice.setBattery_status(deviceDto.getBatteryStatus());
        } else {
            requestDevice.setBattery_status(0);
        }
        if (null != deviceDto.getBatteryPower()) {
            requestDevice.setBattery_power(deviceDto.getBatteryPower());
        }
        if (null != deviceDto.getIdfaPolicy()) {
            switch (deviceDto.getIdfaPolicy()) {
                case 1:
                    requestDevice.setAuth_status(0);
                    break;
                case 2:
                    requestDevice.setAuth_status(1);
                    break;
                case 3:
                    requestDevice.setAuth_status(2);
                    break;
                case 4:
                    requestDevice.setAuth_status(3);
                    break;
                default:
                    requestDevice.setAuth_status(0);
                    break;
            }
        } else {
            requestDevice.setAuth_status(0);
        }
        if (null != deviceDto.getScreenInch()) {
            requestDevice.setInch(deviceDto.getScreenInch().floatValue());
        }
        if (null != deviceDto.getApiLevel()) {
            requestDevice.setApi_level(deviceDto.getApiLevel());
        }
        if (null != deviceDto.getPpi()) {
            requestDevice.setPpi(deviceDto.getPpi());
        }
        return requestDevice;
    }

    private LiYueRequestApp convertApp(String advChAppId, String appName, String bundle, String appVersion, String appVersionCode, Boolean isDeeplink) {
        LiYueRequestApp requestApp = new LiYueRequestApp();
        requestApp.setApp_key(advChAppId);
        requestApp.setBundle(bundle);
        requestApp.setApp_name(StringUtils.isNotEmpty(appName) ? appName : "");
        requestApp.setApp_version(appVersion);
        requestApp.setApp_version_code(StringUtils.isNotEmpty(appVersionCode) ? appVersionCode : "");
        // 暂无 channel
        requestApp.setChannel("");
        requestApp.setSupport_dpl(isDeeplink ? 1 : 0);
        return requestApp;
    }

    private LiYueRequestGeo convertGeo(RequestGeoDto geoDto) {
        LiYueRequestGeo requestGeo = new LiYueRequestGeo();
        if (geoDto.getLatitude() != null) {
            requestGeo.setLat(geoDto.getLatitude().floatValue());
        }
        if (geoDto.getLongitude() != null) {
            requestGeo.setLon(geoDto.getLongitude().floatValue());
        }
        // 暂无 province 省份
        requestGeo.setProvince("");
        // 暂无 city 城市
        requestGeo.setCity("");
        // 暂无 district 地区
        requestGeo.setDistrict("");
        // ad_code 设备通过⾼德定位的 AdCode
        requestGeo.setAd_code("");
        return requestGeo;
    }

    private LiYueRequestUser convertUser(RequestUserDto userDto) {
        LiYueRequestUser requestUser = new LiYueRequestUser();
        requestUser.setId(StringUtils.isNotEmpty(userDto.getUserId()) ? userDto.getUserId() : "");
        if (StringUtils.isNotEmpty(userDto.getGender())) {
            requestUser.setGender(userDto.getGender());
        } else {
            requestUser.setGender("");
        }
        requestUser.setAge(userDto.getAge());
        if (null != userDto.getInterest() && userDto.getInterest().length > 0) {
            requestUser.setTags(StringUtils.join(userDto.getInterest(), ","));
        } else {
            requestUser.setTags("");
        }
        return requestUser;
    }

    private String getIv() {
        byte[] iv = new byte[16];
        SecureRandom random = new SecureRandom();
        random.nextBytes(iv);
        return Hex.encodeHexString(iv);
    }

    public static String encryptData(String secretKey, String iv, String data) throws DecoderException {
        byte[] aesData = Aes.encrypt(data.getBytes(StandardCharsets.UTF_8), Hex.decodeHex(secretKey.toCharArray()), Hex.decodeHex(iv.toCharArray()), algorithm);
        return Hex.encodeHexString(aesData);
    }
}
