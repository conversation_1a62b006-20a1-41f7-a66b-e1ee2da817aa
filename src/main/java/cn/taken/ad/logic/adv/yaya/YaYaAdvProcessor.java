package cn.taken.ad.logic.adv.yaya;


import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.ActionType;
import cn.taken.ad.constant.business.EventType;
import cn.taken.ad.constant.business.MacroType;
import cn.taken.ad.constant.business.MaterialType;
import cn.taken.ad.constant.business.OsType;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.yaya.dto.BidRequest4;
import cn.taken.ad.logic.adv.yaya.dto.BidResponse4;
import cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType;
import cn.taken.ad.logic.adv.yaya.dto.ReqAdImage;
import cn.taken.ad.logic.adv.yaya.dto.ReqApp;
import cn.taken.ad.logic.adv.yaya.dto.ReqBanner;
import cn.taken.ad.logic.adv.yaya.dto.ReqDevice;
import cn.taken.ad.logic.adv.yaya.dto.ReqGeo;
import cn.taken.ad.logic.adv.yaya.dto.ReqImp;
import cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd;
import cn.taken.ad.logic.adv.yaya.dto.ReqSite;
import cn.taken.ad.logic.adv.yaya.dto.ReqUser;
import cn.taken.ad.logic.adv.yaya.dto.ReqVideo;
import cn.taken.ad.logic.adv.yaya.dto.ResImage;
import cn.taken.ad.logic.adv.yaya.dto.RespApp;
import cn.taken.ad.logic.adv.yaya.dto.RespBanner;
import cn.taken.ad.logic.adv.yaya.dto.RespNative;
import cn.taken.ad.logic.adv.yaya.dto.RespVideo;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestAppDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestCaidDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestDeviceDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestGeoDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestNetworkDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestTagDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestUserDto;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseMiniProgramDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.ParamParser;
import cn.taken.ad.utils.time.TimeUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component("YAYA" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class YaYaAdvProcessor implements AdvProcessor {
    private static final Logger log = LoggerFactory.getLogger(YaYaAdvProcessor.class);
    public static final String AES_KEY = "aesKey";


    //请求 imp调整
    //宏替换 调整
    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        BidRequest4.Builder builder = protobufRequest(rtbDto, advDto);
        BidRequest4 req = builder.build();
        advDto.setReqObj(req);
        byte[] reqBytes = req.toByteArray();
        // gzip 压缩json
        HttpResult httpResult = httpClient.postBytes(advDto.getRtburl(), reqBytes, new Header[]{new BasicHeader("Content-Type", "application/x-protobuf")}, advDto.getTimeout());

        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        String caid = req.getDevice().getCaid();
        return parseProtobubfResponse(rtbDto, httpResult, advDto,caid);
    }


    private BidRequest4.Builder protobufRequest(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        BidRequest4.Builder request = BidRequest4.newBuilder();
        Map<String, String> param = ParamParser.parseParamByJson(advDto.getPnyParam());
        if (StringUtils.isNotEmpty(rtbDto.getReqId())) {
            request.setId(rtbDto.getReqId());
        }
        request.setImp(builderProtobufImp(rtbDto, advDto));
        request.setSite(builderProtobufSite());
        request.setApp(builderProtobufApp(rtbDto, advDto));
        request.setDevice(builderProtobufDevice(rtbDto, advDto));
        request.setUser(builderProtobufUser(rtbDto, advDto));

        request.setTest(0);
        request.setMethod(0);

        return request;
    }


    private ReqImp.Builder builderProtobufImp(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        ReqImp.Builder imp = ReqImp.newBuilder();
        RequestTagDto tagDto = rtbDto.getTag();
        imp.setId(rtbDto.getReqId());
        boolean isNativeAd = false;
        if (null != tagDto.getTagType()) {
            if (tagDto.getTagType().getType() == 2 || tagDto.getTagType().getType() == 3 ||
                    tagDto.getTagType().getType() == 5 || tagDto.getTagType().getType() == 6 ||
                    tagDto.getTagType().getType() == 13 || tagDto.getTagType().getType() == 16) {

                ReqVideo.Builder video = ReqVideo.newBuilder();
                if (null != tagDto.getWidth()) {
                    video.setW(tagDto.getWidth());
                }
                if (null != tagDto.getHeight()) {
                    video.setH(tagDto.getHeight());
                }
                if (null != tagDto.getMaxDuration()) {
                    video.setMaxDuration(tagDto.getMaxDuration());
                }
                if (null != tagDto.getMinDuration()) {
                    video.setMaxDuration(tagDto.getMinDuration());
                }
                video.setMines("video/mp4");
                if (tagDto.getTagType().getType() == 2) {
                    video.setVideoType(1);
                } else {
                    video.setVideoType(0);
                }
                imp.setVideo(video);
                imp.setIsNativeAd(1);
            } else if (tagDto.getTagType().getType() == 1) {
                ReqNativeAd.Builder nativeAd = ReqNativeAd.newBuilder();
                ReqAdImage.Builder adImage = ReqAdImage.newBuilder();
                if (null != tagDto.getWidth()) {
                    adImage.setW(tagDto.getWidth());
                }
                if (null != tagDto.getHeight()) {
                    adImage.setH(tagDto.getHeight());
                }

                adImage.setIndex(1);
                nativeAd.addImages(adImage);
                nativeAd.setIsSupportVideo(1);
                imp.setNativeAd(nativeAd);
                isNativeAd = true;
            } else {
                ReqBanner.Builder banner = ReqBanner.newBuilder();
                if (null != tagDto.getWidth()) {
                    banner.setW(tagDto.getWidth());
                }
                if (null != tagDto.getHeight()) {
                    banner.setH(tagDto.getHeight());
                }
                if (tagDto.getTagType().getType() == 4) {
                    banner.setType(3);
                } else if (tagDto.getTagType().getType() == 8) {
                    banner.setType(6);
                } else if (tagDto.getTagType().getType() == 7) {
                    banner.setType(1);
                }
                banner.setMines("img");
                banner.setIsSupportVideo(1);
                imp.setBanner(banner);
            }
        } else {
            ReqBanner.Builder banner = ReqBanner.newBuilder();
            if (null != tagDto.getWidth()) {
                banner.setW(tagDto.getWidth());
            }
            if (null != tagDto.getHeight()) {
                banner.setH(tagDto.getHeight());
            }
            banner.setMines("img");
            imp.setBanner(banner);
        }
        if (null != advDto.getTagCode()) {
            imp.setTagid(advDto.getTagCode());
        }

        if (null != tagDto.getPrice()) {
            imp.setBidfloor(tagDto.getPrice().intValue());
        }
        if (null != tagDto.getNeedHttps()) {
            if (tagDto.getNeedHttps()) {
                imp.setIsSupportHttps(MobReqSupportHttpsType.MobReqSupportHttpsType_Https);
            } else {
                imp.setIsSupportHttps(MobReqSupportHttpsType.MobReqSupportHttpsType_Default);
            }
        }
        if (isNativeAd) {
            imp.setIsNativeAd(1);
        } else {
            imp.setIsNativeAd(0);
        }
        imp.setIsDeeplink(1);
        return imp;
    }

    private ReqSite.Builder builderProtobufSite() {
        ReqSite.Builder site = ReqSite.newBuilder();
        return site;
    }

    private ReqApp.Builder builderProtobufApp(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        ReqApp.Builder app = ReqApp.newBuilder();
        RequestAppDto appDto = rtbDto.getApp();
        if (StringUtils.isNotEmpty(advDto.getAppCode())) {
            app.setId(advDto.getAppCode());
        }
        if (StringUtils.isNotEmpty(appDto.getAppName())) {
            app.setName(appDto.getAppName());
        }
        if (StringUtils.isNotEmpty(appDto.getBundle())) {
            app.setBundle(appDto.getBundle());
        }
        if (StringUtils.isNotEmpty(appDto.getAppVersion())) {
            app.setVersion(appDto.getAppVersion());
        }
        return app;
    }

    private ReqDevice.Builder builderProtobufDevice(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        ReqDevice.Builder device = ReqDevice.newBuilder();
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        RequestNetworkDto networkDto = rtbDto.getNetwork();


        if (StringUtils.isNotEmpty(deviceDto.getUserAgent())) {
            device.setUa(deviceDto.getUserAgent());
        }
        device.setGeo(builderProtobufGeo(rtbDto));

        if (StringUtils.isNotEmpty(networkDto.getIp())) {
            device.setIp(networkDto.getIp());
        }
        if (StringUtils.isNotEmpty(networkDto.getIpv6())) {
            device.setIpv6(networkDto.getIpv6());
        }
        if (null != deviceDto.getDeviceType()) {
            if (deviceDto.getDeviceType().getType() == 1) {
                device.setDeviceTypeValue(3);
            } else if (deviceDto.getDeviceType().getType() == 2) {
                device.setDeviceTypeValue(2);
            } else if (deviceDto.getDeviceType().getType() == 3) {
                device.setDeviceTypeValue(1);
            } else if (deviceDto.getDeviceType().getType() == 4) {
                device.setDeviceTypeValue(4);
            } else {
                device.setDeviceTypeValue(0);
            }
        }
        if (StringUtils.isNotEmpty(deviceDto.getBrand())) {
            device.setMake(deviceDto.getBrand());
        }
        if (StringUtils.isNotEmpty(deviceDto.getModel())) {
            device.setMode(deviceDto.getModel());
        }
        if (null != deviceDto.getOsType()) {
            if (deviceDto.getOsType() == OsType.ANDROID) {
                device.setOsValue(2);
            } else if (deviceDto.getOsType() == OsType.IOS ) {
                device.setOsValue(1);
            } else if (deviceDto.getOsType() == OsType.WINDOWS_PC) {
                device.setOsValue(3);
            }
        }
        if (StringUtils.isNotEmpty(deviceDto.getOsVersion())) {
            device.setOsv(deviceDto.getOsVersion());
        }
        if (null != networkDto.getCarrierType()) {
            if (networkDto.getCarrierType().getType() == 1) {
                device.setCarrierValue(46000);
            } else if (networkDto.getCarrierType().getType() == 2) {
                device.setCarrierValue(46001);
            } else if (networkDto.getCarrierType().getType() == 3) {
                device.setCarrierValue(46003);
            }
        }

        if (null != networkDto.getConnectType()) {
            if (networkDto.getConnectType().getType() == 2) {
                device.setConnectionTypeValue(1);
            } else if (networkDto.getConnectType().getType() == 4) {
                device.setConnectionTypeValue(2);
            } else if (networkDto.getConnectType().getType() == 5) {
                device.setConnectionTypeValue(3);
            } else if (networkDto.getConnectType().getType() == 6) {
                device.setConnectionTypeValue(4);
            } else if (networkDto.getConnectType().getType() == 7) {
                device.setConnectionTypeValue(5);
            } else {
                device.setConnectionTypeValue(0);
            }
        }


        if (StringUtils.isNotEmpty(deviceDto.getIdfa())) {
            device.setIfa(deviceDto.getIdfa());
        } else {
            if (StringUtils.isNotEmpty(deviceDto.getImei())) {
                device.setIfa(deviceDto.getImei());
            }
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfv())) {
            device.setIdfv(deviceDto.getIdfv());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidIdMd5())) {
            device.setAdidmd5(deviceDto.getAndroidIdMd5());
        }
        if (StringUtils.isNotEmpty(networkDto.getMac())) {
            device.setMac(networkDto.getMac());
        }
        if (StringUtils.isNotEmpty(networkDto.getMacMd5())) {
            device.setMacmd5(networkDto.getMacMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidId())) {
            device.setAdid(deviceDto.getAndroidId());
        }

        if (null != deviceDto.getWidth()) {
            device.setW(deviceDto.getWidth());
        }
        if (null != deviceDto.getHeight()) {
            device.setH(deviceDto.getHeight());
        }
        if (null != deviceDto.getWidth()) {
            device.setAsw(deviceDto.getWidth());
        }
        if (null != deviceDto.getHeight()) {
            device.setAsh(deviceDto.getHeight());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaid())) {
            device.setOaid(deviceDto.getOaid());
        }
        if (StringUtils.isNotEmpty(deviceDto.getBrand())) {
            device.setBrand(deviceDto.getBrand());
        }
        if (null != deviceDto.getPpi()) {
            device.setPpi(deviceDto.getPpi());
        }


        RequestCaidDto caidDto = null;
        List<RequestCaidDto> caidDtos = deviceDto.getCaids();
        if (caidDtos != null && !caidDtos.isEmpty()) {
            for (RequestCaidDto c : caidDtos) {
                if (StringUtils.isNotEmpty(c.getCaid())) {
                    caidDto = c;
                    break;
                }
            }
        }
        if (caidDto != null) {
            if (StringUtils.isNotEmpty(caidDto.getCaid())) {
                device.setCaid(caidDto.getCaid());
            }
            if (StringUtils.isNotEmpty(caidDto.getVersion())) {
                device.setCaidVersion(caidDto.getVersion());
            }

        }
        if (StringUtils.isNotEmpty(deviceDto.getBootMark())) {
            device.setBootMark(deviceDto.getBootMark());
        }
        if (StringUtils.isNotEmpty(deviceDto.getUpdateMark())) {
            device.setUpdateMark(deviceDto.getUpdateMark());
        }
        if (StringUtils.isNotEmpty(deviceDto.getRomVersion())) {
            device.setRomVersion(deviceDto.getRomVersion());
        }
        Long sysComTime = TimeUtils.convertMilliSecond(deviceDto.getSysCompileTime());
        if (null != sysComTime) {
            device.setSysCompileTs(sysComTime);
        }

        if (StringUtils.isNotEmpty(deviceDto.getHmsVersion())) {
            device.setHmscore(deviceDto.getHmsVersion());
        }
        if (StringUtils.isNotEmpty(deviceDto.getPaid())) {
            device.setPaid(deviceDto.getPaid());
        }
        if (StringUtils.isNotEmpty(networkDto.getSsid())) {
            device.setBssid(networkDto.getSsid());
        }

        if (null != deviceDto.getOrientation()) {
            if (deviceDto.getOrientation().getType() == 1) {
                device.setScreenType(2);
            } else if (deviceDto.getOrientation().getType() == 2) {
                device.setScreenType(1);
            } else if (deviceDto.getOrientation().getType() == 999) {
                device.setScreenType(0);
            }
        }

        if (!CollectionUtils.isEmpty(deviceDto.getInstalledAppInfo())) {
            deviceDto.getInstalledAppInfo().forEach(v -> {
                if (StringUtils.isNotBlank(v.getPackageName())) {
                    device.addCheckedApps(v.getPackageName());
                }
            });
        }
        if (StringUtils.isNotEmpty(deviceDto.getHmsAgVersion())) {
            device.setHmsVer(deviceDto.getHmsAgVersion());
        }
        if (StringUtils.isNotEmpty(deviceDto.getHmsAgVersion())) {
            device.setHwagVer(deviceDto.getHmsAgVersion());
        }
        if (StringUtils.isNotEmpty(deviceDto.getDeviceName())) {
            device.setDeviceName(deviceDto.getDeviceName());
        }
        if (StringUtils.isNotEmpty(deviceDto.getTimeZone())) {
            device.setTimeZone(deviceDto.getTimeZone());
        }
        if (null != deviceDto.getDeviceMemory()) {
            device.setMemorySize(deviceDto.getDeviceMemory() / 1024);
        }
        if (null != deviceDto.getDeviceHardDisk()) {
            device.setHardDiskSize(deviceDto.getDeviceHardDisk() / 1024);
        }
        if (StringUtils.isNotEmpty(deviceDto.getAppStoreVersion())) {
            device.setAppStoreVer(deviceDto.getAppStoreVersion());
        }
        if (null != deviceDto.getApiLevel()) {
            device.setApiLevel(deviceDto.getApiLevel() + "");
        }
        if (StringUtils.isNotEmpty(networkDto.getWifiMac())) {
            device.setWifiMac(networkDto.getWifiMac());
        }
        Long startTime = TimeUtils.convertMilliSecond(deviceDto.getSysStartTime());
        if (null != startTime) {
            String time = TimeUtils.formatUnixTimeWithPrecision(startTime, false);
            if (StringUtils.isNotEmpty(time)) {
                device.setStartupTime(time);
            }
        }
        if (StringUtils.isNotEmpty(deviceDto.getSysInitTime())) {
            device.setBootTime(deviceDto.getSysInitTime());
        }
        Long updateTime = TimeUtils.convertMilliSecond(deviceDto.getSysUpdateTime());
        if (null != updateTime) {
            String time = TimeUtils.formatUnixTimeWithPrecision(updateTime, false);
            if (StringUtils.isNotEmpty(time)) {
                device.setUpdateTime(time);
            }
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfaMd5())) {
            device.setIdfaMd5(deviceDto.getIdfaMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaidMd5())) {
            device.setOaidMd5(deviceDto.getOaidMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAaid())) {
            device.setAliAaid(deviceDto.getAaid());
        }

        if (StringUtils.isNotEmpty(deviceDto.getSysUiVersion())) {
            device.setMiuiVersion(deviceDto.getSysUiVersion());
        }
        if (null != deviceDto.getCpuFreq()) {
            device.setCpuFreq(deviceDto.getCpuFreq().floatValue());
        }
        if (null != deviceDto.getCpuNum()) {
            device.setCpuNumber(deviceDto.getCpuNum());
        }
        if (null != deviceDto.getBatteryStatus()) {
            device.setBatteryStatus(deviceDto.getBatteryStatus());
        }
        if (null != deviceDto.getBatteryPower()) {
            device.setBatteryPower(deviceDto.getBatteryPower());
        }
        if (StringUtils.isNotBlank(deviceDto.getCountry())) {
            device.setCon(deviceDto.getCountry());
        }
        if (StringUtils.isNotBlank(deviceDto.getLanguage())) {
            device.setLan(deviceDto.getLanguage());
        }
        if (StringUtils.isNotEmpty(deviceDto.getHardwareModel())) {
            device.setHardwareModel(deviceDto.getHardwareModel());
        }
        if (null != deviceDto.getIdfaPolicy()) {
            if (deviceDto.getIdfaPolicy() == 1) {
                device.setAuthStatus(0);
            } else if (deviceDto.getIdfaPolicy() == 2) {
                device.setAuthStatus(1);
            } else if (deviceDto.getIdfaPolicy() == 3) {
                device.setAuthStatus(2);
            } else if (deviceDto.getIdfaPolicy() == 4) {
                device.setAuthStatus(3);
            }
        }

        return device;
    }

    private ReqGeo.Builder builderProtobufGeo(RtbRequestDto rtbDto) {
        ReqGeo.Builder geo = ReqGeo.newBuilder();

        RequestGeoDto geoDto = rtbDto.getGeo();
        if (null != geoDto.getLatitude()) {
            geo.setLat(geoDto.getLatitude().floatValue());
        }
        if (null != geoDto.getLongitude()) {
            geo.setLon(geoDto.getLongitude().floatValue());
        }
        if (null != geoDto.getCoordinateType()) {
            if (geoDto.getCoordinateType().getType() == 1) {
                geo.setGpsTypeValue(1);
            } else if (geoDto.getCoordinateType().getType() == 2) {
                geo.setGpsTypeValue(3);
            } else if (geoDto.getCoordinateType().getType() == 3) {
                geo.setGpsTypeValue(4);
            }
        }
        if (StringUtils.isNotBlank(rtbDto.getDevice().getCountry())) {
            geo.setCountry(rtbDto.getDevice().getCountry());
        }
        return geo;
    }

    private ReqUser.Builder builderProtobufUser(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        ReqUser.Builder user = ReqUser.newBuilder();

        RequestUserDto userDto = rtbDto.getUser();

        if (StringUtils.isNotEmpty(userDto.getUserId())) {
            user.setUserId(userDto.getUserId());
        }

        if (null != userDto.getInterest() && userDto.getInterest().length > 0) {
            user.setTags(JsonHelper.toJsonString(userDto.getInterest()));
        }
        if (StringUtils.isNotEmpty(userDto.getGender())) {
            if (userDto.getGender().equals("M")) {
                user.setGenderValue(1);
            } else if (userDto.getGender().equals("F")) {
                user.setGenderValue(2);
            } else {
                user.setGenderValue(3);
            }
        }
        if (null != userDto.getAge()) {
            user.setAge(userDto.getAge());
        }
        return user;
    }


    public RtbResponseDto parseProtobubfResponse(RtbRequestDto rtbDto, HttpResult httpResult, RtbAdvDto advDto,String caid) throws Exception {
        Map<String, String> param = ParamParser.parseParamByJson(advDto.getPnyParam());
        String key = param.get(AES_KEY);

        byte[] data = httpResult.getData();
        if (null == data || data.length == 0) {
            log.info("RtbId:{} Req Adv:{} Resp Empty ", rtbDto.getReqId(), advDto.getTagCode());
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Empty resp");
        }
        BidResponse4 response = BidResponse4.parseFrom(data);
        advDto.setRespObj(response);
        if (response.getSeatBid().getBidList().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充", "");
        }
        List<TagResponseDto> tags = new ArrayList<>();
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", "", tags);
        responseDto.setRespId(response.getBidId());
        response.getSeatBid().getBidList().forEach(tag -> {
            TagResponseDto tagResponseDto = new TagResponseDto();
            String price = String.valueOf(tag.getPrice());

            String encryPrice = YaAes.aesEncryptTest(price, key);

            if (tag.getPrice() > 0) {
                tagResponseDto.setPrice((double) tag.getPrice());
            }
            if (StringUtils.isNotEmpty(tag.getImpId())) {
                tagResponseDto.setTagInfoId(tag.getImpId());
            }
            if (StringUtils.isNotEmpty(tag.getNoticeUrl())) {
                tagResponseDto.setWinNoticeUrls(new ArrayList<>(Collections.singletonList(tag.getNoticeUrl())));
            }

            if (tag.hasBanner()) {
                RespBanner banner = tag.getBanner();
                if (StringUtils.isNotEmpty(banner.getImageUrl())) {
                    tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                    tagResponseDto.setImgUrls(new ArrayList<>(Collections.singletonList(banner.getImageUrl())));
                }
                if (banner.getW() > 0) {
                    tagResponseDto.setMaterialWidth(banner.getW());
                }
                if (banner.getH() > 0) {
                    tagResponseDto.setMaterialHeight(banner.getH());
                }

                if (StringUtils.isNotEmpty(banner.getHtml())) {
                    tagResponseDto.setMaterialType(MaterialType.HTML);
                    tagResponseDto.setHtmlContent(banner.getHtml());
                }

                if (StringUtils.isNotEmpty(banner.getVideoUrl())) {
                    ResponseVideoDto video = new ResponseVideoDto();
                    video.setVideoUrl(banner.getVideoUrl());
                    if (banner.getVideoDuration() > 0){
                        video.setDuration(banner.getVideoDuration());
                    }
                    if (StringUtils.isNotEmpty(banner.getCover())) {
                        video.setCoverImgUrls(Collections.singletonList(banner.getCover()));
                    }
                    tagResponseDto.setVideoInfo(video);
                }
            }

            if (tag.hasNativeAd()) {
                RespNative nativeAd = tag.getNativeAd();
                if (StringUtils.isNotEmpty(nativeAd.getTitle())) {
                    tagResponseDto.setTitle(nativeAd.getTitle());
                }
                if (StringUtils.isNotEmpty(nativeAd.getDesc())) {
                    tagResponseDto.setDesc(nativeAd.getDesc());
                }
                if (StringUtils.isNotEmpty(nativeAd.getDesc())) {
                    tagResponseDto.setDesc(nativeAd.getDesc());
                }
                if (!nativeAd.getImagesList().isEmpty()) {
                    List<String> list = new ArrayList<>();
                    Integer width = null;
                    Integer height = null;
                    for (ResImage resImage : nativeAd.getImagesList()) {
                        if (StringUtils.isNotBlank(resImage.getImageUrl())){
                            list.add(resImage.getImageUrl());
                        }
                        if (null == width && resImage.getW() > 0) {
                            width = resImage.getW();
                        }
                        if (null == height && resImage.getH() > 0) {
                            height = resImage.getH();
                        }
                    }
                    if (!list.isEmpty()){
                        tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                        tagResponseDto.setImgUrls(list);
                        tagResponseDto.setMaterialWidth(width);
                        tagResponseDto.setMaterialHeight(height);
                    }
                }

                if (StringUtils.isNotEmpty(nativeAd.getVideoUrl())) {
                    tagResponseDto.setMaterialType(MaterialType.VIDEO);
                    ResponseVideoDto video = new ResponseVideoDto();
                    video.setVideoUrl(nativeAd.getVideoUrl());
                    if (nativeAd.getVideoDuration() > 0){
                        video.setDuration(nativeAd.getVideoDuration());
                    }
                    if (StringUtils.isNotEmpty(nativeAd.getCover())) {
                        video.setCoverImgUrls(Collections.singletonList(nativeAd.getCover()));
                    }
                    tagResponseDto.setVideoInfo(video);
                }
            }
            if (tag.hasVideo()) {
                RespVideo video = tag.getVideo();
                if (StringUtils.isNotEmpty(video.getUrl())) {
                    tagResponseDto.setMaterialType(MaterialType.VIDEO);
                    ResponseVideoDto videoDto = new ResponseVideoDto();
                    videoDto.setVideoUrl(video.getUrl());
                    if (video.getDuration() > 0){
                        videoDto.setDuration(video.getDuration());
                    }
                    if (StringUtils.isNotEmpty(video.getAfterImage())) {
                        videoDto.setEndImgUrls(Collections.singletonList(video.getAfterImage()));
                    }
                    if (StringUtils.isNotEmpty(video.getAfterHtml())) {
                        videoDto.setEndHtml(video.getAfterHtml());
                    }
                    if (StringUtils.isNotEmpty(video.getTitle())) {
                        tagResponseDto.setTitle(video.getTitle());
                    }
                    if (StringUtils.isNotEmpty(video.getDesc())) {
                        tagResponseDto.setDesc(video.getDesc());
                    }
                    tagResponseDto.setVideoInfo(videoDto);
                }
            }
            if (tag.hasApp()) {
                RespApp app = tag.getApp();
                ResponseAppDto appInfo = new ResponseAppDto();
                if (StringUtils.isNotEmpty(app.getName())) {
                    appInfo.setAppName(app.getName());
                }
                if (StringUtils.isNotEmpty(app.getBundle())) {
                    appInfo.setPackageName(app.getBundle());
                }
                if (StringUtils.isNotEmpty(app.getIcon())) {
                    appInfo.setAppIconUrl(app.getIcon());
                }
                if (StringUtils.isNotEmpty(app.getVer())) {
                    appInfo.setAppVersion(app.getVer());
                }
                if (StringUtils.isNotEmpty(app.getDevelopName())) {
                    appInfo.setAppDeveloper(app.getDevelopName());
                }
                if (StringUtils.isNotEmpty(app.getDevelopName())) {
                    appInfo.setAppDeveloper(app.getDevelopName());
                }
                if (!app.getAppPermissionsList().isEmpty()) {
                    String content = app.getAppPermissionsList().get(0).getContent();
                    appInfo.setAppPermContent(content);
                }
                if (StringUtils.isNotEmpty(app.getPrivatePolicy())) {
                    appInfo.setAppPrivacyUrl(app.getPrivatePolicy());
                }
                if (StringUtils.isNotEmpty(app.getDownloadUrl())) {
                    tagResponseDto.setClickUrl(app.getDownloadUrl());
                }
                if (StringUtils.isNotEmpty(app.getPermissionsLink())) {
                    appInfo.setAppPermissionInfoUrl(app.getPermissionsLink());
                }
                if (StringUtils.isNotEmpty(app.getAppDesc())) {
                    appInfo.setAppInfo(app.getAppDesc());
                }
                if (app.getSize() > 0){
                    appInfo.setAppSize(app.getSize() * 1024L);
                }
                tagResponseDto.setAppInfo(appInfo);
            }

            if (tag.getClkTypeValue() == 0) {
                tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
            } else if (tag.getClkTypeValue() == 1) {
                tagResponseDto.setActionType(ActionType.DOWNLOAD);
            }

            if (StringUtils.isNotEmpty(tag.getLanding())) {
                tagResponseDto.setClickUrl(tag.getLanding());
            }

            if (StringUtils.isNotEmpty(tag.getSourceLogo())) {
                tagResponseDto.setLogoUrl(tag.getSourceLogo());
            }
            if (StringUtils.isNotEmpty(tag.getDpLink())) {
                tagResponseDto.setDeepLinkUrl(tag.getDpLink());
            }


            if (StringUtils.isNotEmpty(tag.getMiniId()) && StringUtils.isNotBlank(tag.getMiniPath())) {
                ResponseMiniProgramDto miniProgram = new ResponseMiniProgramDto();
                miniProgram.setId(tag.getMiniId());
                miniProgram.setPath(tag.getMiniPath());
                tagResponseDto.setMiniProgram(miniProgram);
                tagResponseDto.setActionType(ActionType.MINI_PROGRAM);
            }
            if (StringUtils.isNotEmpty(tag.getMarketUrl())) {
                tagResponseDto.setMarketUrl(tag.getMarketUrl());
            }
            if (StringUtils.isNotEmpty(tag.getUcClickAreaUrl())) {
                tagResponseDto.setClickAreaReportUrls(Collections.singletonList(tag.getUcClickAreaUrl()));
            }

            List<ResponseTrackDto> tracks = new ArrayList<>();
            tagResponseDto.setTracks(tracks);

            RespApp appTrack = tag.getApp();
            RespVideo videoTrack = tag.getVideo();

            if (!tag.getImpList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), new ArrayList<>(tag.getImpList())));
            }
            if (!tag.getClkList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), new ArrayList<>(tag.getClkList())));
            }
            if (!tag.getDpTracksList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), new ArrayList<>(tag.getDpTracksList())));
            }
            if (!tag.getDpTryTracksList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_START.getType(), new ArrayList<>(tag.getDpTryTracksList())));
            }
            if (!tag.getDpFTracksList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), new ArrayList<>(tag.getDpFTracksList())));
            }
            if (!appTrack.getDownloadStartTrackList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), new ArrayList<>(appTrack.getDownloadStartTrackList())));
            }
            if (!appTrack.getDownloadEndTrackList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), new ArrayList<>(appTrack.getDownloadEndTrackList())));
            }
            if (!appTrack.getInstallStartTrackList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), new ArrayList<>(appTrack.getInstallStartTrackList())));
            }
            if (!appTrack.getInstallEndTrackList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), new ArrayList<>(appTrack.getInstallEndTrackList())));
            }

            if (!videoTrack.getTracksList().isEmpty()) {
                videoTrack.getTracksList().forEach(v -> {
                    if (v.getPos() == 0) {
                        tracks.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), new ArrayList<>(v.getTracksList())));
                    }
                    if (v.getPos() == -1) {
                        tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), new ArrayList<>(v.getTracksList())));
                    }
                });
            }
            if (!videoTrack.getSkipTracksList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.VIDEO_SKIP.getType(), new ArrayList<>(videoTrack.getSkipTracksList())));
            }
            if (!videoTrack.getPauseTracksList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), new ArrayList<>(videoTrack.getPauseTracksList())));
            }
            if (!videoTrack.getStopTracksList().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.VIDEO_CLOSE.getType(), new ArrayList<>(videoTrack.getStopTracksList())));
            }

            if (null != advDto.getTagId()) {
                List<String> winUrls = tagResponseDto.getWinNoticeUrls();
                if (null != winUrls && !winUrls.isEmpty()) {
                    replaceMacro("__bid_id__", winUrls, rtbDto.getReqId());
                }
            }

            tagResponseDto.getTracks().forEach(track -> {
                List<String> urls = track.getTrackUrls();
                urls = replaceMacro("__bid_id__", urls, rtbDto.getReqId());
                if (StringUtils.isNotBlank(encryPrice)) {
                    urls = replaceMacro("__bid_price__", urls, encryPrice);
                }
                urls = replaceMacro("__clk_x__", urls, MacroType.DOWN_X.getCode());
                urls = replaceMacro("__clk_y__", urls, MacroType.DOWN_Y.getCode());

                urls = replaceMacro("__clk_up_x__", urls, MacroType.UP_X.getCode());
                urls = replaceMacro("__clk_up_y__", urls, MacroType.UP_Y.getCode());

                urls = replaceMacro("__clk_abs_x__", urls, MacroType.ABS_DOWN_X.getCode());
                urls = replaceMacro("__clk_abs_y__", urls, MacroType.ABS_DOWN_Y.getCode());

                urls = replaceMacro("__clk_up_abs_x__", urls, MacroType.ABS_UP_X.getCode());
                urls = replaceMacro("__clk_up_abs_y__", urls, MacroType.ABS_UP_Y.getCode());

                urls = replaceMacro("__DP_WIDTH__", urls, MacroType.DP_WIDTH.getCode());
                urls = replaceMacro("__DP_HEIGHT__", urls, MacroType.DP_HEIGHT.getCode());

                urls = replaceMacro("__DP_DOWN_X__", urls, MacroType.DP_DOWN_X.getCode());
                urls = replaceMacro("__DP_HEIGHT__", urls, MacroType.DP_DOWN_Y.getCode());

                urls = replaceMacro("__DP_UP_X__", urls, MacroType.DP_UP_X.getCode());
                urls = replaceMacro("__DP_UP_Y__", urls, MacroType.DP_UP_Y.getCode());

                urls = replaceMacro("__clk_time__", urls, MacroType.TIME.getCode());
                urls = replaceMacro("__clk_time_s__", urls, MacroType.TIME_SECONDS.getCode());

                urls = replaceMacro("__gdt_click_id__", urls, MacroType.CLICK_ID.getCode());

                urls = replaceMacro("__WIDTH__", urls, MacroType.WIDTH.getCode());
                urls = replaceMacro("__HEIGHT__", urls, MacroType.HEIGHT.getCode());

                urls = replaceMacro("__EVENT_TIME_START__", urls, MacroType.START_TIME.getCode());
                urls = replaceMacro("__EVENT_TIME_END__", urls, MacroType.END_TIME.getCode());

                urls = replaceMacro("__IP__", urls, MacroType.IP.getCode());
                urls = replaceMacro("__UA__", urls, MacroType.UA.getCode());
                if (StringUtils.isNotBlank(rtbDto.getDevice().getImei())){
                    urls = replaceMacro("__IMEI__", urls, rtbDto.getDevice().getImei());
                }
                if (StringUtils.isNotBlank(rtbDto.getDevice().getOaid())){
                    urls = replaceMacro("__OAID__", urls, rtbDto.getDevice().getOaid());
                }
                if (StringUtils.isNotBlank(rtbDto.getDevice().getIdfa())){
                    urls = replaceMacro("__IDFA__", urls, rtbDto.getDevice().getIdfa());
                }
                if (StringUtils.isNotBlank(caid)){
                    urls = replaceMacro("__CAID__", urls, caid);
                }
                if (StringUtils.isNotBlank(rtbDto.getNetwork().getMac())){
                    urls = replaceMacro("__MAC__", urls, rtbDto.getNetwork().getMac());
                }


                urls = replaceMacro("__VIDEO_TIME__", urls, MacroType.VIDEO_TIME.getCode());
                urls = replaceMacro("__BEGIN_TIME__", urls, MacroType.VIDEO_BEGIN_TIME.getCode());

                urls = replaceMacro("__END_TIME__", urls, MacroType.VIDEO_END_TIME.getCode());

                urls = replaceMacro("__PLAY_FIRST_FRAME__", urls, MacroType.VIDEO_PLAY_FIRST_FRAME.getCode());
                urls = replaceMacro("__PLAY_LAST_FRAME__", urls, MacroType.VIDEO_PLAY_LAST_FRAME.getCode());

                urls = replaceMacro("__SCENE__", urls, MacroType.VIDEO_SCENE.getCode());

                urls = replaceMacro("__TYPE__", urls, MacroType.VIDEO_TYPE.getCode());

                urls = replaceMacro("__BEAVIOR__", urls, MacroType.VIDEO_BEHAVIOR.getCode());

                urls = replaceMacro("__STATUS__", urls, MacroType.VIDEO_STATUS.getCode());

                track.setTrackUrls(urls);
            });

            tags.add(tagResponseDto);
        });
        return responseDto;

    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam reqDto) throws Exception {
        // 是否有请求成功的
        boolean hasRight = false;
        List<String> urls = reqDto.getUrls();
        if (reqDto.getBiddingSuccess()) {
            if (null != reqDto.getPrice()) {
                String key = ParamParser.parseParamByJson(reqDto.getAdvCustomParam()).get(AES_KEY);
                String encryptPrice = YaAes.aesEncryptTest(String.valueOf(reqDto.getPrice()), key);
                urls = replaceMacro("__bid_price__", urls, encryptPrice);
            }
        }

        for (String url : urls) {
            HttpResult result = httpClient.get(url, null, null, -1);
            if (!hasRight) {
                hasRight = result.isSuccess();
            }
            //log.info("Req Adv Bill ReqId:{}, Result:{},HttpCode:{},Header:{},ErrMsg:{},url:{}", reqDto.getRtbId(), result.isSuccess(), result.getStatusLine(), Arrays.toString(result.getHeaders()), null != result.getThrowable() ? result.getThrowable().getMessage() : "", url);
        }
        return hasRight ? SuperResult.rightResult() : SuperResult.badResult("no right");
    }
}
