// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: RequestApp.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.jiuwei.v2.dto.request.app;

public final class TakenReqApp {
  private TakenReqApp() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      TakenReqApp.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_req_TakenRequestApp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_req_TakenRequestApp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\020RequestApp.proto\022\003req\"\230\001\n\017TakenRequest" +
      "App\022\r\n\005appId\030\001 \001(\t\022\022\n\nappVersion\030\002 \001(\t\022\016" +
      "\n\006bundle\030\003 \001(\t\022\017\n\007appName\030\004 \001(\t\022\026\n\016appVe" +
      "rsionCode\030\005 \001(\t\022\023\n\013appstoreUrl\030\006 \001(\t\022\024\n\014" +
      "appDomainUrl\030\007 \001(\tBC\n2cn.taken.ad.logic." +
      "prossor.taken.v2.dto.request.appB\013TakenR" +
      "eqAppP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_req_TakenRequestApp_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_req_TakenRequestApp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_req_TakenRequestApp_descriptor,
        new String[] { "AppId", "AppVersion", "Bundle", "AppName", "AppVersionCode", "AppstoreUrl", "AppDomainUrl", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
