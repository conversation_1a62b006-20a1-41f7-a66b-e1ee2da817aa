package cn.taken.ad.constant.redis;

public class QueueRedisKeys {


    /*----统计队列------*/
    public static final String QUEUE_STATISTICS_MEDIA_ADV_REQ = "QUEUE_STATISTICS_MEDIA_ADV_REQ";

    public static final String QUEUE_STATISTICS_MEDIA_REQ = "QUEUE_STATISTICS_MEDIA_REQ";

    public static final String QUEUE_STATISTICS_ADV_REQ = "QUEUE_STATISTICS_ADV_REQ";

    public static final String QUEUE_STATISTICS_MEDIA_ERROR_CODE = "QUEUE_STATISTICS_MEDIA_ERROR_CODE";

    public static final String QUEUE_STATISTICS_ADV_ERROR_CODE = "QUEUE_STATISTICS_ADV_ERROR_CODE";

    public static final String QUEUE_STATISTICS_MEDIA_ADV_EVENT = "QUEUE_STATISTICS_MEDIA_ADV_EVENT";

    public static final String QUEUE_STATISTICS_MEDIA_EVENT = "QUEUE_STATISTICS_MEDIA_EVENT";

    public static final String QUEUE_STATISTICS_ADV_EVENT = "QUEUE_STATISTICS_ADV_EVENT";

    /**
     * RTB接口日志队列
     */
    public static final String QUEUE_RTB_LOG = "QUEUE_RTB_LOG";

    /**
     * DSP业务
     */

    public static final String QUEUE_STATISTICS_DSP_ADV_AD_REQ = "QUEUE_STATISTICS_DSP_ADV_AD_REQ";

    public static final String QUEUE_STATISTICS_DSP_ADV_AD_EVENT = "QUEUE_STATISTICS_DSP_ADV_AD_EVENT";


    //分包广告位入库队列
    public static final String QUEUE_MEDIA_PACKAGE_TAG_SAVE = "QUEUE_MEDIA_PACKAGE_TAG_SAVE";
}
