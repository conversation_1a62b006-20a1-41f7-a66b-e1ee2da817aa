package cn.taken.ad.configuration.monitor;

import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.core.pojo.media.MediaTag;
import cn.taken.ad.core.pojo.strategy.Strategy;
import cn.taken.ad.logic.base.rtb.RtbDto;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class MediaPackageMonitor {
    private Map<Long, Map<String, String>> tempQueue = new ConcurrentHashMap<>();

    public void accept(Strategy strategy, MediaTag tag, RtbDto rtbDto) {
        if (strategy == null || strategy.getModel() == null || strategy.getModel() != 2) {
            return;
        }
        if (tag == null) {
            return;
        }
        if (rtbDto == null) {
            return;
        }
        if (rtbDto.getMediaDto() == null) {
            return;
        }
        if (rtbDto.getMediaDto().getRequest() == null) {
            return;
        }
        if (rtbDto.getMediaDto().getRequest().getApp() == null) {
            return;
        }
        String packName = rtbDto.getMediaDto().getRequest().getApp().getBundle();
        if (StringUtils.isBlank(packName)) {
            return;
        }
        tempQueue.computeIfAbsent(tag.getId(), k -> new ConcurrentHashMap<>()).put(packName, rtbDto.getMediaDto().getMediaId() + "_" + rtbDto.getMediaDto().getMediaAppId());
    }

    public Map<Long, Map<String, String>> getTempQueue() {
        return tempQueue;
    }

}
