package cn.taken.ad.configuration.monitor;

import cn.taken.ad.constant.business.EventType;
import cn.taken.ad.core.dto.business.statistics.MediaPackageAdvertiserReqMonitorDto;
import cn.taken.ad.core.pojo.media.MediaTag;
import cn.taken.ad.core.pojo.strategy.StrategyTagAdvertiser;
import cn.taken.ad.logic.base.bill.RtbBillDto;
import cn.taken.ad.logic.base.event.RtbEventDto;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import com.google.common.util.concurrent.AtomicDouble;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class RtbPackageMonitor {
    private Map<Long, Map<String, MediaPackageAdvertiserReqMonitorDto>> mediaAdvRequestMonitor = new ConcurrentHashMap<>();

    private Map<Long, Map<String, AtomicInteger>> mediaAdvWindMonitor = new ConcurrentHashMap<>();

    private final Map<Long, Map<String, AtomicInteger>> mediaAdvEventMonitor = new ConcurrentHashMap<>();

    private final Map<Long, Map<String, AtomicDouble>> mediaAdvEventAmountOfMediaMonitor = new ConcurrentHashMap<>();

    private final Map<Long, Map<String, AtomicDouble>> mediaAdvEventAmountOfAdvMonitor = new ConcurrentHashMap<>();


    public void monitorAdvRequest(StrategyTagAdvertiser adv, String bundle, MediaTag tag, int advReq, int advReqSuccess, int advReqFail, int advRespFail, int advReqTimeout, int participating, int use) {
        if (adv.getStrategyPackageId() == null) {
            return;
        }
        long second = System.currentTimeMillis() / 1000L;
        String key = adv.getStrategyId() + "-" + adv.getId() + "-" + tag.getMediaId() + "-" + tag.getMediaAppId() + "-" + tag.getId() + "-" + adv.getAdvertiserId() + "-" + adv.getAdvertiserAppId() + "-" + adv.getAdvertiserTagId() + +adv.getStrategyPackageId() + "-" + bundle;
        MediaPackageAdvertiserReqMonitorDto monitorDto = mediaAdvRequestMonitor.computeIfAbsent(second, k -> new ConcurrentHashMap<>()).computeIfAbsent(key, k -> new MediaPackageAdvertiserReqMonitorDto(
                adv.getStrategyId(),
                adv.getId(),
                tag.getMediaId(),
                tag.getMediaAppId(),
                tag.getId(),
                adv.getAdvertiserId(),
                adv.getAdvertiserAppId(),
                adv.getAdvertiserTagId(),
                adv.getStrategyPackageId(),
                bundle
        ));
        monitorDto.getAdvertiserReqTotal().getAndAdd(advReq);
        monitorDto.getAdvertiserReqSuccessTotal().getAndAdd(advReqSuccess);
        monitorDto.getAdvertiserReqFailTotal().getAndAdd(advReqFail);
        monitorDto.getAdvertiserRespFailTotal().getAndAdd(advRespFail);
        monitorDto.getAdvertiserReqTimeoutTotal().getAndAdd(advReqTimeout);
        monitorDto.getAdvertiserParticipatingTotal().getAndAdd(participating);
        monitorDto.getAdvertiserUseTimeTotal().getAndAdd(use);
        monitorDto.updateMinConcurrentValue(use);
        monitorDto.updateMaxConcurrentValue(use);
    }

    public void monitorMediaAdvRequest(Long strategyId, RtbAdvDto adv, String bundle, MediaTag tag, int mediaReq, int mediaInvalid, int mediaRespFail, int participating, int useTime) {
        if (adv.getStrategyPackageId() == null) {
            return;
        }
        long second = System.currentTimeMillis() / 1000L;
        String key = strategyId + "-" + adv.getStrategyTagAdvId() + "-" + tag.getMediaId() + "-" + tag.getMediaAppId() + "-" + tag.getId() + "-" + adv.getAdvertiserId() + "-" + adv.getAppId() + "-" + adv.getTagId() + "-" + adv.getStrategyPackageId() + "-" + bundle;
        MediaPackageAdvertiserReqMonitorDto monitorDto = mediaAdvRequestMonitor.computeIfAbsent(second, k -> new ConcurrentHashMap<>()).computeIfAbsent(key, k -> new MediaPackageAdvertiserReqMonitorDto(
                strategyId,
                adv.getStrategyTagAdvId(),
                tag.getMediaId(),
                tag.getMediaAppId(),
                tag.getId(),
                adv.getAdvertiserId(),
                adv.getAppId(),
                adv.getTagId(),
                adv.getStrategyPackageId(),
                bundle
        ));
        monitorDto.getMediaReqTotal().getAndAdd(mediaReq);
        monitorDto.getMediaReqInvalidTotal().getAndAdd(mediaInvalid);
        monitorDto.getMediaRespFailTotal().getAndAdd(mediaRespFail);
        monitorDto.getMediaParticipatingTotal().getAndAdd(participating);
        monitorDto.getMediaUseTimeTotal().getAndAdd(useTime);
        monitorDto.updateMediaMinConcurrentValue(useTime);
        monitorDto.updateMediaMaxConcurrentValue(useTime);
    }


    public void monitorBill(RtbBillDto request, boolean success) {
        long second = System.currentTimeMillis() / 1000L;
        String mediaAdvKey = request.getStrategyId() + "-" + request.getStrategyTagAdvId() + "-" + request.getMediaId() + "-" + request.getMediaAppId() + "-" + request.getMediaTagId() + "-" + request.getAdvertiserId() + "-" + request.getAdvertiserAppId() + "-" + request.getAdvertiserTagId() + "-" + request.getStrategyPackageId() + "-" + request.getMediaAppBundle();
        if (success) {
            mediaAdvWindMonitor.computeIfAbsent(second, k -> new ConcurrentHashMap<>()).computeIfAbsent(mediaAdvKey, k -> new AtomicInteger()).incrementAndGet();
        }
    }

    public void monitorRtbEvent(RtbEventDto eventDto, EventType eventType) {
        String mediaAdvKey = eventDto.getStrategyId() + "-" + eventDto.getStrategyTagAdvId() + "-" + eventDto.getMediaId() +
                "-" + eventDto.getMediaAppId() + "-" + eventDto.getMediaTagId() + "-" + eventDto.getAdvertiserId() + "-"
                + eventDto.getAdvertiserAppId() + "-" + eventDto.getAdvertiserTagId() + "-" + eventDto.getStrategyPackageId() + "-" + eventDto.getMediaAppBundle() + "-" + eventType.getType();
        long second = System.currentTimeMillis() / 1000L;
        mediaAdvEventMonitor.computeIfAbsent(second, k -> new ConcurrentHashMap<>()).computeIfAbsent(mediaAdvKey, k -> new AtomicInteger()).incrementAndGet();
        if (eventType == EventType.EXPOSURE) {
            //媒体预算消耗
            mediaAdvKey = mediaAdvKey.substring(0, mediaAdvKey.lastIndexOf("-"));
            // 20250414 使用平台自己记录的出价信息 计算媒体收入
            if (eventDto.getRespMediaPrice() != null) {
                mediaAdvEventAmountOfMediaMonitor.computeIfAbsent(second, k -> new ConcurrentHashMap<>()).computeIfAbsent(mediaAdvKey, k -> new AtomicDouble()).addAndGet(eventDto.getRespMediaPrice());
            }
            //预算消耗
            if (eventDto.getAdvPrice() != null) {
                mediaAdvEventAmountOfAdvMonitor.computeIfAbsent(second, k -> new ConcurrentHashMap<>()).computeIfAbsent(mediaAdvKey, k -> new AtomicDouble()).addAndGet(eventDto.getAdvPrice());
            }
        }
    }


    public Map<Long, Map<String, MediaPackageAdvertiserReqMonitorDto>> getMediaAdvRequestMonitor() {
        return mediaAdvRequestMonitor;
    }

    public Map<Long, Map<String, AtomicInteger>> getMediaAdvWindMonitor() {
        return mediaAdvWindMonitor;
    }

    public Map<Long, Map<String, AtomicInteger>> getMediaAdvEventMonitor() {
        return mediaAdvEventMonitor;
    }

    public Map<Long, Map<String, AtomicDouble>> getMediaAdvEventAmountOfMediaMonitor() {
        return mediaAdvEventAmountOfMediaMonitor;
    }

    public Map<Long, Map<String, AtomicDouble>> getMediaAdvEventAmountOfAdvMonitor() {
        return mediaAdvEventAmountOfAdvMonitor;
    }
}
