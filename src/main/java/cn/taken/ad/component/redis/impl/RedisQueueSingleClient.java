package cn.taken.ad.component.redis.impl;

import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.redis.conf.SingleRedisConf;
import cn.taken.ad.component.redis.executor.JedisBinaryCommandsExecutor;
import cn.taken.ad.component.redis.executor.JedisCommandsExecutor;
import cn.taken.ad.component.redis.executor.JedisPipelineCommandsExecutor;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.Pipeline;

import java.time.Duration;
import java.util.List;

public class RedisSingleClient implements RedisClient {

    private final JedisPool jedisPool;

    private final SingleRedisConf singleRedisConf;

    public RedisSingleClient(SingleRedisConf singleRedisConf) {
        this.singleRedisConf = singleRedisConf;
        GenericObjectPoolConfig<Jedis> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxIdle(singleRedisConf.getMaxIdle());
        poolConfig.setMaxTotal(singleRedisConf.getMaxTotal());
        poolConfig.setMinIdle(singleRedisConf.getMinIdle());
        poolConfig.setMaxWait(Duration.ofMillis(singleRedisConf.getMaxWaitMillis()));
        poolConfig.setTestWhileIdle(true);
        poolConfig.setNumTestsPerEvictionRun(-1);
        if (singleRedisConf.getPassword() != null && !singleRedisConf.getPassword().trim().isEmpty()) {
            jedisPool = new JedisPool(poolConfig, singleRedisConf.getHost(), singleRedisConf.getPort(), singleRedisConf.getConnectionTimeoutMillis(), singleRedisConf.getSoTimeoutMillis(), singleRedisConf.getPassword(), 0, null);
        } else {
            jedisPool = new JedisPool(poolConfig, singleRedisConf.getHost(), singleRedisConf.getPort(), singleRedisConf.getConnectionTimeoutMillis(), singleRedisConf.getSoTimeoutMillis(), null, 0, null);
        }
    }

    @Override
    public <T> T executeCommands(JedisCommandsExecutor<T> executor) {
        try (Jedis jedis = jedisPool.getResource()) {
            return executor.execute(jedis);
        }
    }

    @Override
    public <T> T executeBinaryCommands(JedisBinaryCommandsExecutor<T> executor) {
        try (Jedis jedis = jedisPool.getResource()) {
            return executor.execute(jedis);
        }
    }

    @Override
    public List<Object> executePipelineCommands(JedisPipelineCommandsExecutor pipelineCommand) {
        try (Jedis jedis = jedisPool.getResource();
             Pipeline pipeline = jedis.pipelined()
        ) {
            pipelineCommand.execute(pipeline);
            return pipelineCommand.getResult(pipeline);
        }
    }

    @Override
    public String getDatePattern() {
        return singleRedisConf.getDatePattern();
    }

    @Override
    public void close() {
        jedisPool.close();
    }

}
