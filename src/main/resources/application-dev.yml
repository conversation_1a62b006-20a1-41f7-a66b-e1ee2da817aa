# redis
redis:
  base:
    single:
      connectionTimeoutMillis: 10000
      soTimeoutMillis: 10000
      maxIdle: 8
      maxTotal: 32
      minIdle: 1
      maxWaitMillis: 10000
      datePattern: yyyy-MM-dd HH:mm:ss SSS
      password: "@@90!S0Q@vX@O@b3T3n5"
      host: ***************
      port: 6777
  queue:
    nodes:
      -
        connectionTimeoutMillis: 10000
        soTimeoutMillis: 10000
        maxIdle: 8
        maxTotal: 32
        minIdle: 1
        maxWaitMillis: 10000
        datePattern: yyyy-MM-dd HH:mm:ss SSS
        password: "@@90!S0Q@vX@O@b3T3n5"
        host: ***************
        port: 6777
        read: true
        write: true



# 数据库
# Hibernate packageScan mappingResources都可以逗号分隔传多个
db:
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************
    username: ssp
    password: "@@90!S0Q@vX@O@b3T3n5"
    maxActive: 32
    maxIdle: 8
    minIdle: 1
    initialSize: 1
    timeBetweenEvictionRunsMillis: 60000
    numTestsPerEvictionRun: 100
    logAbandoned: true
  hibernate:
    dialect: org.hibernate.dialect.MySQLInnoDBDialect
    hbm2ddl_auto: none
    show_sql: false
    format_sql: false
    jdbc_batchSize: 50
    enable_lazy_load_no_trans: false
    current_session_context_class: org.springframework.orm.hibernate5.SpringSessionContext
    packageScan: cn.taken.ad

# 定时任务
scheduler:
  poolSize: 32
  threadNamePrefix: "ssp-business-service-"
  awaitTerminationSeconds: 60

# 素材管理
material:
  tos:
    endpoint: tos-cn-beijing.volces.com
    region: cn-beijing
    accessKey: AKLTZmYyYmFhOGMxNTU5NGUyYWE3ZmVhNzdhZjY0OTcxZmY
    secretKey: T1RrMk16bGlOelZpTjJNME5ETTVPRGhpWTJVeVlXWTNObU0yTVRNMVlqUQ==
    bucketName: dsp-static
    connectTimeoutMills: 30000

# DMP
dmp:
  tos:
    endpoint: tos-cn-beijing.volces.com
    region: cn-beijing
    accessKey: AKLTZmYyYmFhOGMxNTU5NGUyYWE3ZmVhNzdhZjY0OTcxZmY
    secretKey: T1RrMk16bGlOelZpTjJNME5ETTVPRGhpWTJVeVlXWTNObU0yTVRNMVlqUQ==
    bucketName: dmp-jw
    connectTimeoutMills: 30000

# 自定义参数
properties:
  chromeDev: true
  chromePath: /opt/base/chrome-linux64/chrome
  chromeDriverPath: /Users/<USER>/Downloads/chrome/chromedriver-mac-arm64/chromedriver
  tmpFilePath: /tmp
  convertIos: true
  dmpCollectPath: /opt/dmp
