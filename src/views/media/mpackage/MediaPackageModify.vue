<template>
  <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="100px" size="mini" class="ruleForm">
    <el-form-item label="所属媒体">
      <span>{{ ruleForm.mediaName }} - {{ ruleForm.mediaCode }}</span>
    </el-form-item>
    <el-form-item label="所属APP">
      <span>{{ ruleForm.mediaAppName }} - {{ ruleForm.mediaAppCode }} - {{ parseOsType(ruleForm.mediaAppType) }}</span>
    </el-form-item>
    <el-form-item label="广告位">
      <span>{{ ruleForm.mediaTagName }} - {{ ruleForm.mediaTagCode }} - {{ parseTagType(ruleForm.type) }}</span>
    </el-form-item>
    <el-form-item label="名称" prop="name">
      <el-input v-model="ruleForm.name" placeholder="分包名称" />
    </el-form-item>
    <el-form-item label="包名" prop="packageName">
      <el-input v-model="ruleForm.packageName" placeholder="应用包名" />
    </el-form-item>
    <el-form-item label="应用名称" prop="appName">
      <el-input v-model="ruleForm.appName" placeholder="应用名称" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" :disabled="btnClicked" @click="submitForm('ruleForm')">确定</el-button>
      <el-button @click="createMM">取消</el-button>
      <el-button type="danger" @click="createMM">关闭</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { infoMediaPackage, modifyMediaPackage } from '@/api/media/mediaPackage'
import { listTagTypeApi, listOsTypeApi, listFlowTypeApi, listBidTypeApi } from '@/api/public/typeinfo'
export default {
  name: 'MediaPackageModify',
  props: {
    isUpdate: {
      required: true,
      type: Boolean
    },
    id: {
      required: true,
      type: Number
    }
  },
  data() {
    return {
      btnClicked: false,
      ruleForm: {
        name: null,
        packageName: null,
        appName: null,
        mediaName: null,
        mediaCode: null,
        mediaAppName: null,
        mediaAppCode: null,
        mediaAppType: null,
        mediaTagName: null,
        mediaTagCode: null,
        mediaTagType: null,
        mediaId: null,
        mediaAppId: null
      },
      osTypeList: [],
      tagTypeList: [],
      flowTypeList: [],
      bidTypeList: []
    }
  },
  created() {
    listTagTypeApi().then(response => {
      this.tagTypeList = response.result
    })
    listOsTypeApi().then(response => {
      this.osTypeList = response.result.filter(item => item.id !== 999)
    })
    listFlowTypeApi().then(response => {
      this.flowTypeList = response.result
    })
    listBidTypeApi().then(response => {
      this.bidTypeList = response.result
    })
    this.loadData()
  },
  methods: {
    parseOsType(id) {
      const value = this.osTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseTagType(id) {
      const value = this.tagTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseFlowType(id) {
      const value = this.flowTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseBidType(id) {
      const value = this.bidTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    async loadData() {
      const res = await infoMediaPackage({ id: this.id })
      this.ruleForm = {
        ...this.ruleForm,
        ...res.result
      }
    },
    closeMe() {
      this.$emit('changePageOpen', false)
      this.btnClicked = false
    },
    createMM() {
      this.$emit('update:isUpdate', false)
      this.closeMe()
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.btnClicked = true
          modifyMediaPackage({
            id: this.id,
            name: this.ruleForm.name,
            packageName: this.ruleForm.packageName,
            appName: this.ruleForm.appName
          })
            .then(response => {
              this.$message({
                message: '修改成功',
                type: 'success'
              })
              this.$emit('update:isUpdate', true)
              this.closeMe()
            })
            .catch(() => {
              this.btnClicked = false
            })
          return true
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
.ruleForm {
  margin-left: 2%;
  margin-right: 2%;
}
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
</style>
