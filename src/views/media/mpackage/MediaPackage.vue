<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" class="ruleForm" size="mini">
        <el-form-item label="媒体" prop="mediaId">
          <el-select
            v-model="listQuery.mediaId"
            filterable
            clearable
            placeholder="请选择媒体"
            style="width: 100%"
            remote
            :remote-method="loadMedias"
            @change="changeMedia"
          >
            <el-option v-for="media in medias" :key="media.id" :label="media.name + ' - ' + media.code" :value="media.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="媒体APP" prop="mediaAppId">
          <el-select
            v-model="listQuery.mediaAppId"
            filterable
            clearable
            placeholder="请选择媒体APP"
            style="width: 100%"
            remote
            :remote-method="loadMediaApps"
          >
            <el-option
              v-for="mediaApp in mediaApps"
              :key="mediaApp.id"
              :label="mediaApp.name + ' - ' + mediaApp.code + ' - ' + parseOsType(mediaApp.type)"
              :value="mediaApp.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="媒体广告位" prop="mediaTagId">
          <el-select
            v-model="listQuery.mediaTagId"
            filterable
            clearable
            placeholder="请选择媒体广告位"
            style="width: 100%"
            remote
            :remote-method="loadMediaTags"
          >
            <el-option
              v-for="mediaTag in mediaTags"
              :key="mediaTag.id"
              :label="mediaTag.name + ' - ' + mediaTag.code + ' - ' + parseBidType(mediaTag.type)"
              :value="mediaTag.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="包名">
          <el-input v-model="listQuery.packageName" placeholder="名称或包名或应用名" />
        </el-form-item>

        <el-form-item label>
          <el-button class="filter-item" type="primary" @click="handleFilter">查询</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="info" @click="remove">清除</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button v-if="AuthUtils.hasAuth('MEDIA_PACKAGE_ADD')" type="success" icon="el-icon-circle-plus-outline" @click="addTag">
            新增
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      ref="dataTable"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      :row-class-name="tableRowClassName"
    >
      <el-table-column align="center" min-width="55" type="selection" :selectable="checkSelectable" />
      <el-table-column align="center" label="序号" min-width="60" type="index" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="包名" align="center">
        <template slot-scope="scope"> {{ scope.row.packageName }} </template>
      </el-table-column>
      <el-table-column label="应用名" align="center">
        <template slot-scope="scope"> {{ scope.row.appName }} </template>
      </el-table-column>
      <el-table-column label="所属媒体" align="center">
        <template slot-scope="scope"> {{ scope.row.mediaName }} - {{ scope.row.mediaCode }} </template>
      </el-table-column>
      <el-table-column label="所属APP" align="center">
        <template slot-scope="scope">
          {{ scope.row.mediaAppName }} - {{ scope.row.mediaAppCode }} - {{ parseOsType(scope.row.mediaAppType) }}
        </template>
      </el-table-column>
      <el-table-column label="所属广告位" align="center">
        <template slot-scope="scope">
          {{ scope.row.mediaTagName }} - {{ scope.row.mediaTagCode }} - {{ parseBidType(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <el-button
            v-if="AuthUtils.hasAuth('MEDIA_PACKAGE_MODIFY')"
            type="success"
            size="mini"
            icon="el-icon-edit"
            title="编辑"
            circle
            @click="handleModify(row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-if="paginationShow"
      class="pagination"
      :total="total"
      :total-page="totalPage"
      :page="currentPageNum"
      :start.sync="listQuery.start"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-drawer
      title="新增分包"
      :show-close="false"
      size="50%"
      :visible.sync="addOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <MediaPackageAdd v-if="addOpen" :is-update.sync="addUpdate" @changePageOpen="changeAddOpen" />
    </el-drawer>

    <el-drawer
      title="修改分包"
      :show-close="false"
      size="50%"
      :visible.sync="modifyOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <MediaPackageModify v-if="modifyOpen" :id.sync="infoId" :is-update.sync="modifyUpdate" @changePageOpen="changeModifyOpen" />
    </el-drawer>

  </div>
</template>
<script>
import { pageMediaPackage } from '@/api/media/mediaPackage' // 后台接口
import { listMedia } from '@/api/media/mediaMain'
import { listMediaApp } from '@/api/media/mediaApp'
import { listMediaTag } from '@/api/media/mediaTag'
import { listOsTypeApi, listTagTypeApi } from '@/api/public/typeinfo'
import { listBidTypeApi } from '@/api/public/typeinfo'
import Pagination from '@/components/Pagination' // 分页
import MediaPackageAdd from './MediaPackageAdd' // 新增
import MediaPackageModify from './MediaPackageModify' // 修改
export default {
  name: 'MediaPackage',
  components: { MediaPackageAdd, MediaPackageModify, Pagination },
  data() {
    return {
      paginationShow: true,
      list: null,
      total: 0,
      totalPage: 0,
      currentPageNum: 0,
      listLoading: false,
      listQuery: {
        start: 0,
        limit: 20,
        name: null,
        mediaId: null,
        mediaAppId: null,
        mediaTagId: null,
        packageName: null,
        bidType: 0
      },
      medias: [],
      mediaApps: [],
      mediaTags: [],
      osTypeList: [],
      tagTypeList: [],
      bidTypeList: [],
      addOpen: false,
      modifyOpen: false,
      addUpdate: false,
      modifyUpdate: false,
      infoId: null,
      mediaId: null,
      mediaAppId: null,
      dialogVisible: false,
      settlementRatio: ''
    }
  },
  created() {
    listTagTypeApi().then(response => {
      this.tagTypeList = response.result
    })
    listOsTypeApi().then(response => {
      this.osTypeList = response.result.filter(item => item.id !== 999)
    })
    listBidTypeApi().then(response => {
      this.bidTypeList = response.result
    })
    this.loadMedias(null)
    this.getList()
  },
  methods: {
    parseOsType(id) {
      const value = this.osTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseTagType(id) {
      const value = this.tagTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseBidType(id) {
      const value = this.bidTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    loadMedias(name) {
      listMedia({ name: name }).then(response => {
        this.medias = response.result
      })
    },
    changeMedia() {
      this.mediaApps = []
      this.listQuery.mediaAppId = null
      this.loadMediaApps(null)
    },
    loadMediaApps(name) {
      listMediaApp({ name: name, mediaId: this.listQuery.mediaId }).then(response => {
        this.mediaApps = response.result
      })
    },
    loadMediaTags(name) {
      listMediaTag({ name: name, mediaId: this.listQuery.mediaId, mediaAppId: this.listQuery.mediaAppId }).then(response => {
        this.mediaTags = response.result
      })
    },
    getList() {
      this.list = []
      this.total = 0
      this.totalPage = 0
      this.currentPageNum = 0
      this.paginationShow = false
      this.listLoading = true
      pageMediaPackage(this.listQuery).then(response => {
        this.list = response.result.list
        this.total = response.result.totalCount
        this.totalPage = response.result.totalPage
        this.currentPageNum = response.result.currentPageNum
        this.listLoading = false
        this.paginationShow = true
      })
    },
    addTag() {
      this.addOpen = true
    },
    editTag(row) {
      this.infoId = row.id
      this.modifyOpen = true
    },
    handleFilter() {
      this.listQuery.start = 0
      this.getList()
    },
    changeAddOpen(open) {
      this.addOpen = open
      if (this.addUpdate) {
        this.getList()
      }
    },
    changeModifyOpen(flag) {
      this.modifyOpen = flag
      if (this.modifyUpdate) {
        this.getList()
      }
    },
    remove() {
      this.listQuery.start = 0
      this.listQuery.limit = 20
      this.listQuery.mediaId = null
      this.listQuery.mediaAppId = null
      this.listQuery.name = null
      this.listQuery.bidType = 0
      this.getList()
    },
    handleModify(row) {
      this.infoId = row.id
      this.modifyOpen = true
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 !== 1) {
        return 'success-row'
      }
      return ''
    },
    // 计算百分比
    calculatePercentage(numerator, denominator) {
      if (denominator === 0 || isNaN(numerator) || isNaN(denominator)) {
        return '0.00%'
      }
      return ((numerator / denominator) * 100).toFixed(2) + '%'
    },
    checkSelectable(row, index) {
      if (row.bidType === 2) {
        return true
      }
      return false
    }
  }
}
</script>
<style scoped>
::v-deep .el-drawer__body {
  overflow: auto;
}
</style>
