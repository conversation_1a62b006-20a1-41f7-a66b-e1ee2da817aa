<template>
  <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="100px" size="mini" class="ruleForm">
    <el-form-item label="媒体" prop="mediaId">
      <el-select
        v-model="ruleForm.mediaId"
        filterable
        clearable
        placeholder="请选择媒体"
        style="width: 100%"
        remote
        :remote-method="loadMedias"
        @change="changeMedia"
      >
        <el-option v-for="media in medias" :key="media.id" :label="media.name + ' - ' + media.code" :value="media.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="媒体APP" prop="mediaAppId">
      <el-select
        v-model="ruleForm.mediaAppId"
        filterable
        clearable
        placeholder="请选择媒体APP"
        style="width: 100%"
        remote
        :remote-method="loadMediaApps"
        @change="getRealName"
      >
        <el-option
          v-for="mediaApp in mediaApps"
          :key="mediaApp.id"
          :label="mediaApp.name + ' - ' + mediaApp.code + ' - ' + parseOsType(mediaApp.type)"
          :value="mediaApp.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="类型" prop="type">
      <el-select v-model="ruleForm.type" filterable clearable placeholder="请选择类型" style="width: 100%" @change="getRealName">
        <el-option v-for="(tt, x) in types" :key="'tagt-' + x" :label="tt.name" :value="tt.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="结算方式" prop="bidType">
      <el-radio-group v-model="ruleForm.bidType" @change="getRealName">
        <el-radio v-for="item in bidTypeList" :key="item.id" :label="item.id">{{ item.name }} </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="描述" prop="name">
      <el-input v-model="ruleForm.name" placeholder="建议填写预算名称,如：启航" />
    </el-form-item>
    <el-form-item label="名称">
      <span>{{ realTagName }}</span>
    </el-form-item>
    <el-form-item v-if="2 == localProtocol.type" label="CODE" prop="code">
      <el-input v-model="ruleForm.code">
        <el-button slot="append" icon="el-icon-refresh" @click="handTagCode" />
      </el-input>
    </el-form-item>
    <el-form-item label="宽度" prop="width">
      <el-input v-model="ruleForm.width">
        <template slot="append">像素</template>
      </el-input>
    </el-form-item>
    <el-form-item label="高度" prop="height">
      <el-input v-model="ruleForm.height">
        <template slot="append">像素</template>
      </el-input>
    </el-form-item>
    <el-form-item label="超时时间" prop="timeout">
      <el-input v-model="ruleForm.timeout">
        <template slot="append">毫秒</template>
      </el-input>
    </el-form-item>
    <el-form-item v-if="ruleForm.bidType === 2" label="分成结算比例" prop="settlementRatio">
      <el-input v-model="ruleForm.settlementRatio">
        <template slot="append">%</template>
      </el-input>
    </el-form-item>
    <el-form-item label="流量类型" prop="flowType">
      <el-radio-group v-model="ruleForm.flowType">
        <el-radio v-for="item in flowTypeList" :key="item.id" :label="item.id">{{ item.name }} </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="HTTPS上报" prop="needHttps">
      <el-radio v-model="ruleForm.needHttps" :label="0">否</el-radio>
      <el-radio v-model="ruleForm.needHttps" :label="1">是</el-radio>
    </el-form-item>
    <el-form-item label="HTTPS素材" prop="materialHttps">
      <el-radio v-model="ruleForm.materialHttps" :label="false">否</el-radio>
      <el-radio v-model="ruleForm.materialHttps" :label="true">是</el-radio>
    </el-form-item>
    <el-form-item label="开启过滤" prop="filterOnOff">
      <el-switch v-model="ruleForm.filterOnOff" />
    </el-form-item>
    <el-form-item v-if="ruleForm.filterOnOff" label="链接域名">
      <el-input
        v-model="ruleForm.filterUrlDomain"
        type="textarea"
        :autosize="{ minRows: 1, maxRows: 5 }"
        placeholder="多个英文逗号分割"
      />
    </el-form-item>
    <el-form-item v-if="ruleForm.filterOnOff" label="过滤素材比例">
      <el-radio v-model="ruleForm.filterMaterialRatio" :label="0">不过滤</el-radio>
      <el-radio v-model="ruleForm.filterMaterialRatio" :label="1">过滤</el-radio>
    </el-form-item>
    <el-form-item v-if="ruleForm.filterOnOff" label="过滤素材类型">
      <el-radio v-model="ruleForm.filterMaterialType" :label="0">不过滤</el-radio>
      <el-radio v-model="ruleForm.filterMaterialType" :label="1">过滤</el-radio>
    </el-form-item>
    <el-form-item v-if="ruleForm.filterOnOff" label="过滤操作类型">
      <el-radio v-model="ruleForm.filterActionType" :label="0">不过滤</el-radio>
      <el-radio v-model="ruleForm.filterActionType" :label="1">过滤</el-radio>
    </el-form-item>
    <el-form-item
      v-show="ruleForm.mediaId && localProtocol.param && localProtocol.param !== '' && localProtocol.param !== '[]'"
      label="扩展参数"
      prop="pnyParam"
    >
      <ParameterValueForm ref="pnyParam" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" :disabled="btnClicked" @click="submitForm('ruleForm')">确定</el-button>
      <el-button @click="createMM">取消</el-button>
      <el-button type="danger" @click="createMM">关闭</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { addMediaTag } from '@/api/media/mediaTag'
import { listMedia } from '@/api/media/mediaMain'
import { listMediaApp } from '@/api/media/mediaApp'
import { infoMediaProtocol } from '@/api/media/mediaProtocol'
import { listTagTypeApi, listOsTypeApi, listFlowTypeApi, listBidTypeApi } from '@/api/public/typeinfo'
import UUID from '@/utils/Uuid'
import ParameterValueForm from '@/components/ParameterValueForm'
export default {
  name: 'MediaTagAdd',
  components: { ParameterValueForm },
  props: {
    isUpdate: {
      required: true,
      type: Boolean
    }
  },
  data() {
    return {
      btnClicked: false,
      ruleForm: {
        name: null,
        code: null,
        mediaId: null,
        mediaAppId: null,
        type: null,
        width: null,
        height: null,
        bidType: 1,
        pnyParam: null,
        timeout: null,
        needHttps: 0,
        materialHttps: false,
        flowType: 1,
        settlementRatio: null,
        filterOnOff: false,
        filterUrlDomain: null,
        filterMaterialType: 0,
        filterActionType: 0,
        filterMaterialRatio: 0
      },
      rules: {
        name: [{ required: true, trigger: 'blur', message: '不能为空' }],
        code: [{ required: true, trigger: 'blur', message: '不能为空' }],
        mediaId: [{ required: true, trigger: 'change', message: '不能为空' }],
        mediaAppId: [{ required: true, trigger: 'change', message: '不能为空' }],
        type: [{ required: true, trigger: 'blur', message: '不能为空' }],
        bidType: [{ required: true, trigger: 'blur', message: '不能为空' }],
        width: [{ required: true, trigger: 'blur', message: '不能为空' }],
        height: [{ required: true, trigger: 'blur', message: '不能为空' }],
        flowType: [{ required: true, trigger: 'blur', message: '不能为空' }],
        needHttps: [{ required: true, trigger: 'blur', message: '不能为空' }],
        materialHttps: [{ required: true, trigger: 'blur', message: '不能为空' }],
        timeout: [{ required: true, trigger: 'blur', message: '不能为空' }],
        filterOnOff: [{ required: true, trigger: 'change', message: '未选择' }]
      },
      osTypeList: [],
      flowTypeList: [],
      bidTypeList: [],
      medias: [],
      mediaApps: [],
      types: [],
      localProtocol: {},
      realTagName: ''
    }
  },
  computed: {},
  watch: {
    'ruleForm.name': {
      handler(v) {
        this.getRealName()
      },
      immediate: true
    }
  },
  created() {
    listFlowTypeApi().then(response => {
      this.flowTypeList = response.result
    })
    listBidTypeApi().then(response => {
      this.bidTypeList = response.result
    })
    listOsTypeApi().then(response => {
      this.osTypeList = response.result.filter(item => item.id !== 999)
    })
    listTagTypeApi({}).then(response => {
      this.types = response.result
    })
    this.loadMedias(null)
  },
  methods: {
    parseOsType(id) {
      const value = this.osTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseTagType(id) {
      const value = this.types.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseBidType(id) {
      const value = this.bidTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    getRealName() {
      if (this.ruleForm.mediaAppId && this.ruleForm.type && this.ruleForm.bidType && this.ruleForm.name) {
        const app = this.mediaApps.find(item => item.id === this.ruleForm.mediaAppId)
        this.realTagName =
          app.name +
          '-' +
          this.parseOsType(app.type) +
          '-' +
          this.parseTagType(this.ruleForm.type) +
          '-' +
          this.ruleForm.name +
          '-' +
          this.parseBidType(this.ruleForm.bidType)
      }
    },
    loadMedias(name) {
      listMedia({ name: name }).then(response => {
        this.medias = response.result
      })
    },
    changeMedia(id) {
      this.mediaApps = []
      this.ruleForm.mediaAppId = null
      this.loadMediaApps(null)
      const o = this.medias.find(item => item.id === id)
      if (o) {
        this.loadProtocolData(o.protocolId)
      }
      this.getRealName()
    },
    loadMediaApps(name) {
      if (this.ruleForm.mediaId) {
        listMediaApp({ name: name, mediaId: this.ruleForm.mediaId }).then(response => {
          this.mediaApps = response.result
        })
      }
    },
    async loadProtocolData(protocolId) {
      const res = await infoMediaProtocol({ id: protocolId })
      const t = res.result
      this.localProtocol = t
      if (t.tagParam) {
        this.$refs.pnyParam.setParamText(
          JSON.parse(t.tagParam).map(item => ({
            ...item,
            paramsValue: ''
          }))
        )
      } else {
        this.$refs.pnyParam.setParamText([])
      }
    },
    closeMe() {
      this.$emit('changePageOpen', false)
      this.btnClicked = false
    },
    createMM() {
      this.$emit('update:isUpdate', false)
      this.closeMe()
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.ruleForm.bidType === 2) {
            if (this.ruleForm.settlementRatio === null || this.ruleForm.settlementRatio === '' || this.ruleForm.settlementRatio <= 0) {
              this.$message({
                message: '分成结算比例不能为空，且必须大于0',
                type: 'warning'
              })
              return false
            }
          }
          this.btnClicked = true
          this.ruleForm.pnyParam = this.$refs.pnyParam.getParamText()
          addMediaTag(this.ruleForm)
            .then(response => {
              this.$message({
                message: '新增成功',
                type: 'success'
              })
              this.$emit('update:isUpdate', true)
              this.closeMe()
            })
            .catch(() => {
              this.btnClicked = false
            })
          return true
        } else {
          return false
        }
      })
    },
    handTagCode() {
      this.ruleForm.code = UUID.gen().replace(/-/g, '').substr(24, 8)
    }
  }
}
</script>

<style scoped>
.ruleForm {
  margin-left: 2%;
  margin-right: 2%;
}
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
</style>
