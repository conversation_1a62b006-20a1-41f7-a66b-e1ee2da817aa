<template>
  <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="100px" size="mini" class="ruleForm">
    <el-form-item label="媒体" prop="mediaId">
      <el-select
        v-model="ruleForm.mediaId"
        filterable
        clearable
        placeholder="请选择媒体"
        style="width: 100%"
        remote
        :remote-method="loadMedias"
        @change="changeMedia"
      >
        <el-option v-for="media in medias" :key="media.id" :label="media.name + ' - ' + media.code" :value="media.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="媒体APP" prop="mediaAppId">
      <el-select
        v-model="ruleForm.mediaAppId"
        filterable
        clearable
        placeholder="请选择媒体APP"
        style="width: 100%"
        remote
        :remote-method="loadMediaApps"
        @change="changeMediaApp"
      >
        <el-option
          v-for="mediaApp in mediaApps"
          :key="mediaApp.id"
          :label="mediaApp.name + ' - ' + mediaApp.code + ' - ' + parseOsType(mediaApp.type)"
          :value="mediaApp.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="媒体广告位" prop="mediaTagId">
      <el-select
        v-model="ruleForm.mediaTagId"
        filterable
        clearable
        placeholder="请选择媒体广告位"
        style="width: 100%"
        remote
        :remote-method="loadMediaTags"
      >
        <el-option
          v-for="mediaTag in mediaTags"
          :key="mediaTag.id"
          :label="mediaTag.name + ' - ' + mediaTag.code + ' - ' + parseBidType(mediaTag.type)"
          :value="mediaTag.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="名称" prop="name">
      <el-input v-model="ruleForm.name" placeholder="分包名称" />
    </el-form-item>
    <el-form-item label="包名" prop="name">
      <el-input v-model="ruleForm.packageName" placeholder="应用包名" />
    </el-form-item>
    <el-form-item label="应用名称">
      <el-input v-model="ruleForm.appName" placeholder="应用名称" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" :disabled="btnClicked" @click="submitForm('ruleForm')">确定</el-button>
      <el-button @click="createMM">取消</el-button>
      <el-button type="danger" @click="createMM">关闭</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { addMediaPackage } from '@/api/media/mediaPackage'
import { listMedia } from '@/api/media/mediaMain'
import { listMediaApp } from '@/api/media/mediaApp'
import { listMediaTag } from '@/api/media/mediaTag'
import { listTagTypeApi } from '@/api/public/typeinfo'
import { listOsTypeApi } from '@/api/public/typeinfo'
import { listBidTypeApi } from '@/api/public/typeinfo'
export default {
  name: 'MediaTagAdd',
  props: {
    isUpdate: {
      required: true,
      type: Boolean
    }
  },
  data() {
    return {
      btnClicked: false,
      ruleForm: {
        name: null,
        packageName: null,
        appName: null,
        mediaId: null,
        mediaAppId: null,
        mediaTagId: null
      },
      rules: {
        name: [{ required: true, trigger: 'blur', message: '不能为空' }],
        mediaId: [{ required: true, trigger: 'change', message: '不能为空' }],
        mediaAppId: [{ required: true, trigger: 'change', message: '不能为空' }],
        mediaTagId: [{ required: true, trigger: 'change', message: '不能为空' }],
        packageName: [{ required: true, trigger: 'blur', message: '不能为空' }],
        appName: [{ required: true, trigger: 'blur', message: '不能为空' }]
      },
      medias: [],
      mediaApps: [],
      mediaTags: [],
      osTypeList: [],
      tagTypeList: [],
      bidTypeList: []
    }
  },
  computed: {},
  created() {
    this.loadMedias(null)
    listTagTypeApi().then(response => {
      this.tagTypeList = response.result
    })
    listOsTypeApi().then(response => {
      this.osTypeList = response.result.filter(item => item.id !== 999)
    })
    listBidTypeApi().then(response => {
      this.bidTypeList = response.result
    })
  },
  methods: {
    parseOsType(id) {
      const value = this.osTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseTagType(id) {
      const value = this.tagTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseBidType(id) {
      const value = this.bidTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    changeMediaApp(id) {
      this.mediaTags = []
      this.ruleForm.mediaTagId = null
      this.loadMediaTags(null)
    },
    loadMediaTags(name) {
      listMediaTag({ name: name }).then(response => {
        this.mediaTags = response.result
      })
    },
    loadMedias(name) {
      listMedia({ name: name }).then(response => {
        this.medias = response.result
      })
    },
    changeMedia(id) {
      this.mediaApps = []
      this.ruleForm.mediaAppId = null
      this.loadMediaApps(null)
    },
    loadMediaApps(name) {
      if (this.ruleForm.mediaId) {
        listMediaApp({ name: name, mediaId: this.ruleForm.mediaId }).then(response => {
          this.mediaApps = response.result
        })
      }
    },
    closeMe() {
      this.$emit('changePageOpen', false)
      this.btnClicked = false
    },
    createMM() {
      this.$emit('update:isUpdate', false)
      this.closeMe()
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.btnClicked = true
          addMediaPackage(this.ruleForm)
            .then(response => {
              this.$message({
                message: '新增成功',
                type: 'success'
              })
              this.$emit('update:isUpdate', true)
              this.closeMe()
            })
            .catch(() => {
              this.btnClicked = false
            })
          return true
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
.ruleForm {
  margin-left: 2%;
  margin-right: 2%;
}
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
</style>
