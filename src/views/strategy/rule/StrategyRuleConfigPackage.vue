<template>
  <el-container class="app-container" style="display: flex; flex-direction: column">
    <el-card header="媒体广告位信息" style="width: 100%; margin-bottom: 10px">
      <el-form class="ruleForm" size="mini" :inline="true">
        <el-form-item label="媒体" prop="mediaId">
          <el-select
            v-model="listQuery.mediaId"
            filterable
            clearable
            placeholder="请选择媒体"
            style="width: 200px"
            remote
            :remote-method="loadMedias"
            @change="changeMedia"
          >
            <el-option v-for="media in medias" :key="media.id" :label="media.name + ' - ' + media.code" :value="media.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="媒体APP" prop="mediaAppId">
          <el-select
            v-model="listQuery.mediaAppId"
            filterable
            clearable
            placeholder="请选择媒体APP"
            style="width: 200px"
            remote
            :remote-method="loadMediaApps"
            @change="changeMediaApp"
          >
            <el-option
              v-for="mediaApp in mediaApps"
              :key="mediaApp.id"
              :label="mediaApp.name + ' - ' + mediaApp.code + ' - ' + parseOsType(mediaApp.type)"
              :value="mediaApp.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="媒体广告位" prop="mediaTagId">
          <el-select
            v-model="listQuery.mediaTagId"
            filterable
            clearable
            placeholder="请选择媒体广告位"
            style="width: 200px"
            remote
            :remote-method="loadMediaTags"
            @change="handleTagChange"
          >
            <el-option
              v-for="mediaTag in mediaTags"
              :key="mediaTag.id"
              :label="mediaTag.name + ' - ' + mediaTag.code + ' - ' + parseType(mediaTag.type)"
              :value="mediaTag.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="媒体分包" prop="mediaPackageId">
          <el-select
            v-model="listQuery.mediaPackageId"
            filterable
            clearable
            placeholder="请选择媒体分包"
            style="width: 200px"
            remote
            :remote-method="loadMediaPackages"
            @change="handlePackageChange"
          >
            <el-option
              v-for="mediaPackage in mediaPackages"
              :key="mediaPackage.id"
              :label="mediaPackage.name + ' - ' + mediaPackage.appName"
              :value="mediaPackage.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="primary" icon="el-icon-search" @click="selectAll()">查询</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="info" icon="el-icon-refresh" @click="remove">清除</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="success" icon="el-icon-eves" @click="changeShowNoTags">
            {{ showNoTags ? '隐藏' : '显示' }}未配置预算广告位
          </el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="mediaTabLoading"
        :data="infoShowList"
        border
        fit
        style="width: 100%"
        highlight-current-row
        :row-key="row => row.id"
        :current-row="selectedInfoShow"
        @current-change="handleInfoShowChange"
      >
        <el-table-column
          type="selection"
          width="55"
          :reserve-selection="false"
          :selectable="(row, index) => true"
        >
          <template slot-scope="{ row }">
            <el-radio
              :value="selectedInfoShow && selectedInfoShow.id"
              :label="row.id"
              @change="() => handleInfoShowSelect(row)"
            />
            <el-table-column label="分量规则" prop="id" align="center">
              <span>{{ parseQuantityLimitType(row.quantityLimitType) }}</span>
            </el-table-column>
            <el-table-column v-if="selectedInfoShow.quantityLimitType === 2" label="最大并行数" prop="id" align="center">
              <span v-if="row.quantityLimitType === 2">{{ row.parallelMaxAdv }}</span>
            </el-table-column>
            <el-table-column label="媒体" prop="appName" align="center">
              {{ row.mediaName }}
              <br />
              {{ row.mediaCode }}
            </el-table-column>
            <el-table-column label="APP" prop="appName" align="center">
              {{ row.appName }}
              <br />
              {{ row.appCode }}
              <br />
              {{ parseOsType(row.appType) }}
            </el-table-column>
            <el-table-column label="广告位" prop="appName" align="center">
              {{ row.tagName }}
              <br />
              {{ row.tagCode }}
            </el-table-column>
            <el-table-column label="分包" prop="appName" align="center">
              {{ row.name }}
              <br />
              {{ row.mediaAppName }}
              <br />
              {{ row.mediaPackageName }}
            </el-table-column>
            <el-table-column label="尺寸" prop="id" align="center">
              <span>{{ row.width }} * {{ row.height }}</span>
            </el-table-column>
            <el-table-column label="类型" prop="type" align="center">
              <span>{{ parseType(row.type) }}</span>
            </el-table-column>
            <el-table-column label="结算方式" prop="bidType" align="center">
              <span>{{ parseBidType(row.bidType) }}</span>
            </el-table-column>
            <el-table-column label="超时时间" prop="id" align="center">
              <span>{{ row.timeout }}毫秒</span>
            </el-table-column>
          </template>

        </el-table-column>
      </el-table>
    </el-card>

    <div style="display: flex; flex-direction: row">
      <el-card v-if="showNoTags" header="未配置预算广告位" style="width: 29.5%; margin-right: 0.5%">
        <el-form ref="ruleForm" :inline="true" size="mini" class="ruleForm" label-width="60px">
          <el-form-item label="预算" prop="advertiserId">
            <el-select
              v-model="listQuery.advertiserId"
              filterable
              clearable
              placeholder="请选择预算"
              style="width: 200px"
              remote
              :remote-method="loadAdvertiser"
              @change="handlerAdvChang"
            >
              <el-option v-for="advertiser in advertisers" :key="advertiser.id" :label="advertiser.name" :value="advertiser.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="APP" prop="advertiserAppId">
            <el-select
              v-model="listQuery.advertiserAppId"
              filterable
              clearable
              placeholder="请选择预算APP"
              style="width: 200px"
              remote
              :remote-method="loadAdvApp"
              @change="loadAdvTag(1)"
            >
              <el-option
                v-for="advertiserApp in advertiserApps"
                :key="advertiserApp.id"
                :label="advertiserApp.name + ' - ' + advertiserApp.code + ' - ' + parseOsType(advertiserApp.type)"
                :value="advertiserApp.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="广告位" prop="name">
            <el-input v-model="listQuery.name" type="text" placeholder="请输入预算广告位名称/代码" style="width: 200px" />
          </el-form-item>
          <el-form-item label="结算">
            <el-select v-model="listQuery.settlementType" style="width: 200px">
              <!-- <el-option :value="0" label="全部" /> -->
              <el-option :value="2" label="分成" />
              <el-option :value="1" label="竞价" />
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label>
            <el-button class="filter-item" type="primary" icon="el-icon-search" @click="loadAdvTag">查询</el-button>
          </el-form-item>
          <el-form-item label>
            <el-button class="filter-item" type="info" icon="el-icon-refresh" @click="removeAdv">清除</el-button>
          </el-form-item>
          <el-form-item label>
            <el-button type="success" icon="el-icon-circle-plus-outline" @click="handleAdd">批量添加</el-button>
          </el-form-item>
        </el-form>
        <el-table ref="advTagTable" v-loading="advTabLoading" :data="advertiserTags" border fit highlight-current-row style="width: 100%">
          <el-table-column align="center" min-width="55" type="selection" />
          <el-table-column align="center" label="序号" min-width="60" type="index" />
          <el-table-column label="预算广告位" align="left" prop="name">
            <template slot-scope="{ row }">
              <el-tooltip placement="right" effect="light">
                <div slot="content">
                  <el-tabs type="card">
                    <el-tab-pane label="基础信息">
                      <div class="demo-row">
                        <label>类型</label><span>{{ parseType(row.type) }}</span>
                      </div>
                      <div class="demo-row">
                        <label>超时时间</label><span>{{ row.timeout }}毫秒</span>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="结算信息">
                      <div class="demo-row">
                        <label>结算方式</label><span>{{ parseBidType(row.settlementType) }}</span>
                      </div>
                      <div v-if="row.settlementType == 1" class="demo-row">
                        <label>竞价模式</label><span>{{ parseBidPriceType(row.bidPriceType) }}</span>
                      </div>
                      <div v-if="row.settlementType == 1 && row.bidPriceType == 1" class="demo-row">
                        <label>涨幅比例</label><span>{{ row.bidRisesRatio }}%</span>
                      </div>
                      <div v-if="row.settlementType == 2" class="demo-row">
                        <label>价格处理</label><span>{{ parseSharingPriceTypeList(row.sharingPriceType) }}</span>
                      </div>
                      <div v-if="fixedPriceShow(row)" class="demo-row">
                        <label>固定底价</label><span>{{ row.fixedPrice }}元</span>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane v-if="row.limitRuleOpen" label="基础规则">
                      <div class="demo-row">
                        <label>限量额度</label>
                        <span v-if="row.limitType == 1">{{ row.quota }}次/每天</span>
                        <span v-if="row.limitType == 2">{{ row.quota }}次/小时</span>
                        <span v-if="row.limitType == 3">{{ row.quota }}次/每秒</span>
                        <span v-if="row.limitType == 4">不限</span>
                      </div>
                      <div class="demo-row">
                        <label>允许时间</label>
                        <span v-if="row.startTime && row.endTime"> {{ row.startTime }}点 - {{ row.endTime }}点 </span>
                        <span v-if="row.startTime && !row.endTime"> {{ row.startTime }}点 - 23点 </span>
                        <span v-if="!row.startTime && row.endTime"> 0点 - {{ row.endTime }}点 </span>
                        <span v-if="!row.startTime && !row.endTime"> 不限 </span>
                      </div>
                      <div class="demo-row">
                        <label>日限曝光量</label><span>{{ row.filterExposureNum }}次/日</span>
                      </div>
                      <div class="demo-row">
                        <label>日限点击量</label><span>{{ row.filterClickNum }}次/日</span>
                      </div>
                      <div class="demo-row">
                        <label>单设备限请求量</label><span>{{ row.filterDeviceReqNum }}次/日</span>
                      </div>
                      <div class="demo-row">
                        <label>单设备限曝光量</label><span>{{ row.filterDeviceExposureNum }}次/日</span>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane v-if="row.directOnOff" label="定向规则">
                      <div class="demo-row">
                        <label>定向地区</label><span>{{ row.targetArea }}</span>
                      </div>
                      <div class="demo-row">
                        <label>定向行业</label><span>{{ row.targetAppIndustry }}</span>
                      </div>
                      <div class="demo-row">
                        <label>定向APP</label><span>{{ row.targetAppPackage }}</span>
                      </div>
                      <div class="demo-row">
                        <label>定向设备型号</label><span>{{ row.targetDeviceModel }}</span>
                      </div>
                      <div class="demo-row">
                        <label>定向网络类型</label><span>{{ row.targetNetwork }}</span>
                      </div>
                      <div class="demo-row">
                        <label>定向运营商</label><span>{{ row.targetOperator }}</span>
                      </div>
                      <div class="demo-row">
                        <label>定向系统版本</label><span>{{ row.targetOsVersionType }} - {{ row.targetOsVersion }}</span>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="过滤规则">
                      <div class="demo-row">
                        <label>过滤地区</label><span>{{ row.filterArea }}</span>
                      </div>
                      <div class="demo-row">
                        <label>过滤行业</label><span>{{ row.filterAppIndustry }}</span>
                      </div>
                      <div class="demo-row">
                        <label>过滤APP</label><span>{{ row.filterAppPackage }}</span>
                      </div>
                      <div class="demo-row">
                        <label>过滤设备型号</label><span>{{ row.filterDeviceModel }}</span>
                      </div>
                      <div class="demo-row">
                        <label>过滤链接域名</label><span>{{ row.filterUrlDomain }}</span>
                      </div>
                      <div class="demo-row">
                        <label>过滤网络类型</label><span>{{ row.filterNetwork }}</span>
                      </div>
                      <div class="demo-row">
                        <label>过滤运营商</label><span>{{ row.filterOperator }}</span>
                      </div>
                      <div class="demo-row">
                        <label>过滤空设备</label><span>{{ row.filterEmptyDevice ? '是' : '否' }}</span>
                      </div>
                      <div class="demo-row">
                        <label>过滤无效设备</label><span>{{ row.filterInvalidDevice ? '是' : '否' }}</span>
                      </div>
                      <div class="demo-row">
                        <label>过滤国外IP</label><span>{{ row.filterForeignIp ? '是' : '否' }}</span>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>
                <span>{{ row.name + ' - ' + row.code }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <!-- <div style="display: flex; align-items: center; justify-content: flex-end; margin-bottom: -10px"> -->
        <el-pagination
          style="float: right; margin-bottom: 20px; margin-top: 10px"
          small
          layout="total,prev,pager,next"
          :page-size="listQuery.limit"
          :current-page.sync="currentPageNum"
          :total="total"
          @current-change="handlePageChange"
        />
        <!-- </div> -->
      </el-card>

      <el-card header="已配置预算广告位" :class="hasTagsClass">
        <div>
          <el-table v-loading="ruleTableLoading" :data="dataList" border fit highlight-current-row style="width: 100%">
            <el-table-column align="center" label="序号" min-width="60" type="index" />
            <el-table-column label="预算" prop="code" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.advertName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="APP" prop="code" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.appName }}</span>
                <br />
                <span>{{ row.appCode }}</span>
                <br />
                {{ parseOsType(row.appType) }}
              </template>
            </el-table-column>
            <el-table-column label="广告位" prop="linkman" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.tagName }}</span>
                <br />
                <span>{{ row.tagCode }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类型" prop="tagType" align="center">
              <template slot-scope="{ row }">
                {{ parseType(row.type) }}
              </template>
            </el-table-column>
            <el-table-column label="结算方式" prop="settlementType" align="center">
              <template slot-scope="{ row }">
                {{ parseBidType(row.settlementType) }}
              </template>
            </el-table-column>
            <el-table-column label="参数" align="center">
              <template slot-scope="{ row }">
                <template v-if="selectedInfoShow.quantityLimitType != 1">
                  <span> 优先级：{{ parseParallelPriority(row.parallelPriority) }} </span>
                  <br />
                </template>
                <template v-if="selectedInfoShow.quantityLimitType == 1">
                  <span> 权重：{{ row.randomFlowRatio }} </span>
                  <br />
                </template>
                <template v-if="row.randomQps">
                  <span> QPS：{{ row.randomQps }}次/秒</span>
                  <br />
                </template>
                <span>包名：{{ parsePackageHandleType(row.handleType) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="请求" header-align="center" align="left" min-width="100">
              <template slot-scope="scope">
                <span v-if="scope.row.mediaReqTotal > 0 || scope.row.advertiserReqTotal > 0">
                  总量: {{ scope.row.mediaReqTotal }} -> {{ scope.row.advertiserReqTotal }}
                </span>
                <br v-if="scope.row.mediaReqTotal > 0 || scope.row.advertiserReqTotal > 0" />
                <span v-if="scope.row.mediaReqInvalidTotal > 0" style="color: red">无效: {{ scope.row.mediaReqInvalidTotal }}</span>
                <br v-if="scope.row.mediaReqInvalidTotal > 0" />
                <span v-if="scope.row.mediaRespFailTotal > 0 || scope.row.advertiserReqFailTotal > 0" style="color: red">
                  失败：{{ scope.row.mediaRespFailTotal }} -> {{ scope.row.advertiserReqFailTotal }}
                </span>
                <br v-if="scope.row.mediaRespFailTotal > 0 || scope.row.advertiserReqFailTotal > 0" />
                <span v-if="scope.row.advertiserReqTimeoutTotal > 0" style="color: coral">
                  超时：{{ scope.row.advertiserReqTimeoutTotal }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="填充" align="center" min-width="100">
              <template slot-scope="scope">
                <span v-if="scope.row.advertiserReqTotal > 0">
                  媒体: {{ scope.row.mediaParticipatingTotal }} ({{
                    calculatePercentage(scope.row.mediaParticipatingTotal, scope.row.mediaReqTotal - scope.row.mediaReqInvalidTotal)
                  }})
                </span>
                <br />
                <span v-if="scope.row.mediaReqTotal > 0">
                  预算: {{ scope.row.advertiserParticipatingTotal }} ({{
                    calculatePercentage(
                      scope.row.advertiserParticipatingTotal,
                      scope.row.advertiserReqTotal - scope.row.advertiserReqTimeoutTotal
                    )
                  }})
                </span>
              </template>
            </el-table-column>
            <el-table-column label="竟胜" align="center" min-width="80">
              <template slot-scope="scope">
                <span v-if="scope.row.mediaWinTotal > 0">媒体: {{ scope.row.mediaWinTotal }}</span>
                <br v-if="scope.row.mediaWinTotal > 0" />
                <span v-if="scope.row.advertiserWinTotal > 0">预算: {{ scope.row.advertiserWinTotal }}</span>
              </template>
            </el-table-column>
            <el-table-column label="消耗" align="center" min-width="80">
              <template slot-scope="scope">
                <span v-if="scope.row.mediaAmount > 0">媒体: ¥{{ scope.row.mediaAmount }}元</span>
                <br />
                <span v-if="scope.row.advertiserAmount > 0">预算: ¥{{ scope.row.advertiserAmount }}元</span>
                <br />
                <span v-if="scope.row.mediaAmount > 0 || scope.row.advertiserAmount > 0" style="color: red">
                  盈利: ¥{{ parseFloat((scope.row.advertiserAmount - scope.row.mediaAmount).toFixed(2)) }}元
                </span>
              </template>
            </el-table-column>
            <el-table-column label="上报" align="center" min-width="80">
              <template slot-scope="scope">
                <span v-if="scope.row.eventExposureTotal > 0">曝光: {{ scope.row.eventExposureTotal }}</span>
                <br v-if="scope.row.eventExposureTotal > 0" />
                <span v-if="scope.row.eventClickTotal > 0">点击: {{ scope.row.eventClickTotal }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" min-width="140" fixed="right">
              <template slot-scope="{ row }">
                <el-popconfirm
                  v-if="AuthUtils.hasAuth('STRATEGY_RULE_OPERATOR') && row.state === 0"
                  title="确定要启用吗?"
                  icon="el-icon-info"
                  icon-color="green"
                  @confirm="handState(row, 1)"
                >
                  <el-button slot="reference" type="success" size="mini" title="启动" circle><svg-icon icon-class="start" /></el-button>
                </el-popconfirm>
                <el-popconfirm
                  v-if="AuthUtils.hasAuth('STRATEGY_RULE_OPERATOR') && row.state === 1"
                  title="确定要停用吗?"
                  icon="el-icon-info"
                  icon-color="#E6A23C"
                  @confirm="handState(row, 0)"
                >
                  <el-button slot="reference" type="danger" size="mini" title="停用" circle><svg-icon icon-class="stop" /></el-button>
                </el-popconfirm>
                <el-button
                  v-if="AuthUtils.hasAuth('STRATEGY_RULE_MODIFY')"
                  type="primary"
                  size="mini"
                  icon="el-icon-edit"
                  title="编辑"
                  circle
                  @click="handleModify(row)"
                />
                <el-popconfirm
                  v-if="AuthUtils.hasAuth('STRATEGY_RULE_OPERATOR') && row.state === 0"
                  title="确定要删除吗?"
                  icon="el-icon-info"
                  icon-color="#E6A23C"
                  @confirm="handDelete(row)"
                >
                  <el-button slot="reference" type="danger" size="mini" title="删除" circle><svg-icon icon-class="wrong" /></el-button>
                </el-popconfirm>
                <el-button
                  v-if="AuthUtils.hasAuth('STATISTICS_MEDIA_VIEW') || AuthUtils.hasAuth('STATISTICS_ADVERT_VIEW')"
                  type="primary"
                  size="mini"
                  icon="el-icon-s-data"
                  title="监控"
                  circle
                  @click="handleMonitor(row)"
                />
                <el-button
                  v-if="AuthUtils.hasAuth('TOOL_RTB_LOG')"
                  type="warning"
                  size="mini"
                  title="RTB日志"
                  circle
                  @click="handleRtbLog(row)"
                >
                  <svg-icon icon-class="bug" />
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!--批量新增-->
    <el-drawer
      title="新增策略"
      :show-close="true"
      size="50%"
      :visible.sync="addOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" status-icon label-width="120px" size="mini" class="ruleForm">
        <el-form-item v-if="selectedInfoShow.quantityLimitType != 1" label="优先级" prop="parallelPriority">
          <el-select v-model="ruleForm.parallelPriority" placeholder="请选择" style="width: 100%">
            <el-option v-for="item in parallelPriorityList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="selectedInfoShow.quantityLimitType == 1" label="权重" prop="randomFlowRatio">
          <el-input v-model="ruleForm.randomFlowRatio" />
        </el-form-item>
        <el-form-item label="流量QPS" prop="randomQps">
          <el-input v-model="ruleForm.randomQps">
            <template slot="append">次/秒</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="bidType === 2" label="固定底价" prop="fixedPrice">
          <el-input v-model="ruleForm.fixedPrice" type="number">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>

        <el-form-item v-if="showBidPriceType" label="底价" prop="rtbToSharingBasePriceType">
          <el-radio-group v-model="ruleForm.rtbToSharingBasePriceType">
            <el-radio-button :label="1">隐藏</el-radio-button>
            <el-radio-button :label="2">透传</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="showBidPriceType" label="出价">
          <!-- 竞价跑分成模式-->
          <el-radio-group v-model="ruleForm.rtbToSharingBidPriceType">
            <el-radio-button v-for="item in bidPriceTypeList" :key="item.id" :label="item.id">{{ item.name }} </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="showBidPriceType && ruleForm.rtbToSharingBidPriceType === 1" label="出价涨幅">
          <el-input v-model="ruleForm.rtbToSharingBidRisesRatio" type="number" placeholder="媒体底价按照此配置涨幅后出价">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="showBidPriceType && ruleForm.rtbToSharingBidPriceType === 1" label="最高出价">
          <el-input v-model="ruleForm.rtbToSharingMaxPrice" type="number" placeholder="不填写则不限制最高出价">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="showBidPriceType && ruleForm.rtbToSharingBidPriceType === 2" label="固价">
          <el-input v-model="ruleForm.rtbToSharingFixedPrice" type="number">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>

        <el-form-item label="包名处理" prop="handleType">
          <el-radio-group v-model="ruleForm.handleType">
            <el-radio v-for="item in packageHandleTypeList" :key="item.id" :label="item.id">{{ item.name }} </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :disabled="btnClicked" @click="submitForm('ruleForm')">确定</el-button>
          <el-button type="danger" @click="addOpen = false">关闭</el-button>
        </el-form-item>
      </el-form>
    </el-drawer>
    <!-- 修改 -->
    <el-drawer
      title="修改预算规则"
      :show-close="true"
      size="50%"
      :visible.sync="modifyOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <StrategyRuleModify
        v-if="modifyOpen"
        :id.sync="infoId"
        :bid-type="bidType"
        :strategy-id="infoShow.id"
        :quantity-type="infoShow.quantityLimitType"
        :is-update.sync="modifyUpdate"
        @changePageOpen="changeModifyOpen"
      />
    </el-drawer>

    <el-drawer
      title="统计"
      :show-close="true"
      size="50%"
      :visible.sync="monitorOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <TagStatistics
        v-if="monitorOpen"
        :id.sync="infoId"
        :strategy-id.sync="infoShow.id"
        :s-type.sync="sType"
        @changePageOpen="changeMonitorOpen"
      />
    </el-drawer>

    <el-drawer
      title="设置日志条件"
      :show-close="false"
      size="50%"
      :visible.sync="rtbOpen"
      :wrapper-closable="true"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <RtbLogGet v-if="rtbOpen" :strategy-info.sync="strategyRuleInfo" @changePageOpen="changeRtbOpen" />
    </el-drawer>
  </el-container>
</template>
<script>
import { listMediaTag, infoMediaTag } from '@/api/media/mediaTag'
import {
  listOsTypeApi,
  listQuantityLimitTypeApi,
  listBidTypeApi,
  listTagTypeApi,
  listParallelPriorityApi,
  listPackageHandleTypeApi,
  listBidPriceTypeApi,
  listSharingPriceTypeApi
} from '@/api/public/typeinfo'
import { pageStrategyRule, onOffStrategyRule, deleteStrategyRule, batchAddStrategyRule } from '@/api/strategy/strategyRule' // 后台接口
import { listAdvertiser } from '@/api/advertiser/advertiserMain'
import { listAdvertiserApp } from '@/api/advertiser/advertiserApp'
import { pageRuleAdvertiserTag as pageAdvertiserTag } from '@/api/advertiser/advertiserTag'
import { findOfMediaPackageApi } from '@/api/strategy/strategy' // 后台接口
import { listMedia } from '@/api/media/mediaMain'
import { listMediaApp } from '@/api/media/mediaApp'
import StrategyRuleModify from './StrategyRuleModify'
import TagStatistics from '../../TagStatistics'
import RtbLogGet from '../../tool/rtb/RtbLogGet'
import { listMediaPackage } from '@/api/media/mediaPackage'
export default {
  name: 'StrategyRuleConfigPackage',
  components: { StrategyRuleModify, TagStatistics, RtbLogGet },
  data() {
    return {
      hasTagsClass: 'showNoTags_hasTagsClass',
      showNoTags: true,
      infoList: [],
      showBidPriceType: false, // 竞价跑分成模式
      listQuery: {
        name: null,
        advertiserId: null,
        advertiserAppId: null,
        tagType: null,
        start: 0,
        limit: 10,
        settlementType: null
      },
      ruleForm: {
        parallelPriority: null,
        randomFlowRatio: null,
        handleType: null,
        fixedPrice: null,
        randomQps: null,
        rtbToSharingBasePriceType: 1, // 竞价跑分成，底价处理方式，默认因此底价
        rtbToSharingBidPriceType: 1, // 竞价跑分成 默认涨幅
        rtbToSharingMaxPrice: null,
        rtbToSharingBidRisesRatio: null,
        rtbToSharingFixedPrice: null
      },
      rules: {
        randomFlowRatio: [{ required: true, trigger: 'blur', message: '不能为空' }],
        parallelPriority: [{ required: true, trigger: 'blur', message: '不能为空' }],
        handleType: [{ required: true, trigger: 'blur', message: '未选择' }]
      },
      advertiserTags: [],
      dragId: null,
      medias: [],
      mediaApps: [],
      mediaTags: [],
      dataList: [],
      infoShow: {},
      tagType: null,
      types: [],
      osTypeList: [],
      quantityLimitTypeList: [],
      bidTypeList: [],
      strategyId: null,
      parallelPriorityList: [],
      packageHandleTypeList: [],
      bidPriceTypeList: [],
      sharingPriceTypeList: [],
      paginationShow: true,
      totalPage: 0,
      currentPageNum: 0,
      listLoading: false,
      total: 0,
      advertisers: [],
      advertiserApps: [],
      addOpen: false,
      btnClicked: false,
      tagId: null,
      bidType: null,
      advTabLoading: false,
      mediaTabLoading: false,
      infoId: null,
      modifyOpen: false,
      modifyUpdate: false,
      monitorOpen: false,
      sType: 'strategyRule',
      strategyRuleInfo: {},
      rtbOpen: false,
      ruleTableLoading: false,
      mediaPackages: [],
      selectedInfoShow: null
    }
  },
  activated() {
    this.tagId = this.$route.query.id
    if (this.tagId) {
      this.loadMediaInfo()
      this.listQuery.mediaTagId = this.tagId
    }
  },
  created() {
    listQuantityLimitTypeApi().then(response => {
      this.quantityLimitTypeList = response.result
    })
    listOsTypeApi().then(response => {
      this.osTypeList = response.result.filter(item => item.id !== 999)
    })
    listBidTypeApi().then(response => {
      this.bidTypeList = response.result
    })
    listTagTypeApi({}).then(response => {
      this.types = response.result
    })
    listParallelPriorityApi().then(response => {
      this.parallelPriorityList = response.result
    })
    listPackageHandleTypeApi().then(response => {
      this.packageHandleTypeList = response.result
    })
    listBidPriceTypeApi().then(response => {
      this.bidPriceTypeList = response.result
    })
    listSharingPriceTypeApi().then(response => {
      this.sharingPriceTypeList = response.result
    })
    listAdvertiser({ name: null }).then(response => {
      this.advertisers = response.result
    })
    this.tagId = ~~Number(this.$route.query.id)
    this.loadMedias(null)
    this.loadMediaTags(null)
    this.loadAdvTag()
    if (this.tagId) {
      this.loadMediaInfo()
      this.listQuery.mediaTagId = this.tagId
    }
  },
  methods: {
    handleInfoShowSelect(row) {
      this.selectedInfoShow = row
      this.loadStrategyAdvertiser(row.id)
    },
    handlePackageChange() {
      this.mediaPackages = []
      this.listQuery.mediaPackageId = null
      this.loadMediaPackages(null)
    },
    loadMediaPackages(name) {
      if (this.infoShow.mediaId) {
        listMediaPackage({ name: name, mediaId: this.infoShow.mediaId }).then(response => {
          this.mediaPackages = response.result
        })
      }
    },
    loadMedias(name) {
      listMedia({ name: name }).then(response => {
        this.medias = response.result
      })
    },
    changeMedia() {
      this.mediaApps = []
      this.listQuery.mediaAppId = null
      this.loadMediaApps(null)
      this.mediaTags = []
      this.listQuery.mediaTagId = null
    },
    loadMediaApps(name) {
      if (this.listQuery.mediaId) {
        listMediaApp({ name: name, mediaId: this.listQuery.mediaId }).then(response => {
          this.mediaApps = response.result
        })
      }
    },
    changeMediaApp() {
      this.mediaTags = []
      this.listQuery.mediaTagId = null
      this.loadMediaTags(null)
    },
    loadMediaTags(name) {
      listMediaTag({ name: name, mediaAppId: this.listQuery.mediaAppId }).then(response => {
        this.mediaTags = response.result
      })
    },
    parseQuantityLimitType(id) {
      const value = this.quantityLimitTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseBidType(id) {
      const value = this.bidTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseType(type) {
      for (const item of this.types) {
        if (item.id === type) {
          return item.name
        }
      }
      return '未知'
    },
    parseOsType(id) {
      const value = this.osTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    changeShowNoTags() {
      this.showNoTags = !this.showNoTags
      if (!this.showNoTags) {
        this.hasTagsClass = 'notShowNoTags_hasTagsClass'
      } else {
        this.hasTagsClass = 'showNoTags_hasTagsClass'
      }
    },
    remove() {
      this.listQuery.mediaAppId = null
      this.listQuery.mediaId = null
      this.listQuery.mediaTagId = null
      this.infoList = []
      this.dataList = []
      this.tagId = null
      this.infoShow.id = null
      this.loadAdvTag()
    },
    loadMediaInfo() {
      if (!this.tagId) {
        return
      }
      this.mediaTabLoading = true
      findOfMediaPackageApi({ id: this.tagId })
        .then(resp => {
          if (!resp.success) {
            this.$message.error(resp.message)
            this.mediaTabLoading = false
            return
          } else {
            this.infoShow = resp.result
            infoMediaTag({ id: this.tagId })
              .then(item => {
                this.bidType = item.result.bidType
                this.listQuery.tagType = item.result.type
                this.listQuery.settlementType = item.result.bidType
                this.infoList = [{ ...resp.result, ...item.result }]
                this.loadStrategyAdvertiser()
                this.mediaTabLoading = false
                this.loadAdvTag(1)
              })
              .catch(() => {
                this.mediaTabLoading = false
              })
          }
        })
        .catch(() => {
          this.mediaTabLoading = false
        })
    },
    handleTagChange(v) {
      if (!v) {
        this.listQuery.tagType = null
        this.listQuery.settlementType = null
        this.infoShow = {}
        this.infoList = []
        this.dataList = []
        this.loadAdvTag(1)
        return
      }
      this.tagId = v
      this.loadMediaInfo()
    },
    selectAll() {
      this.loadAdvTag(1)
      this.loadMediaInfo()
      this.loadStrategyAdvertiser()
    },
    loadStrategyAdvertiser() {
      if (this.infoShow.id === null || this.infoShow.id === undefined) {
        return
      }
      this.ruleTableLoading = true
      pageStrategyRule({ strategyId: this.infoShow.id })
        .then(item => {
          this.dataList = item.result
          this.ruleTableLoading = false
        })
        .catch(() => {
          this.ruleTableLoading = false
        })
    },
    loadAdvTag(c) {
      this.currentPageNum = Math.max(~~c, 1)
      this.listLoading = true
      this.advertiserTags = []
      this.advTabLoading = true
      this.listQuery.start = (this.currentPageNum - 1) * this.listQuery.limit
      pageAdvertiserTag(this.listQuery)
        .then(response => {
          // 分成可以选择竞价
          this.total = response.result.totalCount
          this.totalPage = response.result.totalPage
          this.currentPageNum = response.result.currentPageNum
          this.listLoading = false
          this.paginationShow = true
          this.advertiserTags = response.result.list
          this.advTabLoading = false
        })
        .catch(() => {
          this.advTabLoading = false
        })
    },
    parsePackageHandleType(id) {
      const value = this.packageHandleTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseParallelPriority(id) {
      const value = this.parallelPriorityList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    fixedPriceShow(row) {
      return (row.settlementType === 1 && row.bidPriceType === 2) || (row.settlementType === 2 && row.sharingPriceType === 2)
    },
    parseBidPriceType(id) {
      const value = this.bidPriceTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    parseSharingPriceTypeList(id) {
      const value = this.sharingPriceTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    handlerAdvChang(v) {
      this.listQuery.advertiserAppId = ''
      this.advertiserApps = []
      if (v) {
        this.loadAdvApp(null)
      }
      this.loadAdvTag(1)
    },
    loadAdvApp(name) {
      if (this.listQuery.advertiserId) {
        listAdvertiserApp({ name: name, advertiserId: this.listQuery.advertiserId }).then(response => {
          this.advertiserApps = response.result
        })
      }
    },
    loadAdvertiser(name) {
      listAdvertiser({ name: name }).then(response => {
        this.advertisers = response.result
      })
    },
    handlePageChange(v) {
      this.listQuery.start = ~~((v - 1) * this.listQuery.limit)
      this.loadAdvTag(v)
    },
    removeAdv() {
      this.listQuery.advertiserId = null
      this.listQuery.advertiserAppId = null
      this.listQuery.name = null
      this.loadAdvTag()
    },
    handleAdd() {
      if (!this.infoShow.id) {
        this.$message.error('未选择策略-媒体广告位')
        return
      }
      const selectedRows = this.$refs.advTagTable.selection
      if (!selectedRows.length) {
        this.$message.error('未选择预算广告位')
        return
      }
      this.showBidPriceType = false
      selectedRows.forEach(item => {
        if (this.bidType === 1 && item.settlementType === 2) {
          this.showBidPriceType = true
        }
      })
      this.clearRuleFrom()
      this.addOpen = true
    },
    submitForm(formName) {
      const tag = this.infoList[0]
      const selectedRows = this.$refs.advTagTable.selection
      const advs = []
      selectedRows.forEach(item => {
        const adv = this.advertiserTags.find(a => a.id === item.id)
        if (adv && adv.settlementType === 1) {
          advs.push(adv)
        }
      })
      if (advs.length && tag.bidType === 2 && !this.ruleForm.fixedPrice) {
        this.$message.error('分成媒体广告位需要竞价预算广告位设置固定底价')
        return
      }
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.ruleForm.advTagFixedPrice = this.ruleForm.fixedPrice * 100.0
          this.ruleForm.advIds = selectedRows.map(item => item.id)
          this.ruleForm.strategyId = this.infoShow.id
          this.btnClicked = true
          batchAddStrategyRule(this.ruleForm)
            .then(res => {
              this.btnClicked = false
              this.addOpen = false
              this.loadMediaInfo()
            })
            .catch(() => {
              this.btnClicked = false
            })
        }
      })
    },
    clearRuleFrom() {
      this.ruleForm.parallelPriority = null
      this.ruleForm.randomFlowRatio = null
      this.ruleForm.handleType = null
      this.ruleForm.fixedPrice = null
      this.ruleForm.randomQps = null
      this.ruleForm.rtbToSharingBasePriceType = 1 // 竞价跑分成，底价处理方式，默认因此底价
      this.ruleForm.rtbToSharingBidPriceType = 1 // 竞价跑分成 默认涨幅
      this.ruleForm.rtbToSharingMaxPrice = null
      this.ruleForm.rtbToSharingBidRisesRatio = null
      this.ruleForm.rtbToSharingFixedPrice = null
    },
    handState(row, state) {
      // 停用
      onOffStrategyRule({ id: row.id, state: state })
        .then(response => {
          this.$message({
            type: 'success',
            message: '成功!'
          })
          this.loadStrategyAdvertiser()
        })
        .catch(() => {})
    },
    handDelete(row) {
      deleteStrategyRule({ id: row.id })
        .then(response => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.loadMediaInfo()
        })
        .catch(() => {})
    },
    handleModify(row) {
      this.infoId = row.id
      this.modifyOpen = true
    },
    changeModifyOpen(flag) {
      this.modifyOpen = flag
      if (this.modifyUpdate) {
        this.loadMediaInfo()
      }
    },
    handleMonitor(row) {
      this.infoId = row.id
      this.monitorOpen = true
    },
    changeMonitorOpen(open) {
      this.monitorOpen = open
    },
    handleRtbLog(strategyAdvRow) {
      this.strategyRuleInfo.mediaId = this.infoList[0].mediaId
      this.strategyRuleInfo.mediaAppId = this.infoList[0].mediaAppId
      this.strategyRuleInfo.mediaTagId = this.infoList[0].id
      this.strategyRuleInfo.advertiserId = strategyAdvRow.advertiserId
      this.strategyRuleInfo.advertiserAppId = strategyAdvRow.advertiserAppId
      this.strategyRuleInfo.advertiserTagId = strategyAdvRow.advertiserTagId
      this.rtbOpen = true
    },
    changeRtbOpen(open) {
      this.rtbOpen = open
    },
    // 计算百分比
    calculatePercentage(numerator, denominator) {
      if (denominator === 0 || isNaN(numerator) || isNaN(denominator)) {
        return '0.00%'
      }
      return ((numerator / denominator) * 100).toFixed(2) + '%'
    }
  }
}
</script>
<style scoped>
.draggable-item {
  width: 100px;
  height: 50px;
  background: #f0f0f0;
  border: 1px solid #ccc;
  padding: 10px;
  cursor: move;
}

.drop-zone {
  width: 200px;
  height: 100px;
  margin-top: 20px;
  background: #e9e9e9;
  border: 2px dashed #999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.car-box {
  margin: 5px;
  cursor: pointer;
}
.demo-row {
  display: flex;
  flex-direction: row;
  padding: 5px 20px 0px 20px;
  align-items: center;
}
.demo-row label {
  width: 100px;
  color: #99a9bf;
}
.demo-row span {
  flex: 1;
  height: 24px;
  line-height: 24px;
}
.pagination {
  margin-top: 0px;
  padding: 16px 0px;
}
.showNoTags_hasTagsClass {
  width: 69.5%;
  margin-left: 0.5%;
  margin-right: 0%;
}
.notShowNoTags_hasTagsClass {
  width: 100%;
}
</style>
